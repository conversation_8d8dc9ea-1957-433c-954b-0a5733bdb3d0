import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import {
  Educational,
  EducationalServiceProxy,
} from '../../../../shared/service-proxies/service-proxies';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AuthService } from '../../../../shared/services/auth.service';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '../../user/user-request/confirmation-dialog/confirmation-dialog.component';
import { EditorJsService } from '../../../shared/services/editor-js.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-educational-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, MatSnackBarModule, MatDialogModule],
  templateUrl: './educational-detail.component.html',
  styleUrls: ['./educational-detail.component.css']
})
export class EducationalDetailComponent implements OnInit {
  // Base URL for API calls
  baseUrl: string = getRemoteServiceBaseUrl();

  // Content data
  educational: Educational | null = null;
  educationalId: string | null = null;
  isLoading: boolean = true;
  isAdmin: boolean = false;
  loadError: string | null = null;

  // EditorJS content display
  parsedContent: SafeHtml | null = null;
  contentBlocks: any[] = [];

  constructor(
    private _educationalService: EducationalServiceProxy,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private dialog: MatDialog,
    private editorJsService: EditorJsService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.isAdmin = this.authService.hasRole('Admin');

    // Get ID from route
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.educationalId = params['id'];
        this.loadEducationalContent(this.educationalId!);
      } else {
        this.loadError = 'No content ID provided';
        this.isLoading = false;
      }
    });
  }

  loadEducationalContent(id: string) {
    this.isLoading = true;
    this._educationalService.getEducationalById(id).subscribe({
      next: (result) => {
        this.educational = result;

        // Process image URL if present
        if (this.educational && this.educational.featuredImg) {
          if (
            !this.educational.featuredImg.includes('://') &&
            !this.educational.featuredImg.startsWith(this.baseUrl)
          ) {
            this.educational.featuredImg = `${this.baseUrl}/api/File/Getfile/${this.educational.featuredImg}`;
          }
        }

        // Parse EditorJS content
        if (this.educational && this.educational.content) {
          this.parseEditorJSContent(this.educational.content);
        }

        this.isLoading = false;
      },
      error: (error) => {
        this.loadError =
          'Failed to load educational content. Please try again.';
        this.isLoading = false;
        console.error('Error loading educational content:', error);
      },
    });
  }

  formatDate(date: any): string {
    if (!date) return 'N/A';
    const dateObj = new Date(date.toString());
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  goToList(): void {
    this.router.navigate(['/educational/list']);
  }

  editEducational(): void {
    if (!this.educational) return;
    this.router.navigate(['/educational/edit', this.educational.id]);
  }

  deleteEducational(): void {
    if (!this.educational) return;

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        type: 'danger',
        title: 'Delete Educational Content',
        message:
          'Are you sure you want to delete this educational content? This action cannot be undone.',
        itemName: this.educational.content?.substring(0, 50) + '...',
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.educational) {
        this._educationalService
          .deleteEducational(this.educational.id)
          .subscribe({
            next: () => {
              this.snackBar.open(
                'Educational content deleted successfully',
                'Close',
                {
                  duration: 3000,
                  horizontalPosition: 'end',
                  verticalPosition: 'bottom',
                }
              );
              this.router.navigate(['/educational/list']);
            },
            error: (error) => {
              this.snackBar.open(
                'Failed to delete educational content',
                'Close',
                {
                  duration: 3000,
                  horizontalPosition: 'end',
                  verticalPosition: 'bottom',
                }
              );
              console.error('Error deleting educational content:', error);
            },
          });
      }
    });
  }

  getHTMLContent(): SafeHtml | string {
    if (this.parsedContent) {
      return this.parsedContent;
    }

    // Fallback: try to render blocks individually if parsedContent is not available
    if (this.contentBlocks && this.contentBlocks.length > 0) {
      console.log('Using fallback rendering for blocks:', this.contentBlocks);
      const htmlParts = this.contentBlocks.map(block => {
        const renderedBlock = this.renderBlock(block);
        return typeof renderedBlock === 'string' ? renderedBlock : renderedBlock.toString();
      });
      const combinedHtml = htmlParts.join('');
      return this.sanitizer.bypassSecurityTrustHtml(combinedHtml);
    }

    return this.educational?.content || '';
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    const placeholder = document.createElement('div');
    placeholder.className =
      'w-full h-64 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center';
    placeholder.innerHTML = `
      <div class="text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <p class="mt-2 text-gray-500 dark:text-gray-400">Image not available</p>
      </div>
    `;
    img.style.display = 'none';
    img.parentElement?.appendChild(placeholder);
  }

  getDefaultImage(): string {
    return 'assets/images/default-educational.jpg';
  }

  parseEditorJSContent(content: string): void {
    try {
      const editorData = JSON.parse(content);
      console.log('Parsed EditorJS data:', editorData); // Debug log
      if (editorData?.blocks?.length) {
        this.contentBlocks = editorData.blocks;
        console.log('Content blocks:', this.contentBlocks); // Debug log
        const htmlContent = this.editorJsService.convertToHtml(editorData);
        console.log('Generated HTML:', htmlContent); // Debug log
        this.parsedContent = this.sanitizer.bypassSecurityTrustHtml(htmlContent);
      } else {
        console.log('No blocks found, treating as plain HTML');
        this.parsedContent = this.sanitizer.bypassSecurityTrustHtml(content);
        this.contentBlocks = [];
      }
    } catch (error) {
      console.error('Error parsing EditorJS content:', error);
      console.log('Original content:', content); // Debug log
      this.parsedContent = this.sanitizer.bypassSecurityTrustHtml(content);
      this.contentBlocks = [];
    }
  }

  getFormattedContent(): SafeHtml | null {
    return this.parsedContent;
  }

  getContentBlocks(): any[] {
    return this.contentBlocks;
  }

  renderBlock(block: any): SafeHtml {
    let html = '';
    console.log('Rendering block:', block); // Debug log

    switch (block.type) {
      case 'header':
        const level = block.data.level || 2;
        const textSize = level === 1 ? '3xl' : level === 2 ? '2xl' : 'xl';
        html = `<h${level} class="text-${textSize} font-bold mb-4 text-gray-800 dark:text-white">${block.data.text}</h${level}>`;
        break;

      case 'paragraph':
        html = `<p class="mb-4 text-gray-700 dark:text-gray-300 leading-relaxed">${block.data.text}</p>`;
        break;

      case 'list':
        console.log('Rendering list block:', block.data); // Debug log
        if (block.data && block.data.items && Array.isArray(block.data.items)) {
          const tag = block.data.style === 'ordered' ? 'ol' : 'ul';
          const listClass = block.data.style === 'ordered' ? 'list-decimal' : 'list-disc';
          const items = block.data.items.map((item: any) => {
            const text = typeof item === 'string' ? item : (item.text || item.content || '');
            return `<li class="mb-1 text-gray-700 dark:text-gray-300">${text}</li>`;
          }).join('');
          html = `<${tag} class="${listClass} pl-6 mb-4 text-gray-700 dark:text-gray-300">${items}</${tag}>`;
        } else {
          console.warn('Invalid list data:', block.data);
          html = `<div class="mb-4 p-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded">Invalid list data</div>`;
        }
        break;

      case 'checklist':
      case 'checkpoint':
        console.log('Rendering checklist/checkpoint block:', block.data);
        if (block.data && block.data.items && Array.isArray(block.data.items)) {
          const items = block.data.items.map((item: any) => {
            const text = typeof item === 'string' ? item : (item.text || item.content || '');
            const checked = typeof item === 'object' ? (item.checked || false) : false;
            const checkIcon = checked ?
              '<svg class="w-4 h-4 text-green-500 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
              '<svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle></svg>';

            return `<li class="flex items-start mb-2">
              <span class="flex-shrink-0 mr-3 mt-0.5">${checkIcon}</span>
              <span class="text-gray-700 dark:text-gray-300 ${checked ? 'line-through text-gray-500 dark:text-gray-500' : ''}">${text}</span>
            </li>`;
          }).join('');
          html = `<ul class="mb-4 space-y-2">${items}</ul>`;
        } else {
          console.warn('Invalid checklist/checkpoint data:', block.data);
          html = `<div class="mb-4 p-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded">Invalid checklist data</div>`;
        }
        break;

      case 'image':
        html = this.renderImageBlock(block);
        break;

      case 'quote':
        html = `<blockquote class="border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic text-gray-600 dark:text-gray-300 mb-4">${block.data.text}</blockquote>`;
        break;

      case 'code':
        html = `<pre class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4 overflow-x-auto"><code class="text-gray-800 dark:text-gray-200 font-mono text-sm">${block.data.code}</code></pre>`;
        break;

      case 'delimiter':
      case 'divider':
        html = `<hr class="my-6 border-gray-200 dark:border-gray-700" />`;
        break;

      default:
        console.warn('Unsupported block type:', block.type);
        html = `<div class="mb-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700">Unsupported content type: ${block.type}</div>`;
    }

    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  private renderImageBlock(block: any): string {
    const caption = block.data.caption
      ? `<figcaption class="mt-2 text-sm text-center text-gray-600 dark:text-gray-400">${block.data.caption}</figcaption>`
      : '';

    return `
      <div class="mb-6">
        <img src="${block.data.file?.url || block.data.url}"
             alt="${block.data.caption || ''}"
             class="rounded-lg w-full h-auto max-w-full"
             onerror="this.onerror=null;this.src='${this.getDefaultImage()}'">
        ${caption}
      </div>
    `;
  }
}
