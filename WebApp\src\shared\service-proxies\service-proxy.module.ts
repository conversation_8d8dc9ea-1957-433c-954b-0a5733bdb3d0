import { NgModule } from '@angular/core';
import * as ApiServiceProxies from './service-proxies';

@NgModule({
  providers: [
    ApiServiceProxies.WoundRequestServiceProxy,
    ApiServiceProxies.FileServiceProxy,
    ApiServiceProxies.CommentServiceProxy,
    ApiServiceProxies.UserAccountServiceProxy,
    ApiServiceProxies.WorkspaceServiceProxy,
    ApiServiceProxies.AssignWorkspaceServiceProxy,
    ApiServiceProxies.DocsServiceProxy,
    ApiServiceProxies.TaskServiceProxy,
    ApiServiceProxies.ProjectCategoryServiceProxy,
    ApiServiceProxies.ProjectMemoryServiceProxy,
    ApiServiceProxies.AuthServiceProxy,
    ApiServiceProxies.MedicineServiceProxy,
    ApiServiceProxies.DailyRoutineServiceProxy,

    ApiServiceProxies.OrderServiceProxy,
    ApiServiceProxies.EducationalServiceProxy,
    ApiServiceProxies.AppointmentMessageServiceProxy,
    ApiServiceProxies.PatientInfoServiceProxy,
    ApiServiceProxies.DoctorInfoServiceProxy,
    ApiServiceProxies.RequestFilesServiceProxy,
    ApiServiceProxies.DoctorPatientChatServiceProxy,
    ApiServiceProxies.MedicineRoutineContollerServiceProxy







  ],
})
export class ServiceProxyModule { }
