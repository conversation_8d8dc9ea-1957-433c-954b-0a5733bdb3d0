import { Component, OnInit, HostListener, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import {
  AuthService,
  AuthUser,
} from '../../../../shared/services/auth.service';
import { AppDownloadBannerComponent } from '../../components/app-download-banner/app-download-banner.component';
import { ThemeToggleComponent } from '../../components/theme-toggle/theme-toggle.component';
import { ThemeService } from '../../services/theme.service';
import { GlobalMessageNotificationService } from '../../../services/global-message-notification.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [CommonModule, RouterModule, AppDownloadBannerComponent, ThemeToggleComponent],
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.css'],
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  currentUser: AuthUser | null = null;
  showUserDropdown = false;
  isMobileSidebarOpen = false;
  isMobileView = false;
  totalUnreadCount = 0;
  private destroy$ = new Subject<void>();

  constructor(
    public authService: AuthService,
    private router: Router,
    public themeService: ThemeService,
    private globalMessageService: GlobalMessageNotificationService
  ) { }

  ngOnInit(): void {
    // Subscribe to user observable from the AuthService
    this.authService.user$.subscribe((user: AuthUser | null) => {
      this.currentUser = user;
      console.log(this.currentUser);
    });

    // Check if user is already loaded
    const currentUser = this.authService.getUser();
    if (currentUser) {
      this.currentUser = currentUser;
    }

    // Check initial screen size
    this.checkMobileView();

    // Subscribe to unread message counts
    this.globalMessageService.totalUnreadCount$
      .pipe(takeUntil(this.destroy$))
      .subscribe(count => {
        this.totalUnreadCount = count;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any): void {
    this.checkMobileView();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;

    // Close user dropdown
    const dropdown = target.closest('.relative');
    if (!dropdown) {
      this.showUserDropdown = false;
    }

    // Close mobile sidebar when clicking outside
    if (this.isMobileView && this.isMobileSidebarOpen) {
      const sidebar = target.closest('.mobile-sidebar');
      const hamburger = target.closest('.hamburger-menu');
      if (!sidebar && !hamburger) {
        this.closeMobileSidebar();
      }
    }
  }

  private checkMobileView(): void {
    if (typeof window !== 'undefined') {
      this.isMobileView = window.innerWidth < 1024; // lg breakpoint
      if (!this.isMobileView) {
        this.isMobileSidebarOpen = false;
      }
    }
  }

  toggleMobileSidebar(): void {
    this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
  }

  closeMobileSidebar(): void {
    this.isMobileSidebarOpen = false;
  }

  onMobileNavClick(): void {
    // Close sidebar when navigation link is clicked on mobile
    if (this.isMobileView) {
      this.closeMobileSidebar();
    }
  }

  toggleUserDropdown(): void {
    this.showUserDropdown = !this.showUserDropdown;
  }

  closeUserDropdown(): void {
    this.showUserDropdown = false;
  }

  logout(): void {
    console.log('Logging out user...');
    this.closeUserDropdown();

    // Call the AuthService logout method
    this.authService.logout();

    // Note: The AuthService.logout() already contains navigation to login,
    // but we'll add it here as an additional safety measure
    setTimeout(() => {
      // Give the logout method a moment to process
      if (!this.authService.isUserLoggedIn) {
        console.log('User logged out successfully, redirecting to login');
        this.router.navigate(['/auth/login']);
      }
    }, 100);
  }

  getUserInitials(): string {
    if (!this.currentUser) return 'U';
    const first = this.currentUser.firstName?.charAt(0) || '';
    // Access safely using bracket notation for dynamic properties
    const name = this.currentUser['name'] || '';
    const firstChar = name.charAt(0) || '';

    return (first || firstChar).toUpperCase() || 'U';
  }

  getUserRole(): string {
    if (
      !this.currentUser ||
      !this.currentUser.role ||
      this.currentUser.role.length === 0
    ) {
      return 'User';
    }
    return this.currentUser.role[0] || 'User';
  }

  canViewUsers(): boolean {
    // Check if user has Admin, User, or Manager role
    const userRoles = this.authService.getUserRoles();
    return userRoles.some((role) =>
      ['Admin', 'User', 'Manager'].includes(role)
    );
  }

  canManageUsers(): boolean {
    // Only Admin can manage users
    return this.authService.isAdmin();
  }

  canManageRoles(): boolean {
    // Only Admin can manage roles
    return this.authService.isAdmin();
  }

  /**
   * Check if user only has the User role and no other roles
   * This is used to determine if we should show only the download app message
   */
  isOnlyUserRole(): boolean {
    return this.authService.isOnlyUserRole();
  }

  /**
   * Check if the current user has the Doctor role
   * Used to determine which menu items to show in the sidebar
   */
  hasDoctorRole(): boolean {
    return this.authService.hasDoctorRole();
  }

  /**
   * Check if the current user can manage patients
   * Only Admin and Doctor roles can manage patients
   */
  canManagePatients(): boolean {
    return this.authService.isAdmin() || this.authService.hasDoctorRole();
  }

  /**
   * Check if the current user can manage educational content
   * Only Admin and Doctor roles can create/edit educational content
   */
  canManageEducationalContent(): boolean {
    return this.authService.isAdmin() || this.authService.hasDoctorRole();
  }

  /**
   * Check if the current user can manage medicine orders
   * Only Admin and Doctor roles can manage medicine orders
   */
  canManageMedicineOrders(): boolean {
    return this.authService.isAdmin() || this.authService.hasDoctorRole();
  }

  /**
   * Check if the current user can view appointments
   * All authenticated users can view appointments, but with different scopes
   */
  canViewAppointments(): boolean {
    return this.authService.isUserLoggedIn;
  }

  /**
   * Check if the current user can view educational resources
   * All authenticated users can view educational resources
   */
  canViewEducationalResources(): boolean {
    return this.authService.isUserLoggedIn;
  }

  /**
   * Check if the current user should see the profile option
   * Only doctors should see the profile option
   */
  canAccessProfile(): boolean {
    return this.authService.hasDoctorRole();
  }

  /**
   * Get total unread message count
   */
  getTotalUnreadCount(): number {
    return this.totalUnreadCount;
  }

  /**
   * Check if we're currently on the chat route
   */
  isChatRoute(): boolean {
    return this.router.url.includes('/chat');
  }
}
