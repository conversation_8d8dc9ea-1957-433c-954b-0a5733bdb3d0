<div *ngIf="showBanner" class="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-blue-100 shadow-lg z-50 transition-all duration-300 ease-in-out transform translate-y-0">
  <div class="max-w-7xl mx-auto">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <div class="flex-shrink-0">
          <div class="bg-blue-50 p-3 rounded-full">
            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
        </div>
        <div class="flex-1 min-w-0">
          <h3 class="text-md font-medium text-gray-900">
            Get our Patient Care mobile app
          </h3>
          <p class="text-sm text-gray-500">
            Stay connected with your care provider and manage your health on the go.
          </p>
        </div>
      </div>
      <div class="flex space-x-3">
        <a href="https://play.google.com/store" target="_blank" rel="noopener noreferrer"
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 20.5V3.5C3 2.91 3.34 2.39 3.84 2.15L13.69 12L3.84 21.85C3.34 21.6 3 21.09 3 20.5ZM16.81 15.12L6.05 21.34L14.54 12.85L16.81 15.12ZM20.16 12.34C20.5 12.55 20.75 12.92 20.75 13.34C20.75 13.76 20.5 14.12 20.17 14.33L17.57 15.85L14.99 13.27L17.57 10.69L20.16 12.34ZM6.05 2.66L16.81 8.88L14.54 11.15L6.05 2.66Z" />
          </svg>
          Google Play
        </a>
        <a href="https://www.apple.com/app-store/" target="_blank" rel="noopener noreferrer"
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
          <svg class="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14.94,5.19A4.38,4.38,0,0,0,16,2,4.44,4.44,0,0,0,13,3.52,4.17,4.17,0,0,0,12,6.61,3.69,3.69,0,0,0,14.94,5.19Zm2.52,7.44A4.51,4.51,0,0,1,19,9.5a4.41,4.41,0,0,0-2.88-2.53,4.3,4.3,0,0,0-1.64,3.31,4.3,4.3,0,0,0,3,2.35ZM18.4,20.1l-1.1-3.2a.4.4,0,0,0-.38-.3H7.08a.4.4,0,0,0-.38.3L5.6,20.1a.39.39,0,0,0,.36.5H18a.39.39,0,0,0,.36-.5ZM12,6.9c-.16,0-4.2.11-4.2,5.3s4.2,5.2,4.2,5.2,4.2,0,4.2-5.2S12.16,6.9,12,6.9Z"/>
          </svg>
          App Store
        </a>
        <button
          (click)="dismissBanner()"
          class="inline-flex items-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100">
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
          <span class="sr-only">Dismiss</span>
        </button>
      </div>
    </div>
  </div>
</div>
