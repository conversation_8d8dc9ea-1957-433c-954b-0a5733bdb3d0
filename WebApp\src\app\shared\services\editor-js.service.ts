import { Injectable } from '@angular/core';

/**
 * A service to handle EditorJS operations
 * Including converting between HTML and JSON formats
 */
@Injectable({
  providedIn: 'root'
})
export class EditorJsService {

  /**
   * Convert EditorJS JSON to HTML with Tailwind CSS styling
   */
  convertToHtml(editorData: any): string {
    if (!editorData || !editorData.blocks || !Array.isArray(editorData.blocks)) {
      return '';
    }

    console.log('EditorJS convertToHtml - Full data:', JSON.stringify(editorData, null, 2));

    return editorData.blocks.map((block: any) => {
      console.log(`Processing block type: ${block.type}`, block.data);

      switch (block.type) {
        case 'header':
          const level = block.data.level || 2;
          let headerClass = '';
          switch (level) {
            case 1:
              headerClass = 'text-4xl font-bold mb-6 text-gray-900 leading-tight';
              break;
            case 2:
              headerClass = 'text-3xl font-bold mb-5 text-gray-900 leading-tight';
              break;
            case 3:
              headerClass = 'text-2xl font-bold mb-4 text-gray-900 leading-tight';
              break;
            case 4:
              headerClass = 'text-xl font-bold mb-4 text-gray-900 leading-tight';
              break;
            case 5:
              headerClass = 'text-lg font-bold mb-3 text-gray-900 leading-tight';
              break;
            case 6:
              headerClass = 'text-base font-bold mb-3 text-gray-900 leading-tight';
              break;
            default:
              headerClass = 'text-2xl font-bold mb-4 text-gray-900 leading-tight';
          }
          return `<h${level} class="${headerClass}">${block.data.text}</h${level}>`;

        case 'paragraph':
          return `<p class="mb-4 text-gray-700 leading-relaxed text-base">${block.data.text}</p>`;

        case 'list':
          console.log('EditorJS Service - Processing list:', block.data); // Debug log
          if (!block.data || !block.data.items || !Array.isArray(block.data.items)) {
            console.warn('Invalid list data structure:', block.data);
            return `<div class="mb-4 p-2 bg-yellow-100 text-yellow-800 rounded">Invalid list data</div>`;
          }

          const tag = block.data.style === 'ordered' ? 'ol' : 'ul';
          const listClass = block.data.style === 'ordered' ? 'list-decimal' : 'list-disc';
          const items = block.data.items.map((item: any) => {
            let text = '';

            // Handle different item formats
            if (typeof item === 'string') {
              text = item;
            } else if (item && typeof item === 'object') {
              // Try different possible properties
              text = item.text || item.content || item.value || item.label || '';

              // If still empty, try to stringify the object and extract meaningful content
              if (!text && item) {
                console.warn('Unknown list item format:', item);
                text = JSON.stringify(item);
              }
            } else {
              text = String(item || '');
            }

            return `<li class="mb-2 text-gray-700">${text}</li>`;
          }).join('');
          return `<${tag} class="${listClass} pl-6 mb-4 space-y-1">${items}</${tag}>`;

        case 'image':
          const caption = block.data.caption ?
            `<figcaption>${block.data.caption}</figcaption>` : '';
          const imageClass = this.getImageClass(block.data);
          const imageAlt = block.data.caption || 'Article image';

          return `<figure class="article-figure ${imageClass}">
            <img src="${block.data.file?.url}"
                 alt="${imageAlt}"
                 loading="lazy"
                 onerror="this.closest('figure').innerHTML='<div class=\\'image-error\\'><svg viewBox=\\'0 0 24 24\\'><path fill=\\'currentColor\\' d=\\'M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19M19,19H5V5H19V19Z\\'></path></svg><p>Image could not be loaded</p></div>';">
            ${caption}
          </figure>`;

        case 'quote':
          return `<blockquote class="border-l-4 border-blue-500 pl-4 py-2 mb-4 italic text-gray-700 bg-blue-50 rounded-r-lg">
            <p class="mb-2">${block.data.text}</p>
            ${block.data.caption ? `<cite class="text-sm text-gray-600">— ${block.data.caption}</cite>` : ''}
          </blockquote>`;

        case 'code':
          return `<pre class="bg-gray-900 text-green-400 p-4 rounded-lg mb-4 overflow-x-auto">
            <code class="text-sm font-mono">${block.data.code}</code>
          </pre>`;

        case 'delimiter':
          return `<div class="flex justify-center my-8">
            <div class="flex space-x-2">
              <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
            </div>
          </div>`;

        case 'checklist':
        case 'checkpoint':
          console.log('EditorJS Service - Processing checklist/checkpoint:', block.data);
          if (block.data && block.data.items && Array.isArray(block.data.items)) {
            const items = block.data.items.map((item: any) => {
              let text = '';
              let checked = false;

              // Handle different item formats
              if (typeof item === 'string') {
                text = item;
                checked = false;
              } else if (item && typeof item === 'object') {
                // Try different possible properties for text
                text = item.text || item.content || item.value || item.label || '';
                // Try different possible properties for checked state
                checked = item.checked || item.completed || item.done || false;

                // If still empty text, try to stringify the object and extract meaningful content
                if (!text && item) {
                  console.warn('Unknown checklist item format:', item);
                  text = JSON.stringify(item);
                }
              } else {
                text = String(item || '');
                checked = false;
              }

              const checkIcon = checked ?
                '<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
                '<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"></circle></svg>';

              return `<li class="flex items-start mb-2">
                <span class="flex-shrink-0 mr-3 mt-0.5">${checkIcon}</span>
                <span class="text-gray-700 ${checked ? 'line-through text-gray-500' : ''}">${text}</span>
              </li>`;
            }).join('');
            return `<ul class="mb-4 space-y-2">${items}</ul>`;
          }
          return '';

        case 'linkEmbed':
          if (block.data && block.data.url) {
            if (block.data.embedType === 'link') {
              const domain = new URL(block.data.url).hostname.replace('www.', '');
              return `<div class="mb-6 p-5 border border-gray-200 dark:border-gray-700 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-sm hover:shadow-md transition-all duration-200">
                <div class="flex items-center mb-3">
                  <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-sm">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-gray-900 dark:text-gray-100 font-semibold text-base mb-1">${block.data.title || 'External Link'}</h4>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">${domain}</p>
                  </div>
                </div>
                <a href="${block.data.url}" target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-white hover:text-white font-medium text-sm bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 px-4 py-2 rounded-lg transition-all duration-200 no-underline">
                  Visit Link
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ml-2">
                    <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </a>
              </div>`;
            } else {
              return `<div class="mb-6 rounded-xl overflow-hidden shadow-lg bg-white dark:bg-gray-800">
                <div class="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 px-4 py-3 flex items-center justify-between border-b border-gray-200 dark:border-gray-600">
                  <div class="flex items-center">
                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-gray-700 dark:text-gray-300 font-medium text-sm">Embedded Content</span>
                  </div>
                  <span class="text-gray-500 dark:text-gray-400 text-xs font-mono bg-white dark:bg-gray-900 px-2 py-1 rounded border border-gray-200 dark:border-gray-600">${new URL(block.data.url).hostname}</span>
                </div>
                <iframe
                  src="${block.data.url}"
                  width="100%"
                  height="400"
                  frameborder="0"
                  class="w-full block border-none bg-white dark:bg-gray-900"
                  allowfullscreen>
                </iframe>
              </div>`;
            }
          }
          return '';

        case 'youtube':
          if (block.data && block.data.url) {
            const videoId = this.extractYouTubeId(block.data.url);
            if (videoId) {
              return `<div class="mb-6 rounded-xl overflow-hidden shadow-lg bg-white dark:bg-gray-800">
                <div class="bg-gradient-to-r from-red-600 to-red-700 px-4 py-3 flex items-center justify-between">
                  <div class="flex items-center">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2">
                      <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z" fill="white"/>
                      <polygon points="9.75,15.02 15.5,11.75 9.75,8.48" fill="#dc2626"/>
                    </svg>
                    <span class="text-white font-semibold text-sm">YouTube Video</span>
                  </div>
                  <span class="text-red-100 text-xs font-mono bg-red-800 bg-opacity-30 px-2 py-1 rounded">youtube.com</span>
                </div>
                <div class="relative w-full bg-gray-50 dark:bg-gray-900" style="padding-bottom: 56.25%; height: 0;">
                  <iframe
                    src="https://www.youtube.com/embed/${videoId}"
                    class="absolute top-0 left-0 w-full h-full"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen>
                  </iframe>
                </div>
                ${block.data.title ? `<div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                  <p class="text-gray-700 dark:text-gray-300 font-medium text-sm mb-0">${block.data.title}</p>
                </div>` : ''}
              </div>`;
            }
          }
          return '';

        case 'embed':
          if (block.data && (block.data.service || block.data.embed || block.data.source)) {
            const service = block.data.service || 'generic';
            const embedUrl = block.data.embed || block.data.source || '';
            const { width, height, caption } = block.data;

            // Handle YouTube embeds
            if (service === 'youtube' || embedUrl.includes('youtube.com') || embedUrl.includes('youtu.be')) {
              return `<div class="mb-4">
                <div class="relative w-full" style="padding-bottom: 56.25%; height: 0;">
                  <iframe
                    src="${embedUrl}"
                    class="absolute top-0 left-0 w-full h-full rounded-lg"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen>
                  </iframe>
                </div>
                ${caption ? `<p class="text-sm text-gray-600 mt-2 text-center italic">${caption}</p>` : ''}
              </div>`;
            }

            // Handle other embed types generically
            return `<div class="mb-4">
              <div class="relative w-full">
                <iframe
                  src="${embedUrl}"
                  width="${width || '100%'}"
                  height="${height || '400'}"
                  class="w-full rounded-lg border border-gray-300"
                  frameborder="0"
                  allowfullscreen>
                </iframe>
              </div>
              ${caption ? `<p class="text-sm text-gray-600 mt-2 text-center italic">${caption}</p>` : ''}
            </div>`;
          }
          return '';

        case 'table':
          if (block.data.content && Array.isArray(block.data.content)) {
            const rows = block.data.content.map((row: string[], index: number) => {
              const cells = row.map(cell =>
                index === 0 && block.data.withHeadings ?
                  `<th class="px-4 py-2 bg-gray-100 font-semibold text-left border border-gray-300">${cell}</th>` :
                  `<td class="px-4 py-2 border border-gray-300">${cell}</td>`
              ).join('');
              return `<tr>${cells}</tr>`;
            }).join('');
            return `<div class="overflow-x-auto mb-4">
              <table class="w-full border-collapse border border-gray-300 rounded-lg">
                ${rows}
              </table>
            </div>`;
          }
          return '';

        default:
          console.warn(`Unsupported EditorJS block type: ${block.type}`);
          return `<div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p class="text-yellow-800 text-sm">
              <strong>Unsupported content type:</strong> ${block.type}
            </p>
          </div>`;
      }
    }).join('');
  }

  /**
   * Parse HTML content to EditorJS JSON format
   * This is a simple implementation that handles basic HTML tags
   */
  convertFromHtml(html: string): any {
    const blocks = [];
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    const elements = doc.body.children;

    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      const tagName = element.tagName.toLowerCase();

      switch (tagName) {
        case 'h1':
        case 'h2':
        case 'h3':
        case 'h4':
        case 'h5':
        case 'h6':
          blocks.push({
            id: this.generateId(),
            type: 'header',
            data: {
              text: element.innerHTML,
              level: parseInt(tagName.substring(1), 10)
            }
          });
          break;

        case 'p':
          blocks.push({
            id: this.generateId(),
            type: 'paragraph',
            data: {
              text: element.innerHTML
            }
          });
          break;

        case 'ul':
        case 'ol':
          const items = Array.from(element.querySelectorAll('li')).map(li => li.innerHTML);
          blocks.push({
            id: this.generateId(),
            type: 'list',
            data: {
              style: tagName === 'ul' ? 'unordered' : 'ordered',
              items: items
            }
          });
          break;

        case 'img':
          blocks.push({
            id: this.generateId(),
            type: 'image',
            data: {
              file: {
                url: element.getAttribute('src') || ''
              },
              caption: element.getAttribute('alt') || '',
              withBorder: false,
              stretched: false,
              withBackground: false
            }
          });
          break;

        case 'iframe':
          const src = element.getAttribute('src') || '';
          let service = 'unknown';

          // Detect YouTube embeds
          if (src.includes('youtube.com') || src.includes('youtu.be')) {
            service = 'youtube';
          }

          blocks.push({
            id: this.generateId(),
            type: 'embed',
            data: {
              service: service,
              source: src,
              embed: src,
              width: element.getAttribute('width') || undefined,
              height: element.getAttribute('height') || undefined,
              caption: ''
            }
          });
          break;
      }
    }

    return {
      time: Date.now(),
      blocks,
      version: '2.25.0'
    };
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  /**
   * Determine the appropriate CSS class for an image based on its properties
   */
  private getImageClass(imageData: any): string {
    if (!imageData) return '';

    const classes = [];

    // Check for stretched images (full width)
    if (imageData.stretched) {
      classes.push('image-wide');
    }

    // Check for background styling
    if (imageData.withBackground) {
      classes.push('image-with-background');
    }

    // Check for border
    if (imageData.withBorder) {
      classes.push('image-with-border');
    }

    return classes.join(' ');
  }

  /**
   * Extract YouTube video ID from various URL formats including YouTube Shorts
   */
  private extractYouTubeId(url: string): string | null {
    const patterns = [
      // Regular YouTube URLs
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/,
      // YouTube Shorts URLs
      /(?:youtube\.com\/shorts\/|www\.youtube\.com\/shorts\/)([^&\n?#]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return null;
  }
}
