<div
  class="max-w-7xl mx-auto p-3 sm:p-4 lg:p-6 min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">

  <!-- Only show profile for doctors -->
  <div *ngIf="shouldShowProfile(); else noProfileMessage">
    <!-- Header -->
    <div
      class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-4 sm:mb-6 transition-all duration-300">
      <div
        class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-sm border border-blue-100 dark:border-gray-600 p-4 sm:p-6">
        <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
          Doctor Profile
        </h1>
        <p class="text-gray-600 dark:text-gray-300 text-sm sm:text-base mt-1 sm:mt-2">
          Manage your doctor information and professional details
        </p>
      </div>
    </div>

    <!-- Mobile Stack Layout / Desktop Grid Layout -->
    <div class="space-y-4 sm:space-y-6 lg:grid lg:grid-cols-3 lg:gap-6 lg:space-y-0">
      <!-- Profile Picture Section -->
      <div class="lg:col-span-1 space-y-4 sm:space-y-6">
        <!-- Profile Picture Card -->
        <div
          class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-all duration-300">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 sm:mb-6">Profile Picture</h2>

          <div class="flex flex-col items-center">
            <div class="relative mb-4 sm:mb-6">
              <!-- Profile Preview -->
              <div
                class="w-24 h-24 sm:w-32 sm:h-32 lg:w-36 lg:h-36 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full overflow-hidden flex items-center justify-center text-white text-xl sm:text-2xl lg:text-3xl font-bold shadow-lg relative">
                <ng-container *ngIf="previewUrl; else initialsBlock">
                  <img [src]="previewUrl" alt="Profile" class="object-cover w-full h-full" />
                </ng-container>
                <ng-template #initialsBlock>{{ getInitials() }}</ng-template>

                <!-- Upload Progress Overlay -->
                <div *ngIf="isUploadingImage"
                  class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <div class="text-white text-center">
                    <svg class="animate-spin h-8 w-8 mx-auto mb-2" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                      </path>
                    </svg>
                    <span class="text-sm">Uploading...</span>
                  </div>
                </div>
              </div>

              <!-- Upload Button - Only in edit mode -->
              <button type="button" *ngIf="isEditMode && !isUploadingImage"
                class="absolute -bottom-1 -right-1 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-full p-2 sm:p-2.5 lg:p-3 shadow-lg hover:shadow-xl transition-all duration-200 touch-manipulation"
                (click)="fileInput.click()" title="Select Profile Picture">
                <svg class="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>

              <!-- Image Selected Indicator -->
              <div *ngIf="hasImageChanged && !isUploadingImage"
                class="absolute -top-2 -right-2 bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center shadow-md">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>

              <!-- Hidden File Input -->
              <input #fileInput type="file" accept="image/*" class="hidden" (change)="onFileSelected($event)" />
            </div>

            <!-- User Info -->
            <div class="text-center px-2">
              <h3 class="text-base sm:text-lg lg:text-xl font-semibold text-gray-900 dark:text-white mb-1 break-words">
                {{ getDisplayName() }}
              </h3>
              <p class="text-gray-500 dark:text-gray-400 text-sm sm:text-base mb-3 break-all">{{ currentUser?.email }}
              </p>

              <!-- Role Badges -->
              <div class="flex flex-wrap justify-center gap-2 mb-3">
                <span *ngFor="let role of currentUser?.role"
                  class="inline-flex items-center px-2.5 py-1 sm:px-3 sm:py-1 rounded-full text-xs sm:text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  {{ role }}
                </span>
              </div>

              <!-- Image Selection Status - Only in edit mode -->
              <div *ngIf="isEditMode && hasImageChanged" class="mt-2 mb-3">
                <div
                  class="inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 bg-green-50 border border-green-200 rounded-full dark:bg-green-900/20 dark:text-green-400 dark:border-green-800">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span>Image ready to upload</span>
                </div>
              </div>

              <div>
                <span
                  class="inline-flex items-center px-2.5 py-1 sm:px-3 sm:py-1 rounded-full text-xs sm:text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                  <span class="w-2 h-2 rounded-full mr-2 bg-green-400"></span>
                  Active
                </span>
              </div>
            </div>

            <!-- Profile Action Buttons -->
            <div class="w-full mt-4 sm:mt-6 pt-4 sm:pt-6 border-t border-gray-200 dark:border-gray-700">
              <div class="space-y-2">
                <button type="button" *ngIf="!isEditMode" (click)="toggleEditMode()"
                  class="w-full inline-flex items-center justify-center px-4 py-2.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 rounded-lg transition-colors touch-manipulation min-h-[44px]">
                  <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                    </path>
                  </svg>
                  {{ hasProfileData() ? 'Update Profile' : 'Create Profile' }}
                </button>

                <button type="button" *ngIf="isEditMode" (click)="cancelEdit()"
                  class="w-full inline-flex items-center justify-center px-4 py-2.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg transition-colors touch-manipulation min-h-[44px]">
                  <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                  </svg>
                  Cancel Edit
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Account Information Card -->
        <div
          class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-all duration-300">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Account Information</h2>
          <div class="space-y-3 sm:space-y-2">
            <div
              class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
              <div class="flex items-center min-w-0 flex-1">
                <svg class="w-5 h-5 text-gray-400 dark:text-gray-500 mr-3 flex-shrink-0" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <div class="min-w-0 flex-1">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Member Since</label>
                  <p class="text-gray-900 dark:text-gray-100 text-sm truncate">{{
                    formatDate(getCurrentProfileData()?.createdDate?.toString()) }}</p>
                </div>
              </div>
            </div>
            <div
              class="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
              <div class="flex items-center min-w-0 flex-1">
                <svg class="w-5 h-5 text-gray-400 dark:text-gray-500 mr-3 flex-shrink-0" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="min-w-0 flex-1">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Updated</label>
                  <p class="text-gray-900 dark:text-gray-100 text-sm truncate">{{
                    formatDate(getCurrentProfileData()?.updatedDate?.toString()) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Content Section -->
      <div class="lg:col-span-2">
        <div
          class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 lg:p-8 transition-all duration-300">

          <!-- Profile View Mode -->
          <div *ngIf="!isEditMode">
            <div class="mb-6 sm:mb-8">
              <h2 class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Professional Information
              </h2>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Your professional profile and credentials
              </p>
            </div>

            <!-- Loading State -->
            <div *ngIf="isLoading" class="flex items-center justify-center py-12">
              <div class="text-center">
                <svg class="animate-spin h-8 w-8 text-blue-600 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                <p class="text-gray-600 dark:text-gray-400">Loading profile...</p>
              </div>
            </div>

            <!-- No Profile Data -->
            <div *ngIf="!isLoading && !hasProfileData()" class="text-center py-12">
              <svg class="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Complete Your Profile</h3>
              <p class="text-gray-600 dark:text-gray-400 mb-6">Add your professional information to get started</p>
              <button (click)="toggleEditMode()"
                class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                  </path>
                </svg>
                Create Profile
              </button>
            </div>

            <!-- Profile Data Display -->
            <div *ngIf="!isLoading && hasProfileData()" class="space-y-6">
              <!-- Doctor Profile -->
              <div *ngIf="isDoctor() && doctorInfo">
                <!-- Basic Info Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name</label>
                      <p class="text-gray-900 dark:text-white text-base">{{ doctorInfo.fullName || 'Not provided' }}
                      </p>
                    </div>
                    <div>
                      <label
                        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Specialization</label>
                      <p class="text-gray-900 dark:text-white text-base">{{ doctorInfo.specialization || 'Not provided'
                        }}</p>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">License
                        Number</label>
                      <p class="text-gray-900 dark:text-white text-base">{{ doctorInfo.licenseNumber || 'Not provided'
                        }}</p>
                    </div>
                  </div>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone
                        Number</label>
                      <p class="text-gray-900 dark:text-white text-base">{{ doctorInfo.phoneNumber || 'Not provided' }}
                      </p>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Years of
                        Experience</label>
                      <p class="text-gray-900 dark:text-white text-base">{{ doctorInfo.yearsOfExperience || 0 }} years
                      </p>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email
                        Address</label>
                      <p class="text-gray-900 dark:text-white text-base">{{ currentUser?.email }}</p>
                    </div>
                  </div>
                </div>

                <!-- Address -->
                <div *ngIf="doctorInfo?.address">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address</label>
                  <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <p class="text-gray-900 dark:text-white">{{ doctorInfo.address }}</p>
                  </div>
                </div>

                <!-- Education -->
                <div *ngIf="doctorInfo?.education">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Education</label>
                  <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <p class="text-gray-900 dark:text-white whitespace-pre-wrap">{{ doctorInfo.education }}</p>
                  </div>
                </div>

                <!-- Certifications -->
                <div *ngIf="doctorInfo?.certifications">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Certifications</label>
                  <div
                    class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <p class="text-blue-800 dark:text-blue-200 whitespace-pre-wrap">{{ doctorInfo.certifications }}</p>
                  </div>
                </div>

                <!-- Bio -->
                <div *ngIf="doctorInfo?.bio">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Professional
                    Bio</label>
                  <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <p class="text-gray-900 dark:text-white whitespace-pre-wrap">{{ doctorInfo.bio }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Profile Edit Mode -->
          <div *ngIf="isEditMode">
            <div class="mb-6 sm:mb-8">
              <h2 class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-2">{{ hasProfileData() ?
                'Update Profile' : 'Create Profile' }}</h2>
              <p class="text-sm text-gray-600 dark:text-gray-400">{{ hasProfileData() ? 'Update your profile
                information' : 'Complete your profile by adding your information' }} </p>
            </div>

            <form [formGroup]="profileForm" (ngSubmit)="onSubmit()" class="space-y-4 sm:space-y-6">
              <!-- Basic Information -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- First Name -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    First Name <span class="text-red-500 dark:text-red-400">*</span>
                  </label>
                  <input formControlName="firstName" type="text" placeholder="Enter your first name"
                    [class.border-red-500]="getFieldError('firstName')"
                    class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm" />
                  <div *ngIf="getFieldError('firstName')" class="text-sm text-red-600 dark:text-red-400 mt-2">{{
                    getFieldError('firstName') }}</div>
                </div>

                <!-- Last Name -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Last Name <span class="text-red-500 dark:text-red-400">*</span>
                  </label>
                  <input formControlName="lastName" type="text" placeholder="Enter your last name"
                    [class.border-red-500]="getFieldError('lastName')"
                    class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm" />
                  <div *ngIf="getFieldError('lastName')" class="text-sm text-red-600 dark:text-red-400 mt-2">{{
                    getFieldError('lastName') }}</div>
                </div>
              </div>

              <!-- Email (Read-only) -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address</label>
                <input formControlName="email" type="email" readonly
                  class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white text-base sm:text-sm cursor-not-allowed" />
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Contact support to change your email address
                </p>
              </div>

              <!-- Doctor-specific fields -->
              <div *ngIf="isDoctor()">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Specialization -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Specialization <span class="text-red-500 dark:text-red-400">*</span>
                    </label>
                    <input formControlName="specialization" type="text" placeholder="e.g., Cardiology, Pediatrics"
                      [class.border-red-500]="getFieldError('specialization')"
                      class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm" />
                    <div *ngIf="getFieldError('specialization')" class="text-sm text-red-600 dark:text-red-400 mt-2">{{
                      getFieldError('specialization') }}</div>
                  </div>

                  <!-- License Number -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      License Number <span class="text-red-500 dark:text-red-400">*</span>
                    </label>
                    <input formControlName="licenseNumber" type="text" placeholder="Enter your medical license number"
                      [class.border-red-500]="getFieldError('licenseNumber')"
                      class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm" />
                    <div *ngIf="getFieldError('licenseNumber')" class="text-sm text-red-600 dark:text-red-400 mt-2">{{
                      getFieldError('licenseNumber') }}</div>
                  </div>

                  <!-- Phone Number -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone Number</label>
                    <input formControlName="phoneNumber" type="tel" placeholder="Enter your phone number"
                      class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm" />
                  </div>

                  <!-- Years of Experience -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Years of
                      Experience</label>
                    <input formControlName="yearsOfExperience" type="number" min="0" placeholder="0"
                      class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm" />
                  </div>
                </div>

                <!-- Address -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address</label>
                  <textarea formControlName="address" rows="3" placeholder="Enter your address"
                    class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm resize-vertical"></textarea>
                </div>

                <!-- Education -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Education</label>
                  <textarea formControlName="education" rows="3" placeholder="Enter your educational background"
                    class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm resize-vertical"></textarea>
                </div>

                <!-- Certifications -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Certifications</label>
                  <textarea formControlName="certifications" rows="3" placeholder="Enter your certifications"
                    class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm resize-vertical"></textarea>
                </div>

                <!-- Bio -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Professional
                    Bio</label>
                  <textarea formControlName="bio" rows="4"
                    placeholder="Tell us about yourself and your professional experience"
                    class="w-full px-3 py-3 sm:py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors text-base sm:text-sm resize-vertical"></textarea>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="flex flex-col sm:flex-row gap-3 pt-6">
                <button type="submit"
                  [disabled]="!profileForm.valid || (!profileForm.dirty && !hasImageChanged) || isSaving"
                  class="flex-1 inline-flex items-center justify-center px-4 py-3 sm:py-2.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px]">
                  <svg *ngIf="isSaving" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" fill="none"
                    viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                  <span *ngIf="!isSaving">{{ hasProfileData() ? 'Save Changes' : 'Create Profile' }}</span>
                  <span *ngIf="isSaving && isUploadingImage">Uploading Image...</span>
                  <span *ngIf="isSaving && !isUploadingImage">Saving Profile...</span>
                </button>

                <button type="button" (click)="cancelEdit()"
                  class="flex-1 sm:flex-none inline-flex items-center justify-center px-4 py-3 sm:py-2.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-lg transition-colors min-h-[44px]">
                  Cancel
                </button>
              </div>
            </form>
          </div>

          <!-- Success/Error Messages -->
          <div *ngIf="successMessage"
            class="mt-6 p-4 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg">
            <div class="flex items-start">
              <svg class="h-5 w-5 text-green-500 dark:text-green-400 mt-0.5 mr-3 flex-shrink-0" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <p class="text-sm font-medium text-green-700 dark:text-green-300">{{ successMessage }}</p>
            </div>
          </div>

          <div *ngIf="errorMessage"
            class="mt-6 p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
            <div class="flex items-start">
              <svg class="h-5 w-5 text-red-500 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <p class="text-sm font-medium text-red-700 dark:text-red-300">{{ errorMessage }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message for admin-only users -->
  <ng-template #noProfileMessage>
    <div
      class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center">
      <div class="max-w-md mx-auto">
        <div
          class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-4">
          <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Profile Not Available</h3>
        <p class="text-gray-500 dark:text-gray-400">
          Profile management is only available for doctor accounts. As an admin, you have access to system management
          features.
        </p>
      </div>
    </div>
  </ng-template>
</div>
