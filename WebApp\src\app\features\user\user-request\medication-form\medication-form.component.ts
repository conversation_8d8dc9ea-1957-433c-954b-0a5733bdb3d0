import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { OrderServiceProxy } from '../../../../../shared/service-proxies/service-proxies';

interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  timing: string;
  description: string;
}

@Component({
  selector: 'app-medication-form',
  templateUrl: './medication-form.component.html',
  styleUrls: ['./medication-form.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule
  ]
})
export class MedicationFormComponent implements OnInit {
  medication: Medication = {
    name: '',
    dosage: '',
    frequency: '',
    timing: '',
    description: ''
  };
  isEditMode = false;
  dialogTitle = 'Add Medication';
  // Dosage options for select dropdown
  MedicineData: any[] = [];
  patientEmail: string = ''; // Default patient email - should be passed from parent component

  // Timing options for select dropdown
  timingOptions = [
    'Before meals',
    'After meals',
    'With food',
    'On empty stomach',
    'Before sleep',
    'Morning',
    'Afternoon',
    'Evening',
    'As needed'
  ];

  // Frequency options for select dropdown
  frequencyOptions = [
    'Once daily',
    'Twice daily',
    'Three times daily',
    'Every 4 hours',
    'Every 6 hours',
    'Every 8 hours',
    'Every 12 hours',
    'Weekly',
    'As needed'
  ];
  constructor(
    public dialogRef: MatDialogRef<MedicationFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      medication?: Medication,
      patientEmail?: string,
    },
    private orderService: OrderServiceProxy
  ) { }

  ngOnInit(): void {
    console.log('Dialog data received:', this.data);

    if (this.data && this.data.medication) {
      this.isEditMode = true;
      this.dialogTitle = 'Update Medication';
      this.medication = { ...this.data.medication };
    }

    // Use patient email from data if provided, otherwise use default
    if (this.data && this.data.patientEmail) {
      this.patientEmail = this.data.patientEmail;
    }

    console.log('Patient Email after assignment:', this.patientEmail);

    // Load patient medications
    this.LoadPatientMedication();
  }

  LoadPatientMedication() {
    console.log('Loading medications for patient:', this.patientEmail);

    if (!this.patientEmail || this.patientEmail.trim() === '') {
      console.warn('No patient email provided, using fallback medicines');
    }

    this.orderService.getMedicineByPatienteEmail(this.patientEmail).subscribe(
      (result) => {
        console.log('Patient orders loaded successfully:', result);

        // Clear the existing data
        this.MedicineData = [];

        // Extract unique medicine names from all orders
        const uniqueMedicineNames = new Set<string>();

        result.forEach((order: any) => {
          if (order && order.orderItems && Array.isArray(order.orderItems)) {
            order.orderItems.forEach((item: any) => {
              if (item.medicineName && item.medicineName.trim()) {
                uniqueMedicineNames.add(item.medicineName.trim());
              }
            });
          }
        });

        // Convert Set to Array and sort alphabetically
        this.MedicineData = Array.from(uniqueMedicineNames).sort();

        console.log('Extracted medicine names:', this.MedicineData);


      },
      (error) => {
        console.error('Error loading patient medications:', error);
        // Provide fallback medicines on error
      }
    );

  }

  onSubmit(): void {
    this.dialogRef.close(this.medication);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
