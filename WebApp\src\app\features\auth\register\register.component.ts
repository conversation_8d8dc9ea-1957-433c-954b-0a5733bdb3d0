import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { ButtonComponent } from '../../../shared/components/button/button.component';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { AuthServiceProxy, RegisterDto, UserAccountServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { AuthService } from '../../../../shared/services/auth.service';

// User registration interface
export interface RegisterUser {
  name: string;
  email: string;
  password: string;
  role: string;
  skills: string;
}

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ButtonComponent,
    ServiceProxyModule
  ],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css']
})
export class RegisterComponent implements OnInit {

  // Initialize with required fields only as per specification
  registerData: RegisterUser = {
    name: '',
    email: '',
    password: '',
    role: '',
    skills: ''
  };

  isLoading = false;
  errorMessage = '';
  successMessage = '';

  constructor(
    private authService: AuthService,
    private router: Router,
    private _authServiceProxcy: AuthServiceProxy
  ) { }

  ngOnInit(): void {
    // Nothing to initialize
  }

  onSubmit(): void {
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    // Create RegisterDto from form data
    const registerDto = new RegisterDto();
    registerDto.name = this.registerData.name;
    registerDto.email = this.registerData.email;
    registerDto.password = this.registerData.password;
    registerDto.role = "User";
    registerDto.skills = this.registerData.skills;

    // Use real API for registration
    this._authServiceProxcy.register(registerDto).subscribe({
      next: (res) => {
        console.log('Registration successful');
        this.isLoading = false;
        this.successMessage = 'Account created successfully! You can now sign in.';

        // Navigate to login page after registration with a short delay to show the success message
        setTimeout(() => {
          this.router.navigate(['/auth/login']);
        }, 2000);
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Registration error:', error);

        // Extract error message from the response or use default
        if (error?.error?.message) {
          this.errorMessage = error.error.message;
        } else if (typeof error?.error === 'string') {
          this.errorMessage = error.error;
        } else if (error?.message) {
          this.errorMessage = error.message;
        } else {
          this.errorMessage = 'Registration failed. Please try again.';
        }
      }
    });
  }
}
