import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { FileParameter, FileServiceProxy, RequestFilesServiceProxy } from '../../../../../shared/service-proxies/service-proxies';
import { getRemoteServiceBaseUrl } from '../../../../app.config';
import { AuthService } from '../../../../../shared/services/auth.service';

interface FileAttachment {
  name: string;
  type: string;
  size: string;
  uploadDate: Date;
  url?: string;
  file?: File;
}

@Component({
  selector: 'app-attachment-upload',
  templateUrl: './attachment-upload.component.html',
  styleUrls: ['./attachment-upload.component.css'],
  standalone: true, imports: [
    CommonModule,
    MatDialogModule
  ]
})
export class AttachmentUploadComponent implements OnInit {
  dialogTitle = 'Upload Document';
  fileSelected = false;
  fileName: string = '';
  fileSize: string = '';
  fileType: string = '';
  isUploading: boolean = false;


  // List of allowed file types
  allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/jpg',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  selectedFile?: File; constructor(
    public dialogRef: MatDialogRef<AttachmentUploadComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _fileService: FileServiceProxy,
    private _requestFilesService: RequestFilesServiceProxy,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    // No initialization needed as we're focusing only on file upload
  }
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length) {
      this.selectedFile = input.files[0];
      this.fileName = this.selectedFile.name;

      // Format file size
      const fileSizeInKB = this.selectedFile.size / 1024;
      if (fileSizeInKB < 1024) {
        this.fileSize = `${fileSizeInKB.toFixed(1)} KB`;
      } else {
        const fileSizeInMB = fileSizeInKB / 1024;
        this.fileSize = `${fileSizeInMB.toFixed(2)} MB`;
      }      // Determine file type category
      if (this.selectedFile.type.includes('pdf')) {
        this.fileType = 'Document';
      } else if (this.selectedFile.type.includes('image')) {
        this.fileType = 'Image';
      } else if (this.selectedFile.type.includes('word') || this.selectedFile.type.includes('doc')) {
        this.fileType = 'Document';
      } else {
        this.fileType = 'Document'; // Default to Document for consistency
      }

      this.fileSelected = true;
    }
  }

  isValidFileType(): boolean {
    return this.selectedFile ? this.allowedTypes.includes(this.selectedFile.type) : true;
  }

  onSubmit(): void {
    if (this.selectedFile && this.isValidFileType()) {
      this.isUploading = true;

      // Get request ID from dialog data
      const requestId = this.data?.projectId || this.data?.patientId;

      console.log('Upload dialog data:', this.data);
      console.log('Extracted request ID:', requestId);

      if (!requestId) {
        this.showErrorNotification('Request ID is required for file upload.');
        this.isUploading = false;
        return;
      }

      // Create file parameter for API
      const fileParam: FileParameter = {
        data: this.selectedFile,
        fileName: this.selectedFile.name
      };

      // Get current user's email for patientEmail parameter
      const currentUser = this.authService.getUser();
      const patientEmail = currentUser?.email || '';

      console.log('Upload Debug - Patient Email:', patientEmail);
      console.log('Upload Debug - Request ID:', requestId);
      console.log('Upload Debug - File:', this.selectedFile.name);

      // Use the new RequestFiles service for upload with AI analysis
      this._requestFilesService.uploadForRequest(
        requestId,
        [fileParam],
        patientEmail, // Use current user's email as patientEmail
        'File uploaded from doctor/admin request management'
      ).subscribe({
        next: (uploadResponse) => {
          console.log('File upload successful with AI analysis:', uploadResponse);

          if (uploadResponse.isError) {
            this.showErrorNotification(`Upload failed: ${uploadResponse.message}`);
            this.isUploading = false;
            return;
          }

          // Create attachment object for backward compatibility
          const attachment: FileAttachment = {
            name: this.selectedFile?.name || 'Unknown file',
            type: this.fileType,
            size: this.fileSize,
            uploadDate: new Date(),
            url: '', // Will be populated by the parent component from RequestFiles data
            file: this.selectedFile
          };

          // Log the attachment details for debugging
          console.log('Attachment being returned to parent component:', {
            name: attachment.name,
            type: attachment.type,
            uploadResponse: uploadResponse
          });

          // Close dialog and return the attachment data
          this.dialogRef.close({
            ...attachment,
            uploadResponse: uploadResponse,
            success: true
          });
        },
        error: (error) => {
          console.error('File upload failed:', error);
          this.showErrorNotification('Failed to upload file with AI analysis. Please try again.');
          this.isUploading = false;
        },
        complete: () => {
          this.isUploading = false;
        }
      });
    }
  }


  /**
   * Shows an error notification to the user
   */
  private showErrorNotification(message: string): void {
    // You can implement a proper notification system here
    alert(message);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  removeFile(): void {
    this.selectedFile = undefined;
    this.fileSelected = false;
  }
}
