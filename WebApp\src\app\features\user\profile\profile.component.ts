import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { AuthService, AuthUser } from '../../../../shared/services/auth.service';
import { DoctorInfoServiceProxy, DoctorInfo, ResponseMessage, FileParameter } from '../../../../shared/service-proxies/service-proxies';
import { DateTime } from 'luxon';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.css'
})
export class ProfileComponent implements OnInit {
  profileForm!: FormGroup;
  currentUser: AuthUser | null = null;
  doctorInfo: DoctorInfo | null = null;

  isLoading = false;
  isSaving = false;
  errorMessage = '';
  successMessage = '';
  isEditMode = false;
  previewUrl: string | null = null;
  isUploadingImage = false;
  selectedFile: File | null = null;
  hasImageChanged = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private doctorInfoService: DoctorInfoServiceProxy
  ) { }

  ngOnInit(): void {
    // Subscribe to user changes to handle authentication state properly
    this.authService.user$.subscribe(user => {
      this.currentUser = user;
      if (user) {
        this.initializeForm();
        this.loadProfileData();
      }
    });

    // Also get current user immediately if available
    const currentUser = this.authService.getUser();
    if (currentUser) {
      this.currentUser = currentUser;
      this.initializeForm();
      this.loadProfileData();
    }
  }

  private loadProfileData(): void {
    if (!this.currentUser) return;

    this.isLoading = true;
    this.errorMessage = '';

    // Only load doctor info if user is a doctor
    if (this.isDoctor()) {
      this.loadDoctorInfo();
    } else {
      // For admin-only users, we don't show profile
      this.isLoading = false;
    }
  }

  private loadDoctorInfo(): void {
    if (!this.currentUser) return;

    this.doctorInfoService.getDoctorInfoByEmail(this.currentUser.email).subscribe({
      next: (doctorInfo: DoctorInfo) => {
        this.doctorInfo = doctorInfo;
        this.updateFormWithDoctorInfo(doctorInfo);
        // Set preview URL if profile image exists
        if (doctorInfo.profileImageUrl) {
          this.previewUrl = doctorInfo.profileImageUrl;
        }
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading doctor info:', error);
        this.isLoading = false;
        // If doctor info doesn't exist, initialize empty form for creation
        if (error.status === 404) {
          this.doctorInfo = null;
          this.initializeForm();
        } else {
          this.errorMessage = 'Failed to load doctor information.';
        }
      }
    });
  }

  private updateFormWithDoctorInfo(doctorInfo: DoctorInfo): void {
    this.profileForm.patchValue({
      firstName: doctorInfo.fullName?.split(' ')[0] || '',
      lastName: doctorInfo.fullName?.split(' ').slice(1).join(' ') || '',
      email: this.currentUser?.email || '',
      specialization: doctorInfo.specialization || '',
      phoneNumber: doctorInfo.phoneNumber || '',
      licenseNumber: doctorInfo.licenseNumber || '',
      bio: doctorInfo.bio || '',
      yearsOfExperience: doctorInfo.yearsOfExperience || 0,
      education: doctorInfo.education || '',
      certifications: doctorInfo.certifications || '',
      address: doctorInfo.address || ''
    });
  }

  isDoctor(): boolean {
    return this.currentUser?.role?.includes('Doctor') || false;
  }

  isAdmin(): boolean {
    return this.currentUser?.role?.includes('Admin') || false;
  }

  // Only show profile for doctors
  shouldShowProfile(): boolean {
    return this.isDoctor();
  }

  hasProfileData(): boolean {
    return !!(this.doctorInfo?.fullName || this.doctorInfo?.specialization);
  }

  toggleEditMode(): void {
    this.isEditMode = !this.isEditMode;
    this.successMessage = '';
    this.errorMessage = '';

    if (this.isEditMode && this.doctorInfo) {
      this.updateFormWithDoctorInfo(this.doctorInfo);
    }
  }

  cancelEdit(): void {
    this.isEditMode = false;
    this.resetForm();
    this.successMessage = '';
    this.errorMessage = '';

    // Reset image selection
    this.selectedFile = null;
    this.hasImageChanged = false;

    // Reset preview to original image
    if (this.doctorInfo?.profileImageUrl) {
      this.previewUrl = this.doctorInfo.profileImageUrl;
    } else {
      this.previewUrl = null;
    }
  }

  private initializeForm(): void {
    // Only initialize form for doctors
    if (this.isDoctor()) {
      this.profileForm = this.fb.group({
        firstName: [this.currentUser?.firstName || '', [Validators.required, Validators.minLength(2)]],
        lastName: ['', [Validators.required, Validators.minLength(2)]],
        email: [{ value: this.currentUser?.email || '', disabled: true }],
        specialization: ['', [Validators.required]],
        phoneNumber: [''],
        licenseNumber: ['', [Validators.required]],
        bio: [''],
        yearsOfExperience: [0, [Validators.min(0)]],
        education: [''],
        certifications: [''],
        address: ['']
      });
    }
  }

  onSubmit(): void {
    if (this.profileForm.valid && !this.isSaving && this.currentUser) {
      this.isSaving = true;
      this.errorMessage = '';
      this.successMessage = '';

      // If image was changed, upload it first
      if (this.hasImageChanged && this.selectedFile) {
        this.uploadProfileImageThenSave();
      } else {
        // Only handle doctor profile submission
        if (this.isDoctor()) {
          this.saveDoctorProfile();
        }
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private uploadProfileImageThenSave(): void {
    if (!this.currentUser?.email || !this.selectedFile) {
      if (this.isDoctor()) {
        this.saveDoctorProfile();
      }
      return;
    }

    this.isUploadingImage = true;

    // Create FileParameter for service proxy
    const fileParameter: FileParameter = {
      data: this.selectedFile,
      fileName: this.selectedFile.name
    };

    // Use DoctorInfoServiceProxy to upload the file
    this.doctorInfoService.uploadProfileImage(this.currentUser.email, fileParameter).subscribe({
      next: (response: ResponseMessage) => {
        this.isUploadingImage = false;
        if (!response.isError && response.message) {
          // Update preview URL with the uploaded image URL
          this.previewUrl = response.message;

          // Update doctor info with new image URL
          if (this.doctorInfo) {
            this.doctorInfo.profileImageUrl = response.message;
          }

          this.hasImageChanged = false;
          this.selectedFile = null;

          // Now save the profile data with the new image URL
          if (this.isDoctor()) {
            this.saveDoctorProfile();
          }
        } else {
          this.isSaving = false;
          this.errorMessage = response.message || 'Failed to upload profile image';
        }
      },
      error: (error: any) => {
        this.isUploadingImage = false;
        this.isSaving = false;
        this.errorMessage = error.error?.message || 'Failed to upload profile image';
        console.error('Error uploading profile image:', error);
      }
    });
  }

  private saveDoctorProfile(): void {
    const formValue = this.profileForm.value;
    const fullName = `${formValue.firstName} ${formValue.lastName}`.trim();

    const doctorData = new DoctorInfo({
      id: this.doctorInfo?.id || '', // Use existing ID if updating, empty string if creating new
      fullName: fullName,
      email: this.currentUser!.email,
      phoneNumber: formValue.phoneNumber,
      specialization: formValue.specialization,
      licenseNumber: formValue.licenseNumber,
      bio: formValue.bio,
      yearsOfExperience: formValue.yearsOfExperience || 0,
      education: formValue.education,
      certifications: formValue.certifications,
      address: formValue.address,
      profileImageUrl: this.previewUrl || this.doctorInfo?.profileImageUrl,
      dateOfBirth: this.doctorInfo?.dateOfBirth,
      updatedDate: DateTime.now(),
      createdDate: this.doctorInfo?.createdDate || DateTime.now()
    });

    this.doctorInfoService.addOrUpdateDoctorInfo(doctorData).subscribe({
      next: (updatedDoctorInfo: DoctorInfo) => {
        this.isSaving = false;
        this.doctorInfo = updatedDoctorInfo;
        this.successMessage = 'Doctor profile updated successfully!';
        this.isEditMode = false;
        this.profileForm.markAsPristine();

        // Clear success message after 3 seconds
        setTimeout(() => {
          this.successMessage = '';
        }, 3000);
      },
      error: (error: any) => {
        this.isSaving = false;
        this.errorMessage = error.message || 'Failed to update doctor profile. Please try again.';
        console.error('Error updating doctor profile:', error);
      }
    });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.profileForm.controls).forEach(key => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
    });
  }

  resetForm(): void {
    if (this.isDoctor() && this.doctorInfo) {
      this.updateFormWithDoctorInfo(this.doctorInfo);
    }
    this.errorMessage = '';
    this.successMessage = '';
  }

  getInitials(): string {
    if (this.isDoctor() && this.doctorInfo?.fullName) {
      const nameParts = this.doctorInfo.fullName.split(' ');
      const first = nameParts[0]?.charAt(0) || '';
      const last = nameParts[nameParts.length - 1]?.charAt(0) || '';
      return (first + last).toUpperCase();
    }
    if (!this.currentUser) return 'U';
    const first = this.currentUser.firstName?.charAt(0) || '';
    const last = this.currentUser['lastName']?.charAt(0) || '';
    return (first + last).toUpperCase() || 'U';
  }

  getDisplayName(): string {
    if (this.isDoctor() && this.doctorInfo?.fullName) {
      return this.doctorInfo.fullName;
    }
    return this.currentUser?.firstName || this.currentUser?.email || 'User';
  }

  getDisplayEmail(): string {
    return this.currentUser?.email || '';
  }

  isUserActive(): boolean {
    return true; // You can implement logic to determine if user is active
  }

  formatDate(date: string | DateTime | undefined): string {
    if (!date) return 'N/A';
    if (typeof date === 'string') {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
    // Handle DateTime object from Luxon
    return date.toLocaleString(DateTime.DATE_SHORT);
  }

  onFileSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        this.errorMessage = 'Only JPG, JPEG, and PNG files are allowed';
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        this.errorMessage = 'File size must be less than 5MB';
        return;
      }

      // Store the file for later upload
      this.selectedFile = file;
      this.hasImageChanged = true;
      this.profileForm.markAsDirty();

      // Show preview
      const reader = new FileReader();
      reader.onload = () => {
        this.previewUrl = reader.result as string;
        this.successMessage = 'Image selected. Click "Save Changes" to upload.';

        // Clear success message after 3 seconds
        setTimeout(() => {
          this.successMessage = '';
        }, 3000);
      };
      reader.readAsDataURL(file);
    }
  }

  // The uploadProfileImage functionality has been integrated into the form submission process
  // in the uploadProfileImageThenSave method for a better user experience

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      firstName: 'First name',
      lastName: 'Last name',
      username: 'Username'
    };
    return labels[fieldName] || fieldName;
  }

  getCurrentProfileData(): any {
    if (this.isDoctor() && this.doctorInfo) {
      return this.doctorInfo;
    }
    return this.currentUser;
  }

  getFieldError(fieldName: string): string | null {
    const field = this.profileForm.get(fieldName);
    if (field && field.invalid && (field.dirty || field.touched)) {
      if (field.errors?.['required']) {
        return `${this.getFieldLabel(fieldName)} is required.`;
      }
      if (field.errors?.['minlength']) {
        return `${this.getFieldLabel(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters.`;
      }
      if (field.errors?.['min']) {
        return `${this.getFieldLabel(fieldName)} must be a positive number.`;
      }
    }
    return null;
  }
}
