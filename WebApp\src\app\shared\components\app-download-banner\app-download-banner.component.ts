import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../../../shared/services/auth.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-download-banner',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './app-download-banner.component.html',
  styleUrls: ['./app-download-banner.component.css']
})
export class AppDownloadBannerComponent implements OnInit, OnDestroy {
  showBanner = false;
  private userSubscription: Subscription | null = null;

  constructor(private authService: AuthService) {}
  ngOnInit(): void {
    // Subscribe to authentication state changes
    this.userSubscription = this.authService.user$.subscribe(user => {
      // Use the centralized method in AuthService to determine if banner should be shown
      this.showBanner = this.authService.shouldShowAppDownloadBanner();

      // Only show after login, not on every page load
      if (sessionStorage.getItem('just_logged_in') === 'true' && this.showBanner) {
        // Remove the flag after checking it
        sessionStorage.removeItem('just_logged_in');
      }
    });

    // Check if the auth guard flagged this session to show the banner
    if (sessionStorage.getItem('show_app_download_banner') === 'true') {
      // Remove the flag after checking it
      sessionStorage.removeItem('show_app_download_banner');
      // Set the flag that user just logged in
      sessionStorage.setItem('just_logged_in', 'true');
    }
  }
  dismissBanner(): void {
    // Store the user's preference in localStorage
    localStorage.setItem('app_download_banner_dismissed', 'true');
    this.showBanner = false;
  }

  ngOnDestroy(): void {
    // Clean up the subscription to prevent memory leaks
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }
}
