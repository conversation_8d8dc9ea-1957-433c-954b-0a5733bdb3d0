import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading-spinner',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="flex items-center justify-center" [class]="containerClass">
      <div 
        class="animate-spin rounded-full border-4 border-gray-300 border-t-blue-600"
        [style.width.px]="size"
        [style.height.px]="size"
        [style.border-width.px]="borderWidth">
      </div>
      <span *ngIf="text" class="ml-3 text-gray-600 font-medium">{{ text }}</span>
    </div>
  `,
  styles: []
})
export class LoadingSpinnerComponent {
  @Input() size: number = 32;
  @Input() borderWidth: number = 4;
  @Input() text: string = '';
  @Input() containerClass: string = 'p-4';
}
