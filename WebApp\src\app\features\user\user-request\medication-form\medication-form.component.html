<div
  class="dialog-header px-4 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 dark:!from-blue-700 dark:!to-indigo-700 rounded-t-lg transition-colors duration-200">
  <h2 class="dialog-title text-white dark:!text-gray-100 text-lg font-semibold">{{ dialogTitle }}</h2>
</div>

<div
  class="dialog-content bg-white dark:!bg-gray-900 p-6 rounded-b-lg border border-gray-200 dark:!border-gray-700 transition-colors duration-200">
  <form #medicationForm="ngForm" class="medication-form" (ngSubmit)="onSubmit()">
    <div class="form-group mb-4">
      <label for="name" class="form-label text-sm font-medium text-gray-700 dark:!text-gray-300">Medication Name</label>
      <div class="custom-select-container relative"></div>
      <select id="name" name="name" [(ngModel)]="medication.name" #nameField="ngModel" required
        class="custom-select w-full pl-10 pr-10 py-2 border border-gray-300 dark:!border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400 focus:border-blue-500 dark:!focus:border-blue-400 bg-white dark:!bg-gray-800 text-gray-900 dark:!text-gray-100 appearance-none transition-colors duration-200"
        [class.invalid]="nameField.invalid && nameField.touched">
        <option value="" disabled selected>Select medication</option>
        <option *ngFor="let medicineName of MedicineData" [value]="medicineName">{{ medicineName }}</option>
      </select>
      <div class="input-icon absolute inset-y-0 left-0 flex items-center pl-1 pointer-events-none z-10">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="text-gray-400 dark:!text-gray-500">
          <path
            d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z">
          </path>
        </svg>
      </div>
      <div class="select-icon absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="text-gray-400 dark:!text-gray-500">
          <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
      </div>
    </div>
    <div class="error-message text-red-600 dark:!text-red-400 text-xs mt-1"
      *ngIf="nameField.invalid && nameField.touched">
      Medication name is required
    </div>




    <div class="form-group mb-4">
      <label for="dosage" class="form-label text-sm font-medium text-gray-700 dark:!text-gray-300">Dosage</label>
      <div class="input-container relative">
        <input id="dosage" type="text" name="dosage" [(ngModel)]="medication.dosage" #dosageField="ngModel" required
          class="custom-input w-full pl-10 pr-4 py-2 border border-gray-300 dark:!border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400 focus:border-blue-500 dark:!focus:border-blue-400 bg-white dark:!bg-gray-800 text-gray-900 dark:!text-gray-100 placeholder-gray-400 dark:!placeholder-gray-500 transition-colors duration-200"
          placeholder="e.g., 500mg" [class.invalid]="dosageField.invalid && dosageField.touched">
        <div class="input-icon absolute inset-y-0 left-0 flex items-center pl-1">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="text-gray-400 dark:!text-gray-500">
            <path d="m19 11-8-8-8.6 8.6a2 2 0 0 0 0 2.8l5.2 5.2c.8.8 2 .8 2.8 0L19 11Z"></path>
            <path d="m5 2 5 5"></path>
            <path d="M2 13h15"></path>
            <path d="M22 20v2h-2"></path>
            <path d="M20 14v4h4"></path>
          </svg>
        </div>
      </div>
      <div class="error-message text-red-600 dark:!text-red-400 text-xs mt-1"
        *ngIf="dosageField.invalid && dosageField.touched">
        Dosage is required
      </div>
    </div>

    <div class="form-group mb-4">
      <label for="frequency" class="form-label text-sm font-medium text-gray-700 dark:!text-gray-300">Frequency</label>
      <div class="custom-select-container relative">
        <select id="frequency" name="frequency" [(ngModel)]="medication.frequency" #frequencyField="ngModel" required
          class="custom-select w-full pl-10 pr-10 py-2 border border-gray-300 dark:!border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400 focus:border-blue-500 dark:!focus:border-blue-400 bg-white dark:!bg-gray-800 text-gray-900 dark:!text-gray-100 appearance-none transition-colors duration-200"
          [class.invalid]="frequencyField.invalid && frequencyField.touched">
          <option value="" disabled selected>Select frequency</option>
          <option *ngFor="let freq of frequencyOptions" [value]="freq">{{ freq }}</option>
        </select>
        <div class="input-icon absolute inset-y-0 left-0 flex items-center pl-1 pointer-events-none z-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="text-gray-400 dark:!text-gray-500">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12,6 12,12 16,14"></polyline>
          </svg>
        </div>
        <div class="select-icon absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="text-gray-400 dark:!text-gray-500">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </div>
      </div>
      <div class="error-message text-red-600 dark:!text-red-400 text-xs mt-1"
        *ngIf="frequencyField.invalid && frequencyField.touched">
        Frequency is required
      </div>
    </div>

    <div class="form-group mb-4">
      <label for="timing" class="form-label text-sm font-medium text-gray-700 dark:!text-gray-300">Timing</label>
      <div class="custom-select-container relative">
        <select id="timing" name="timing" [(ngModel)]="medication.timing" #timingField="ngModel" required
          class="custom-select w-full pl-10 pr-10 py-2 border border-gray-300 dark:!border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400 focus:border-blue-500 dark:!focus:border-blue-400 bg-white dark:!bg-gray-800 text-gray-900 dark:!text-gray-100 appearance-none transition-colors duration-200"
          [class.invalid]="timingField.invalid && timingField.touched">
          <option value="" disabled selected>Select timing</option>
          <option *ngFor="let time of timingOptions" [value]="time">{{ time }}</option>
        </select>
        <div class="input-icon absolute inset-y-0 left-0 flex items-center pl-1 pointer-events-none z-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="text-gray-400 dark:!text-gray-500">
            <path d="M12 2v4"></path>
            <path d="m16.2 7.8 2.9-2.9"></path>
            <path d="M18 12h4"></path>
            <path d="m16.2 16.2 2.9 2.9"></path>
            <path d="M12 18v4"></path>
            <path d="m4.9 19.1 2.9-2.9"></path>
            <path d="M2 12h4"></path>
            <path d="m4.9 4.9 2.9 2.9"></path>
          </svg>
        </div>
        <div class="select-icon absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="text-gray-400 dark:!text-gray-500">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </div>
      </div>
      <div class="error-message text-red-600 dark:!text-red-400 text-xs mt-1"
        *ngIf="timingField.invalid && timingField.touched">
        Timing is required
      </div>
    </div>

    <div class="form-group mb-4">
      <label for="description" class="form-label text-sm font-medium text-gray-700 dark:!text-gray-300">Description /
        Notes</label>
      <div class="textarea-container">
        <textarea id="description" name="description" [(ngModel)]="medication.description" #descriptionField="ngModel"
          required
          class="custom-textarea w-full p-3 border border-gray-300 dark:!border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400 focus:border-blue-500 dark:!focus:border-blue-400 bg-white dark:!bg-gray-800 text-gray-900 dark:!text-gray-100 placeholder-gray-400 dark:!placeholder-gray-500 resize-y transition-colors duration-200"
          rows="3" placeholder="Enter additional notes or instructions"
          [class.invalid]="descriptionField.invalid && descriptionField.touched"></textarea>
      </div>
      <div class="error-message text-red-600 dark:!text-red-400 text-xs mt-1"
        *ngIf="descriptionField.invalid && descriptionField.touched">
        Description is required
      </div>
    </div>

    <div class="dialog-actions dark:!bg-gray-700 flex justify-end gap-3 mt-6">
      <button type="button"
        class="btn btn-cancel px-4 py-2 rounded bg-gray-100 dark:!bg-gray-800 text-gray-700 dark:!text-gray-200 hover:bg-gray-200 dark:!hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:!focus:ring-gray-500 transition-colors duration-200"
        (click)="onCancel()">
        Cancel
      </button>
      <button type="submit"
        class="btn btn-primary px-4 py-2 rounded bg-blue-600 dark:!bg-blue-700 text-white hover:bg-blue-700 dark:!hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400 focus:ring-offset-1 dark:!focus:ring-offset-gray-900 transition-colors duration-200"
        [disabled]="medicationForm.invalid" [ngClass]="{'opacity-50 cursor-not-allowed': medicationForm.invalid}">
        {{ isEditMode ? 'Update' : 'Add' }}
      </button>
    </div>
  </form>
</div>
