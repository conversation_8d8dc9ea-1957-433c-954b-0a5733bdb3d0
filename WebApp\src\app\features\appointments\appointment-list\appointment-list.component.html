<div class="min-h-screen  p-5">
  <div class="">
    <!-- <PERSON> Header -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-5">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Appointments</h1>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
        Manage and track your medical appointments
      </p>
    </div>

    <!-- Stats Cards Section -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Appointments Card -->
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-4">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900  flex items-center justify-center w-14 h-14">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Total Appointments</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-blue-800 dark:text-blue-300">{{appointments.length || 0}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">total</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pending Appointments Card -->
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-4">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center w-14 h-14">
              <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Pending</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-yellow-800 dark:text-yellow-300">{{getPendingCount()}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">waiting</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Confirmed Appointments Card -->
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-4">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center w-14 h-14">
              <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Confirmed</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-green-800 dark:text-green-300">{{getConfirmedCount()}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">scheduled</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Today's Appointments Card -->
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-4">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center w-14 h-14">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Today</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-purple-800 dark:text-purple-300">{{getTodayCount()}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">today</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="bg-white dark:bg-gray-950 rounded-lg shadow">
      <!-- Action Bar -->
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex flex-col lg:flex-row justify-between gap-4">
          <!-- Search and Filters -->
          <div class="flex flex-col sm:flex-row gap-4 flex-1">
            <!-- Search Input -->
            <div class="flex-1">
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                    fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
                <input type="text" [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()"
                  placeholder="Search appointments by email, message, or status..."
                  class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-shadow">
              </div>
            </div>

            <!-- Status Filter -->
            <div class="sm:w-48">
              <div class="relative">
                <select [(ngModel)]="selectedStatus" (ngModelChange)="onStatusChange()"
                  class="block w-full pl-3 pr-12 py-2 text-base border border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg appearance-none bg-white">
                  <option *ngFor="let status of statusOptions" [value]="status">{{status}}</option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                  <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <!-- <div class="flex items-center gap-3">
            <button (click)="addNewAppointment()"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-800">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              New Appointment
            </button>
          </div> -->
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex justify-center items-center p-12">
        <div class="flex flex-col items-center">
          <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
          <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">Loading appointments...</p>
        </div>
      </div>

      <!-- Table Content -->
      <div *ngIf="!isLoading && filteredAppointments.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Patient
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Scheduled Time
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th *ngIf="isAdmin" scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Assigned Doctor
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Message
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>

              <th scope="col"
                class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-950 divide-y divide-gray-200 dark:divide-gray-700">
            <tr *ngFor="let appointment of paginatedAppointments" class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <!-- Patient Column -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
              <div class="text-sm font-medium text-gray-900 dark:text-white">{{appointment.patientEmail}}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">Patient</div>
                  </div>
                </div>
              </td>

              <!-- Scheduled Time Column -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">{{formatDateTime(appointment.scheduledTime)}}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">{{getRelativeTime(appointment.scheduledTime)}}</div>
              </td>

              <!-- Status Column -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  [class]="'px-2 inline-flex text-xs leading-5 rounded-full ' + getStatusColor(appointment.status) + ' dark:text-gray-100'">
                  {{appointment.status || 'Pending'}}
                </span>
              </td>

              <!-- Assigned Doctor Column (Admin Only) -->
              <td *ngIf="isAdmin" class="px-6 py-4 whitespace-nowrap">
                <div *ngIf="appointment.assignedDoctorEmail; else unassigned" class="text-sm text-gray-900 dark:text-white">
                  {{appointment.assignedDoctorEmail}}
                </div>
                <ng-template #unassigned>
                  <span class="text-sm text-gray-500 dark:text-gray-400 italic">Unassigned</span>
                </ng-template>
              </td>

              <!-- Message Column -->
              <td class="px-6 py-4">
                @if(appointment.status === "Completed"){
                  <div class="text-sm text-gray-900 dark:text-white" [title]="appointment.completionNote">
                  {{appointment.completionNote ? (appointment.completionNote.length > 20 ? appointment.completionNote.substring(0, 20) + '...' : appointment.completionNote) : 'No message'}}
                </div>
                } @else if (appointment.status=== "Confirmed") {
                  <div class="text-sm text-gray-900 dark:text-white" [title]="appointment.confirmationNote">
                  {{appointment.confirmationNote ? (appointment.confirmationNote.length > 20 ? appointment.confirmationNote.substring(0, 20) + '...' : appointment.confirmationNote) : 'No message'}}
                </div>
                } @else if (appointment.status === "Cancelled") {
                  <div class="text-sm text-gray-900 dark:text-white" [title]="appointment.message">
                  {{appointment.message ? (appointment.message.length > 20 ? appointment.message.substring(0, 20) + '...' : appointment.message) : 'No message'}}
                </div>
                } @else {
                  <div class="text-sm text-gray-900 dark:text-white" title="No Message">
                    No Message
                  </div>
                }
                <!-- <div class="text-sm text-gray-900 dark:text-white" [title]="appointment.message">
                  {{appointment.message ? (appointment.message.length > 20 ? appointment.message.substring(0, 20) + '...' : appointment.message) : 'No message'}}
                </div> -->
              </td>

              <!-- Created Column -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-white">{{formatDateTime(appointment.createDate)}}</div>
              </td>



              <!-- Actions Column -->
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-center space-x-2">
                  <!-- View Details Button -->
                  <button (click)="viewAppointment(appointment.id)"
                    class="text-blue-600 hover:text-blue-900 dark:text-blue-300 dark:hover:text-blue-400 focus:outline-none focus:underline" title="View Details">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>

                  <!-- Doctor Workflow Buttons -->
                  <ng-container *ngIf="!isAdmin && appointment.assignedDoctorEmail === currentUser?.email">
                    <!-- Start Reviewing Button (for Doctor Assigned status) -->
                    <!-- <button *ngIf="appointment.status === 'Doctor Assigned'" (click)="startReviewing(appointment)"
                      class="text-purple-600 hover:text-purple-900 focus:outline-none mr-2" title="Start Reviewing">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </button> -->

                    <!-- Confirm Button (for Reviewing status) -->
                    <!-- <button *ngIf="appointment.status === 'Reviewing'" (click)="confirmAppointment(appointment)"
                      class="text-blue-600 hover:text-blue-900 focus:outline-none mr-2" title="Confirm Appointment">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                      </svg>
                    </button> -->

                    <!-- Complete Button (for Confirmed status) -->
                    <!-- <button *ngIf="appointment.status === 'Confirmed'" (click)="completeAppointment(appointment)"
                      class="text-green-600 hover:text-green-900 focus:outline-none mr-2" title="Complete Appointment">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </button> -->
                  </ng-container>
                  <button *ngIf="appointment.patientEmail === currentUser?.email"
                    (click)="editAppointment(appointment.id)"
                    class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-300 dark:hover:text-indigo-400 focus:outline-none focus:underline" title="Edit">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button *ngIf="isAdmin" (click)="deleteAppointment(appointment)"
                    [disabled]="isDeleting === appointment.id"
                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-500 focus:outline-none focus:underline disabled:opacity-50"
                    title="Delete">
                    <span *ngIf="isDeleting === appointment.id">
                      <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                      </svg>
                    </span>
                    <span *ngIf="isDeleting !== appointment.id">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div *ngIf="!isLoading && filteredAppointments.length === 0" class="text-center py-12 dark:bg-gray-900">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No appointments found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Try adjusting your search criteria or schedule a new appointment.
        </p>
        <div class="mt-6">
          <!-- <button (click)="addNewAppointment()"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Schedule First Appointment
          </button> -->
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination Controls -->
  <div *ngIf="!isLoading && filteredAppointments.length > 0"
    class="bg-white dark:bg-gray-900 px-6 py-4 border-t border-gray-200 dark:border-gray-700 rounded-b-lg shadow">
    <div class="flex flex-col sm:flex-row items-center justify-between">
      <!-- Results Info -->
      <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
        Showing <span class="font-medium">{{((currentPage - 1) * pageSize) + 1}}</span> to
        <span class="font-medium">{{getMaxItems()}}</span> of
        <span class="font-medium">{{filteredAppointments.length}}</span> appointments
      </div>

      <!-- Pagination Navigation -->
      <div class="flex items-center space-x-2">
        <!-- Page Size Selector -->
        <div class="flex items-center mr-3">
          <label for="page-size" class="text-sm text-gray-600 dark:text-gray-400 mr-2">Show:</label>
          <select id="page-size" [(ngModel)]="pageSize" (change)="onPageSizeChange()"
            class="border border-gray-300 dark:border-gray-700   dark:bg-gray-800 dark:text-gray-100 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="15">15</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
        </div>

        <!-- Previous Button -->
        <button (click)="previousPage()" [disabled]="currentPage === 1"
          class="px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <!-- First Page Button (if not in view) -->
        <button *ngIf="paginationRange[0] > 1" (click)="goToPage(1)"
          class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
          1
        </button>

        <!-- Ellipsis (if needed) -->
        <span *ngIf="paginationRange[0] > 2" class="px-2 text-gray-400 dark:text-gray-500">...</span>

        <!-- Page Numbers -->
        <button *ngFor="let page of paginationRange" (click)="goToPage(page)" [class]="page === currentPage ?
            'px-4 py-2 rounded-lg border border-blue-500 dark:border-blue-700 bg-blue-50 dark:bg-blue-900 text-sm font-medium text-blue-600 dark:text-blue-200' :
            'px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'"
          class="transition-colors duration-200">
          {{page}}
        </button>

        <!-- Ellipsis (if needed) -->
        <span *ngIf="paginationRange[paginationRange.length - 1] < totalPages - 1" class="px-2 text-gray-400 dark:text-gray-500">...</span>

        <!-- Last Page Button (if not in view) -->
        <button *ngIf="paginationRange[paginationRange.length - 1] < totalPages" (click)="goToPage(totalPages)"
          class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
          {{totalPages}}
        </button>

        <!-- Next Button -->
        <button (click)="nextPage()" [disabled]="currentPage === totalPages"
          class="px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
