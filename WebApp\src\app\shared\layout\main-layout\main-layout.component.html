<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex transition-colors duration-200">

  <!-- Mobile Header - Only visible on mobile -->
  <div *ngIf="!isOnlyUserRole() && isMobileView" class="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 lg:hidden pb-safe">
    <div class="flex items-center justify-between h-16 px-4">
      <!-- Mobile Logo -->
      <div class="flex items-center">
        <div class="h-8 w-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-sm">UM</span>
        </div>
        <span class="ml-3 text-lg font-semibold text-gray-900 dark:text-white">User Manager</span>
      </div>

      <!-- Right side buttons -->
      <div class="flex items-center space-x-2">
        <!-- Mobile User Avatar -->
        <div class="relative">
          <button
            (click)="toggleUserDropdown()"
            class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 touch-manipulation">
            <div class="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
              {{ getUserInitials() }}
            </div>
          </button>

          <!-- Mobile User Dropdown -->
          <div *ngIf="showUserDropdown"
            class="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 py-2 z-50">

            <!-- Theme Toggle Component -->
            <div class="border-b border-gray-200 dark:border-gray-600 pb-2 mb-2">
              <app-theme-toggle></app-theme-toggle>
            </div>

            <!-- My Profile Option -->
            <a *ngIf="canAccessProfile()" routerLink="/profile" (click)="closeUserDropdown(); onMobileNavClick()"
              class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 touch-manipulation">
              <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              My Profile
            </a>

            <!-- Logout Option -->
            <button (click)="logout()"
              class="w-full flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 touch-manipulation">
              <svg class="mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
              </svg>
              Logout
            </button>
          </div>
        </div>

        <!-- Mobile Menu Button -->
        <button
          (click)="toggleMobileSidebar()"
          class="hamburger-menu p-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 touch-manipulation">
          <!-- Hamburger Icon when sidebar is closed -->
          <svg *ngIf="!isMobileSidebarOpen" class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
          <!-- Close Icon when sidebar is open -->
          <svg *ngIf="isMobileSidebarOpen" class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Sidebar Overlay -->
  <div *ngIf="!isOnlyUserRole() && isMobileView && isMobileSidebarOpen"
       class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
       (click)="closeMobileSidebar()">
  </div>

  <!-- Sidebar - Desktop and Mobile -->
  <div *ngIf="!isOnlyUserRole()"
       class="mobile-sidebar bg-white dark:bg-gray-800 shadow-lg border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ease-in-out overflow-y-auto"
       [class.translate-x-0]="!isMobileView || isMobileSidebarOpen"
       [class.-translate-x-full]="isMobileView && !isMobileSidebarOpen"
       [class.fixed]="isMobileView"
       [class.top-16]="isMobileView"
       [class.h-screen]="!isMobileView"
       [class.w-64]="true"
       [class.z-50]="isMobileView"
       [class.lg:fixed]="!isMobileView"
       [class.lg:top-0]="!isMobileView"
       [class.lg:left-0]="!isMobileView"
       [class.lg:h-screen]="!isMobileView"
       [class.lg:overflow-y-auto]="!isMobileView"
       [class.lg:z-40]="!isMobileView"
       [class.lg:shadow-lg]="!isMobileView"
       [class.lg:border-r]="!isMobileView"
       [class.lg:bg-white]="!isMobileView"
       [class.lg:dark:bg-gray-800]="!isMobileView"
       [style.height]="isMobileView ? 'calc(100vh - 4rem)' : '100vh'"
       [style.top]="isMobileView ? '4rem' : '0'">



    <!-- Users Management Header -->
    <div class="px-6 pt-6 pb-2">
      <h2 class="text-xl font-bold text-gray-900 dark:text-white">Users Management</h2>
    </div>
    <!-- Navigation Menu -->
    <nav class="mt-2 px-3 pb-20">
      <div class="space-y-1">
        <!-- Dashboard - Visible for all roles -->
        <a routerLink="/dashboard"
           (click)="onMobileNavClick()"
           routerLinkActive="bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300"
          class="group flex items-center px-3 py-3 text-sm font-medium rounded-lg border-l-4 border-transparent text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 touch-manipulation">
          <svg class="mr-3 h-5 w-5 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400 flex-shrink-0" fill="none" stroke="currentColor"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 012-2h6l2 2h6a2 2 0 012 2v1M3 7l3 3 3-3">
            </path>
          </svg>
          <span class="truncate">Dashboard</span>
        </a>

        <!-- Users Section -->
        <div>
          <a *ngIf="!hasDoctorRole()"
             routerLink="/users"
             (click)="onMobileNavClick()"
             routerLinkActive="bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300"
            class="group flex items-center px-3 py-3 text-sm font-medium rounded-lg border-l-4 border-transparent text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 touch-manipulation">
            <svg class="mr-3 h-5 w-5 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400 flex-shrink-0" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
              </path>
            </svg>
            <span class="truncate">Users Management</span>
          </a>

          <!-- Medicine Section - Only visible for Admin -->
          <a *ngIf="canManageUsers()"
             routerLink="/medicine/list"
             (click)="onMobileNavClick()"
             routerLinkActive="bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300"
            class="group flex items-center px-3 py-3 text-sm font-medium rounded-lg border-l-4 border-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200 touch-manipulation">
            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500 flex-shrink-0" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
              </path>
            </svg>
            <span class="truncate">Medicine Management</span>
          </a>

          <!-- Medicine Orders - Admin Only -->
          <a *ngIf="canManageUsers()"
             routerLink="/medicine/orders"
             (click)="onMobileNavClick()"
             routerLinkActive="bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300"
            class="group flex items-center px-3 py-3 text-sm font-medium rounded-lg border-l-4 border-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200 touch-manipulation">
            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500 flex-shrink-0" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01M12 20h.01">
              </path>
            </svg>
            <span class="truncate">Medicine Orders</span>
          </a>

          <!-- Educational Resources - Visible for all roles -->
          <a routerLink="/educational/list"
             (click)="onMobileNavClick()"
             routerLinkActive="bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300"
            class="group flex items-center px-3 py-3 text-sm font-medium rounded-lg border-l-4 border-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200 touch-manipulation">
            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500 flex-shrink-0" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
              </path>
            </svg>
            <span class="truncate">Educational Resources</span>
          </a>

          <!-- Appointments - Visible for all roles -->
          <a routerLink="/appointments"
             (click)="onMobileNavClick()"
             routerLinkActive="bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300"
            class="group flex items-center px-3 py-3 text-sm font-medium rounded-lg border-l-4 border-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200 touch-manipulation">
            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500 flex-shrink-0" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
              </path>
            </svg>
            <span class="truncate">Appointments</span>
          </a>

          <!-- Patients - Only visible for Admin and Doctor -->
          <a *ngIf="canManagePatients()"
             routerLink="/patients"
             (click)="onMobileNavClick()"
             routerLinkActive="bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300"
            class="group flex items-center px-3 py-3 text-sm font-medium rounded-lg border-l-4 border-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200 touch-manipulation">
            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500 flex-shrink-0" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
              </path>
            </svg>
            <span class="truncate">Patient Management</span>
          </a>

          <!-- Patient Requests - Only visible for Admin and Doctor -->
          <a *ngIf="canManagePatients()"
             routerLink="/request"
             (click)="onMobileNavClick()"
             routerLinkActive="bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300"
            class="group flex items-center px-3 py-3 text-sm font-medium rounded-lg border-l-4 border-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200 touch-manipulation">
            <svg class="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500 flex-shrink-0" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01M12 20h.01">
              </path>
            </svg>
            <span class="truncate">Patient Requests</span>
          </a>
 <!-- Chat - Visible for all authenticated users -->
          <a routerLink="/chat" (click)="onMobileNavClick()"
            routerLinkActive="bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300"
            class="group flex items-center px-3 py-3 text-sm font-medium rounded-lg border-l-4 border-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200 touch-manipulation">
            <div class="relative mr-3">
              <svg class="h-5 w-5 text-gray-400 group-hover:text-gray-500 flex-shrink-0" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <!-- Message Badge -->
              <span *ngIf="totalUnreadCount > 0"
                class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium animate-pulse">
                {{ totalUnreadCount > 99 ? '99+' : totalUnreadCount }}
              </span>
            </div>
            <span class="truncate">Messages</span>
          </a>

        </div>
      </div>
    </nav>

    <!-- User Info at Bottom with Dropdown -->
    <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 transition-colors duration-200">
      <div class="relative">
        <!-- Dropdown Menu -->
        <div *ngIf="showUserDropdown"
          class="user-dropdown dropdown-menu absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 py-2 z-50">

          <!-- Theme Toggle Component -->
          <div class="border-b border-gray-200 dark:border-gray-600 pb-2 mb-2">
            <app-theme-toggle></app-theme-toggle>
          </div>

          <!-- My Profile Option - Only for doctors -->
          <a *ngIf="canAccessProfile()"
             routerLink="/profile"
             (click)="closeUserDropdown(); onMobileNavClick()"
            class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 touch-manipulation">
            <svg class="mr-3 h-4 w-4 text-gray-400 dark:text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span>My Profile</span>
          </a>

          <!-- Logout Option -->
          <button (click)="logout()"
            class="w-full flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 touch-manipulation">
            <svg class="mr-3 h-4 w-4 text-gray-400 dark:text-gray-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span>Logout</span>
          </button>
        </div>

        <!-- User Info Button -->
        <button (click)="toggleUserDropdown()"
          class="w-full flex items-center p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 touch-manipulation">
          <div
            class="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
            {{ getUserInitials() }}
          </div>
          <div class="ml-3 flex-1 min-w-0 text-left">
            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ currentUser?.firstName || currentUser?.['name'] }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ getUserRole() }}</p>
          </div>
          <svg class="ml-2 h-4 w-4 text-gray-400 dark:text-gray-500 transition-transform duration-200 flex-shrink-0"
            [class.rotate-180]="showUserDropdown" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content Area - Hidden for users with only User role -->
  <div *ngIf="!isOnlyUserRole()"
       class="flex-1 min-w-0 transition-all duration-300 ease-in-out lg:ml-64"
       [class.pt-20]="isMobileView && !isChatRoute()">
    <!-- Main content -->
    <main class="h-full overflow-auto focus:outline-none bg-gray-50 dark:bg-gray-900 transition-colors !mb-0 duration-200"
          [class.min-h-screen]="isMobileView && !isChatRoute()"
          [class.h-screen]="isChatRoute()">
      <router-outlet></router-outlet>
    </main>
  </div>

  <!-- App Download Message - Only this is shown for users with only the User role -->
  <div *ngIf="isOnlyUserRole()" class="flex-1 min-w-0 flex flex-col justify-center items-center bg-blue-50 dark:bg-gray-900 transition-colors duration-200">
    <div class="max-w-3xl w-full p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-blue-100 dark:border-gray-700 m-auto text-center transition-colors duration-200">
      <div class="mx-auto w-20 h-20 bg-blue-50 dark:bg-blue-900 rounded-full flex items-center justify-center mb-6 transition-colors duration-200">
        <svg class="h-12 w-12 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      </div>
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Patient Care Mobile App Required</h2>
      <p class="text-gray-600 dark:text-gray-300 mb-8">
        Our patient care system has moved to a mobile-first experience. Please download our mobile app to access all
        your patient care features and information.
      </p>
      <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 justify-center">
        <a href="https://play.google.com/store" target="_blank" rel="noopener noreferrer"
          class="inline-flex justify-center items-center px-5 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M3 20.5V3.5C3 2.91 3.34 2.39 3.84 2.15L13.69 12L3.84 21.85C3.34 21.6 3 21.09 3 20.5ZM16.81 15.12L6.05 21.34L14.54 12.85L16.81 15.12ZM20.16 12.34C20.5 12.55 20.75 12.92 20.75 13.34C20.75 13.76 20.5 14.12 20.17 14.33L17.57 15.85L14.99 13.27L17.57 10.69L20.16 12.34ZM6.05 2.66L16.81 8.88L14.54 11.15L6.05 2.66Z" />
          </svg>
          Download on Google Play
        </a>
        <a href="https://www.apple.com/app-store/" target="_blank" rel="noopener noreferrer"
          class="inline-flex justify-center items-center px-5 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
          <svg class="h-5 w-5 mr-3" viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M14.94,5.19A4.38,4.38,0,0,0,16,2,4.44,4.44,0,0,0,13,3.52,4.17,4.17,0,0,0,12,6.61,3.69,3.69,0,0,0,14.94,5.19Zm2.52,7.44A4.51,4.51,0,0,1,19,9.5a4.41,4.41,0,0,0-2.88-2.53,4.3,4.3,0,0,0-1.64,3.31,4.3,4.3,0,0,0,3,2.35ZM18.4,20.1l-1.1-3.2a.4.4,0,0,0-.38-.3H7.08a.4.4,0,0,0-.38.3L5.6,20.1a.39.39,0,0,0,.36.5H18a.39.39,0,0,0,.36-.5ZM12,6.9c-.16,0-4.2.11-4.2,5.3s4.2,5.2,4.2,5.2,4.2,0,4.2-5.2S12.16,6.9,12,6.9Z" />
          </svg>
          Download on App Store
        </a>
      </div>
      <div class="mt-6">
        <button (click)="logout()" class="text-blue-600 hover:text-blue-800 font-medium">
          Log Out
        </button>
      </div>
    </div>
  </div>

  <!-- App Download Banner - For users with multiple roles including User -->
  <app-download-banner *ngIf="authService.shouldShowAppDownloadBanner() && !isOnlyUserRole()"></app-download-banner>
</div>
