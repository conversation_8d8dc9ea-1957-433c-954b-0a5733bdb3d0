/* Pagination Styles */
.pagination-container {
  width: 100%;
}

.page-size-select {
  height: 34px;
  padding: 0 0.5rem;
  font-size: 0.875rem;
  min-width: 70px;
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 1.5em 1.5em;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.page-size-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
}

.pagination-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background-color: white;
  transition: all 0.15s ease-in-out;
}

.pagination-button:hover:not(.pagination-button-disabled) {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.pagination-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
}

.pagination-button-disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.pagination-button-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  font-size: 0.875rem;
  transition: all 0.15s ease-in-out;
}

.pagination-button-number:hover:not(.pagination-button-active) {
  /* background-color: #f3f4f6; */
  border-color: #9ca3af;
}

.pagination-button-number:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
}

.pagination-button-active {
  background-color: #4f46e5;
  border-color: #4f46e5;
  color: white;
  font-weight: 500;
}

.pagination-button-active:hover {
  background-color: #4338ca;
  border-color: #4338ca;
}

.pagination-ellipsis {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  font-size: 1rem;
  color: #6b7280;
}

/* Responsive styles for pagination */
@media (max-width: 768px) {
  .pagination-container {
    overflow-x: auto;
    padding-bottom: 10px;
  }

  .pagination-button-number {
    display: none;
  }

  /* Always show first, last, and current pages on mobile */
  .pagination-button-number.pagination-button-active,
  .pagination-button-number.first-page,
  .pagination-button-number.last-page {
    display: inline-flex;
  }
}
