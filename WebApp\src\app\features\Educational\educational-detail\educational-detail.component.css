/* Educational Article Detail Styles */

/* Article content typography and spacing */
.article-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.7;
  color: #2d3748;
}

/* Dark mode support for article content */
.dark .article-content {
  color: #e2e8f0;
}

/* Checklist/Checkpoint Styling */
:host ::ng-deep .article-content .checklist,
:host ::ng-deep .article-content .checkpoint {
  list-style: none;
  padding-left: 0;
  margin: 1.5rem 0;
}

:host ::ng-deep .article-content .checklist li,
:host ::ng-deep .article-content .checkpoint li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

:host ::ng-deep .article-content .checklist li:hover,
:host ::ng-deep .article-content .checkpoint li:hover {
  background-color: #f8fafc;
}

:host ::ng-deep .dark .article-content .checklist li:hover,
:host ::ng-deep .dark .article-content .checkpoint li:hover {
  background-color: #374151;
}

:host ::ng-deep .article-content .checklist li svg,
:host ::ng-deep .article-content .checkpoint li svg {
  flex-shrink: 0;
  margin-right: 0.75rem;
  margin-top: 0.125rem;
  transition: color 0.2s ease;
}

:host ::ng-deep .article-content .checklist li.checked,
:host ::ng-deep .article-content .checkpoint li.checked {
  opacity: 0.7;
}

:host ::ng-deep .article-content .checklist li.checked span:last-child,
:host ::ng-deep .article-content .checkpoint li.checked span:last-child {
  text-decoration: line-through;
  color: #9ca3af;
}

:host ::ng-deep .dark .article-content .checklist li.checked span:last-child,
:host ::ng-deep .dark .article-content .checkpoint li.checked span:last-child {
  color: #6b7280;
}

/* Enhanced dark mode text visibility */
:host ::ng-deep .dark .article-content {
  color: #f1f5f9;
}

/* Typography improvements for better reading experience */
:host ::ng-deep .article-content h1 {
  font-size: 2.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
  color: #c3c8d4;
  letter-spacing: -0.025em;
}

/* Dark mode heading styles */
:host ::ng-deep .dark .article-content h1 {
  color: #f7fafc;
}

/* Mobile responsive headings */
@media (max-width: 640px) {
  :host ::ng-deep .article-content h1 {
    font-size: 1.875rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }
}

:host ::ng-deep .article-content h2 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.3;
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
  color: #1a202c;
  letter-spacing: -0.025em;
}

/* Dark mode support for all headings */
:host ::ng-deep .dark .article-content h2 {
  color: #f7fafc;
}

:host ::ng-deep .dark .article-content h3 {
  color: #e2e8f0;
}

:host ::ng-deep .dark .article-content h4 {
  color: #e2e8f0;
}

:host ::ng-deep .dark .article-content h5,
:host ::ng-deep .dark .article-content h6 {
  color: #cbd5e1;
}

:host ::ng-deep .article-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #2d3748;
}

:host ::ng-deep .article-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #2d3748;
}

:host ::ng-deep .article-content h5,
:host ::ng-deep .article-content h6 {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #4a5568;
}

/* Paragraph styling */
:host ::ng-deep .article-content p {
  font-size: 1.125rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  color: #2d3748;
}

/* Dark mode support for paragraphs and lists */
:host ::ng-deep .dark .article-content p {
  color: #e2e8f0;
}

:host ::ng-deep .dark .article-content li {
  color: #e2e8f0;
}

/* Stronger text colors for better visibility in dark mode */
:host ::ng-deep .dark .article-content p {
  color: #f1f5f9;
  font-weight: 400;
}

:host ::ng-deep .dark .article-content li {
  color: #f1f5f9;
  font-weight: 400;
}

/* Enhanced contrast for strong/bold text in dark mode */
:host ::ng-deep .dark .article-content strong,
:host ::ng-deep .dark .article-content b {
  color: #ffffff;
  font-weight: 600;
}

/* Enhanced emphasis text in dark mode */
:host ::ng-deep .dark .article-content em,
:host ::ng-deep .dark .article-content i {
  color: #f8fafc;
}

/* Code and preformatted text in dark mode */
:host ::ng-deep .dark .article-content code {
  background-color: #374151;
  color: #f9fafb;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

:host ::ng-deep .dark .article-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
}

/* Dark mode support for links */
:host ::ng-deep .dark .article-content a {
  color: #63b3ed;
}

:host ::ng-deep .dark .article-content a:hover {
  color: #90cdf4;
}

/* First paragraph after heading */
:host ::ng-deep .article-content h1 + p,
:host ::ng-deep .article-content h2 + p,
:host ::ng-deep .article-content h3 + p {
  margin-top: 0;
}

/* Lists styling */
:host ::ng-deep .article-content ul,
:host ::ng-deep .article-content ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

:host ::ng-deep .article-content ul {
  list-style-type: disc;
}

:host ::ng-deep .article-content ol {
  list-style-type: decimal;
}

:host ::ng-deep .article-content li {
  font-size: 1.125rem;
  line-height: 1.7;
  margin-bottom: 0.5rem;
  color: #2d3748;
}

/* Mobile responsive paragraph and list styling */
@media (max-width: 640px) {
  :host ::ng-deep .article-content p {
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  :host ::ng-deep .article-content li {
    font-size: 1rem;
    margin-bottom: 0.25rem;
  }

  :host ::ng-deep .article-content ul,
  :host ::ng-deep .article-content ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
  }
}

:host ::ng-deep .article-content li p {
  margin-bottom: 0.5rem;
}

/* Nested lists */
:host ::ng-deep .article-content ul ul,
:host ::ng-deep .article-content ol ol,
:host ::ng-deep .article-content ul ol,
:host ::ng-deep .article-content ol ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

/* Links */
:host ::ng-deep .article-content a {
  color: #3182ce;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

:host ::ng-deep .article-content a:hover {
  color: #2c5aa0;
  border-bottom-color: #3182ce;
}

/* Article Figures and Images - Professional Magazine Style */
:host ::ng-deep .article-content figure {
  margin: 3rem 0;
  position: relative;
  display: block;
  width: 100%;
  overflow: hidden;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 10px 20px -5px rgba(0, 0, 0, 0.1);
  background: #fff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:host ::ng-deep .article-content figure:hover {
  transform: translateY(-4px);
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.2), 0 15px 25px -5px rgba(0, 0, 0, 0.15);
}

:host ::ng-deep .article-content figure img {
  width: 100%;
  height: auto;
  display: block;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
  transition: all 0.3s ease;
  object-fit: cover;
  max-height: 500px;
}

/* Image hover effect */
:host ::ng-deep .article-content figure:hover img {
  transform: scale(1.02);
}

/* Figure caption styling */
:host ::ng-deep .article-content figcaption {
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-size: 0.875rem;
  color: #4a5568;
  font-style: normal;
  text-align: left;
  line-height: 1.5;
  border-top: 1px solid #e2e8f0;
  position: relative;
}

:host ::ng-deep .article-content figcaption::before {
  content: '';
  position: absolute;
  top: 0;
  left: 1.5rem;
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, #3182ce, #9f7aea);
  border-radius: 2px;
}

/* Dark mode support for figures and images */
:host ::ng-deep .dark .article-content figure {
  background: #374151;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 10px 20px -5px rgba(0, 0, 0, 0.3);
}

:host ::ng-deep .dark .article-content figure:hover {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.5), 0 15px 25px -5px rgba(0, 0, 0, 0.4);
}

/* Dark mode figure caption */
:host ::ng-deep .dark .article-content figcaption {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  color: #e2e8f0;
  border-top: 1px solid #4b5563;
}

/* Mobile responsive image styling */
@media (max-width: 640px) {
  :host ::ng-deep .article-content figure {
    margin: 1.5rem -1rem;
    border-radius: 0.5rem;
  }

  :host ::ng-deep .article-content figure img {
    max-height: 300px;
  }

  :host ::ng-deep .article-content figcaption {
    padding: 1rem;
    font-size: 0.8rem;
  }

  :host ::ng-deep .article-content figcaption::before {
    left: 1rem;
    width: 30px;
    height: 2px;
  }
}

/* Standalone images (not in figures) */
:host ::ng-deep .article-content img:not(figure img) {
  max-width: 100%;
  height: auto;
  border-radius: 1rem;
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15), 0 8px 16px -4px rgba(0, 0, 0, 0.1);
  margin: 2.5rem auto;
  display: block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:host ::ng-deep .article-content img:not(figure img):hover {
  transform: translateY(-2px) scale(1.01);
  box-shadow: 0 30px 50px -12px rgba(0, 0, 0, 0.2), 0 12px 20px -4px rgba(0, 0, 0, 0.15);
}

/* Wide images (full bleed) */
:host ::ng-deep .article-content .image-wide {
  margin: 3rem -2rem;
  border-radius: 0;
  width: calc(100% + 4rem);
  max-width: calc(100% + 4rem);
}

/* Small inline images */
:host ::ng-deep .article-content .image-small {
  max-width: 300px;
  float: right;
  margin: 0 0 1rem 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1);
}

/* Image gallery grid */
:host ::ng-deep .article-content .image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 3rem 0;
}

:host ::ng-deep .article-content .image-gallery figure {
  margin: 0;
  border-radius: 0.75rem;
  overflow: hidden;
}

:host ::ng-deep .article-content .image-gallery img {
  height: 200px;
  object-fit: cover;
}

/* Image with overlay text */
:host ::ng-deep .article-content .image-overlay {
  position: relative;
  overflow: hidden;
}

:host ::ng-deep .article-content .image-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
  pointer-events: none;
}

:host ::ng-deep .article-content .image-overlay figcaption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: transparent;
  color: white;
  border-top: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  z-index: 1;
}

/* Image loading placeholder */
:host ::ng-deep .article-content .image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 1rem;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Image error state */
:host ::ng-deep .article-content .image-error {
  background: #f8fafc;
  border: 2px dashed #cbd5e0;
  border-radius: 1rem;
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #718096;
  margin: 2rem 0;
}

:host ::ng-deep .article-content .image-error svg {
  width: 4rem;
  height: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

:host ::ng-deep .article-content .image-error p {
  margin: 0;
  font-size: 0.875rem;
  text-align: center;
}

/* Enhanced image error states for better dark mode visibility */
:host ::ng-deep .dark .image-error p {
  color: #e5e7eb !important;
  font-weight: 500 !important;
}

:host ::ng-deep .dark .image-error svg {
  color: #9ca3af !important;
}

/* Ensure error messages are always visible */
:host ::ng-deep .dark .article-content .image-error {
  background: #1f2937 !important;
  border: 2px dashed #4b5563 !important;
  color: #e5e7eb !important;
}

:host ::ng-deep .dark .article-content .image-error * {
  color: #e5e7eb !important;
}

/* Image loading and error states */
:host ::ng-deep .dark .article-content figure.image-error-state {
  background: #1f2937 !important;
  border: 2px dashed #4b5563 !important;
}

:host ::ng-deep .dark .article-content figure.image-error-state * {
  color: #e5e7eb !important;
}

/* Additional image style variations */
:host ::ng-deep .article-content .image-with-background {
  background: #f8fafc;
  padding: 2rem;
  border-radius: 1rem;
}

:host ::ng-deep .article-content .image-with-border {
  border: 3px solid #e2e8f0;
  padding: 0.5rem;
  background: #fff;
}

:host ::ng-deep .article-content .image-with-border img {
  border-radius: 0.5rem;
}

/* Article figure specific styling */
:host ::ng-deep .article-content .article-figure {
  position: relative;
  overflow: hidden;
}

/* Image zoom on click effect */
:host ::ng-deep .article-content .article-figure img {
  cursor: zoom-in;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Photo credit styling */
:host ::ng-deep .article-content figcaption .photo-credit {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.5rem;
  font-style: normal;
}

:host ::ng-deep .article-content figcaption .photo-credit::before {
  content: "Photo: ";
  font-weight: 600;
}

/* Aspect ratio containers for consistent image sizing */
:host ::ng-deep .article-content .aspect-ratio-16-9 {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
  border-radius: 1rem;
}

:host ::ng-deep .article-content .aspect-ratio-16-9 img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin: 0;
}

:host ::ng-deep .article-content .aspect-ratio-4-3 {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 75%; /* 4:3 aspect ratio */
  overflow: hidden;
  border-radius: 1rem;
}

:host ::ng-deep .article-content .aspect-ratio-4-3 img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin: 0;
}

/* Image layouts for multiple images */
:host ::ng-deep .article-content .image-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 3rem 0;
}

:host ::ng-deep .article-content .image-row figure {
  margin: 0;
}

/* Side-by-side image layout */
:host ::ng-deep .article-content .image-side-by-side {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 3rem 0;
}

:host ::ng-deep .article-content .image-side-by-side figure {
  margin: 0;
}

/* Image with text wrap */
:host ::ng-deep .article-content .image-float-left {
  float: left;
  margin: 0 2rem 1rem 0;
  max-width: 350px;
  border-radius: 0.75rem;
  box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1);
}

:host ::ng-deep .article-content .image-float-right {
  float: right;
  margin: 0 0 1rem 2rem;
  max-width: 350px;
  border-radius: 0.75rem;
  box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1);
}

/* Clear floats after floating images */
:host ::ng-deep .article-content .clear-float {
  clear: both;
}

/* Blockquotes */
:host ::ng-deep .article-content blockquote {
  border-left: 4px solid #3182ce;
  padding-left: 1.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  margin: 2rem 0;
  background-color: #f7fafc;
  border-radius: 0 0.5rem 0.5rem 0;
  font-style: italic;
}

/* Blockquotes in dark mode */
:host ::ng-deep .dark .article-content blockquote {
  border-left: 4px solid #60a5fa;
  background-color: #1e293b;
  color: #e2e8f0;
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
}

:host ::ng-deep .article-content blockquote p {
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-size: 1.125rem;
}

:host ::ng-deep .article-content blockquote cite {
  font-size: 0.875rem;
  color: #718096;
  font-style: normal;
  font-weight: 500;
}

/* Code blocks */
:host ::ng-deep .article-content pre {
  background-color: #1a202c;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 0.75rem;
  margin: 2rem 0;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.6;
}

/* Code blocks in dark mode */
:host ::ng-deep .dark .article-content pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
}

/* Code styling */
:host ::ng-deep .article-content code {
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}

/* Code styling in dark mode */
:host ::ng-deep .dark .article-content code {
  background-color: #374151;
  color: #f9fafb;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Tables */
:host ::ng-deep .article-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  font-size: 0.875rem;
}

:host ::ng-deep .article-content th,
:host ::ng-deep .article-content td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

:host ::ng-deep .article-content th {
  background-color: #f7fafc;
  font-weight: 600;
  color: #2d3748;
}

:host ::ng-deep .article-content tr:hover {
  background-color: #f7fafc;
}

/* Dividers */
:host ::ng-deep .article-content hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, #e2e8f0, transparent);
  margin: 3rem 0;
}

/* Delimiter styling (three dots) */
:host ::ng-deep .article-content .delimiter {
  display: flex;
  justify-content: center;
  margin: 3rem 0;
}

/* First letter drop cap effect (optional, for long articles) */
.article-content.drop-cap > p:first-of-type::first-letter {
  float: left;
  font-size: 4rem;
  line-height: 3rem;
  padding-top: 0.25rem;
  padding-right: 0.5rem;
  font-weight: 700;
  color: #3182ce;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :host ::ng-deep .article-content h1 {
    font-size: 2rem;
  }

  :host ::ng-deep .article-content h2 {
    font-size: 1.75rem;
  }

  :host ::ng-deep .article-content h3 {
    font-size: 1.375rem;
  }

  :host ::ng-deep .article-content p,
  :host ::ng-deep .article-content li {
    font-size: 1rem;
  }

  :host ::ng-deep .article-content blockquote {
    padding-left: 1rem;
    margin: 1.5rem 0;
  }

  /* Mobile image adjustments */
  :host ::ng-deep .article-content figure {
    margin: 2rem 0;
    border-radius: 0.75rem;
  }

  :host ::ng-deep .article-content figcaption {
    padding: 1rem;
    font-size: 0.8rem;
  }

  :host ::ng-deep .article-content .image-wide {
    margin: 2rem -1rem;
    width: calc(100% + 2rem);
    max-width: calc(100% + 2rem);
  }

  :host ::ng-deep .article-content .image-small {
    float: none;
    margin: 1.5rem auto;
    display: block;
    max-width: 100%;
  }

  :host ::ng-deep .article-content .image-gallery {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin: 2rem 0;
  }

  :host ::ng-deep .article-content .image-gallery img {
    height: 250px;
  }
}

/* Print styles */
@media print {
  .article-content {
    font-size: 12pt;
    line-height: 1.5;
  }

  :host ::ng-deep .article-content h1,
  :host ::ng-deep .article-content h2,
  :host ::ng-deep .article-content h3 {
    page-break-after: avoid;
  }

  :host ::ng-deep .article-content figure,
  :host ::ng-deep .article-content img {
    page-break-inside: avoid;
  }
}

/* Accessibility improvements */
:host ::ng-deep .article-content a:focus {
  outline: 2px solid #3182ce;
  outline-offset: 2px;
}

/* Selection styling */
:host ::ng-deep .article-content ::selection {
  background-color: #bee3f8;
  color: #1a202c;
}

:host ::ng-deep .prose blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  font-style: italic;
  color: #4b5563;
  margin: 1.5rem 0;
}

:host ::ng-deep .prose pre {
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

:host ::ng-deep .prose code {
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  font-family: monospace;
}

/* Featured image styling */
.featured-image {
  max-height: 500px;
  width: 100%;
  object-fit: cover;
  border-radius: 0.5rem;
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.spin {
  animation: spin 1s linear infinite;
}

/* Category badge styling */
.category-badge {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Image error handling */
.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 16rem; /* 64px */
  background-color: #f3f4f6; /* gray-100 */
  border-radius: 0.5rem;
  text-align: center;
  padding: 2rem;
}

.image-error svg {
  margin-bottom: 1rem;
  color: #9ca3af; /* gray-400 */
}

.image-error p {
  color: #6b7280; /* gray-500 */
  font-size: 0.875rem;
}

/* Enhanced image error states for better dark mode visibility */
:host ::ng-deep .dark .image-error p {
  color: #e5e7eb !important;
  font-weight: 500 !important;
}

:host ::ng-deep .dark .image-error svg {
  color: #9ca3af !important;
}

/* Ensure error messages are always visible */
:host ::ng-deep .dark .article-content .image-error {
  background: #1f2937 !important;
  border: 2px dashed #4b5563 !important;
  color: #e5e7eb !important;
}

:host ::ng-deep .dark .article-content .image-error * {
  color: #e5e7eb !important;
}

/* Image loading and error states */
:host ::ng-deep .dark .article-content figure.image-error-state {
  background: #1f2937 !important;
  border: 2px dashed #4b5563 !important;
}

:host ::ng-deep .dark .article-content figure.image-error-state * {
  color: #e5e7eb !important;
}
