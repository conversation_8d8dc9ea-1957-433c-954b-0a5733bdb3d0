<div class="p-5 bg-white dark:bg-gray-900 min-h-screen">
  <!-- <PERSON> Header -->
  <div
    class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-5 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Educational Resources</h1>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-200">Browse and manage educational health content</p>
    </div>
    <div class="mt-4 md:mt-0">
      <!-- Only <PERSON><PERSON> and Doctor can add educational content -->
      <button *ngIf="canManageEducationalContent" (click)="addNewEducationalItem()"
        class="inline-flex items-center px-4 py-2 border border-blue-800 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600  hover:bg-blue-700 ">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
            clip-rule="evenodd" />
        </svg>
        Add New
      </button>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <div class="flex flex-col sm:flex-row gap-4">
        <!-- Search Input -->
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                  clip-rule="evenodd" />
              </svg>
            </div>
            <div class="relative">
              <input type="text" id="search" [(ngModel)]="searchTerm" (input)="onSearchChange()"
                placeholder="Search educational content..."
                class="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 sm:text-sm transition-shadow placeholder-gray-500 dark:placeholder-gray-400"
                aria-label="Search educational content">
              <button *ngIf="searchTerm" (click)="searchTerm = ''; onSearchChange()"
                class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                aria-label="Clear search">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Category Filter -->
        <div class="sm:w-48">
          <div class="relative">
            <select [(ngModel)]="selectedCategory" (change)="onCategoryChange()"
              class="block w-full pl-3 pr-12 py-2 text-base border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 sm:text-sm rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 appearance-none"
              aria-label="Filter by category">
              <option *ngFor="let category of categoryOptions" [value]="category">
                {{ category }}
              </option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
              <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-24">
    <div class="flex flex-col items-center">
      <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
      <p class="mt-4 text-sm text-gray-500">Loading educational content...</p>
    </div>
  </div>

  <!-- Content Grid -->
  <div *ngIf="!isLoading && displayedItems.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div *ngFor="let item of displayedItems" (click)="viewEducationalItem(item.id)"
      class="educational-card group bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg flex flex-col h-full transition-all duration-300 hover:scale-[1.01] hover:shadow-md cursor-pointer">
      <!-- Featured Image if available -->
      <div class="aspect-video overflow-hidden relative card-image">
        <div *ngIf="item.featuredImg" class="w-full h-full">
          <img [src]="baseUrl + '/api/File/Getfile/' + item.featuredImg" class="object-cover w-full h-full"
            alt="Educational content" loading="lazy" (error)="onImageError($event)">
        </div>
        <div *ngIf="!item.featuredImg"
          class="w-full h-full bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-blue-300 dark:text-gray-500" fill="none"
            viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        </div>

        <!-- Reading Time Badge - Top Left -->
        <div
          class="absolute top-3 left-3 bg-white/90 dark:bg-gray-900/80 backdrop-blur-sm text-gray-700 dark:text-gray-200 text-xs px-3 py-1.5 rounded-full font-medium shadow-sm">
          {{ item.readingTime || '5 min read' }}
        </div>

        <!-- Admin/Doctor Actions - Only visible on hover and for authorized users -->
        <div *ngIf="canManageEducationalContent"
          class="absolute top-3 right-3 flex flex-col space-y-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button (click)="editEducationalItem(item.id); $event.stopPropagation()"
            class="bg-white/90 dark:bg-gray-900/80 backdrop-blur-sm text-indigo-600 dark:text-indigo-400 p-2 rounded-full shadow-sm hover:bg-indigo-50 dark:hover:bg-gray-800 transition-colors"
            title="Edit">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
              </path>
            </svg>
          </button>
          <!-- Only Admin can delete educational content -->
          <button *ngIf="isAdmin" (click)="deleteEducationalItem(item); $event.stopPropagation()"
            [disabled]="isDeleting === item.id"
            class="bg-white/90 dark:bg-gray-900/80 backdrop-blur-sm text-red-600 dark:text-red-400 p-2 rounded-full shadow-sm hover:bg-red-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50"
            title="Delete">
            <svg *ngIf="isDeleting !== item.id" class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
              </path>
            </svg>
            <div *ngIf="isDeleting === item.id"
              class="animate-spin h-4 w-4 border-2 border-red-600 border-t-transparent rounded-full"></div>
          </button>
        </div>

        <!-- Date - Bottom Left -->
        <div
          class="absolute bottom-3 left-3 bg-black/60 dark:bg-gray-900/80 backdrop-blur-sm text-white dark:text-gray-200 text-xs px-2 py-1 rounded font-medium">
          {{ formatDate(item.createDate) }}
        </div>

        <!-- Creator Info - Bottom Right -->
        <div
          class="absolute bottom-3 right-3 flex items-center bg-black/60 dark:bg-gray-900/80 backdrop-blur-sm text-white dark:text-gray-200 text-xs px-2 py-1 rounded font-medium">
          <div
            class="h-5 w-5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-1.5">
            <span class="text-white text-[10px] font-semibold">
              {{ (item.createdBy || 'U')[0].toUpperCase() }}
            </span>
          </div>
          <span>{{ item.createdBy || 'Unknown' }}</span>
        </div>
      </div>

      <div class="p-5 flex-grow flex flex-col">
        <!-- Category Tag -->
        <span
          class="category-badge inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 mb-3 w-fit">
          {{ item.category || 'Uncategorized' }}
        </span>

        <!-- Content Preview -->
        <h3 class="content-title text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
          {{ getContentTitle(item.title) }}
        </h3>
        <p class="content-preview text-sm text-gray-600 dark:!text-gray-200 mb-4 flex-grow line-clamp-2"
          [ngClass]="{'empty-content': !getContentPreview(item.content)}">
          {{ getContentPreview(item.content) || 'No preview available' }}
        </p>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && displayedItems.length === 0"
    class="bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center">
    <svg class="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24"
      stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    </svg>
    <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">No educational content found</h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      {{ searchTerm || selectedCategory !== 'All' ? 'Try adjusting your search criteria or category filter.' : 'Get
      started by adding educational content.' }}
    </p>
    <div class="mt-6">
      <button *ngIf="isAdmin" (click)="addNewEducationalItem()"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
            clip-rule="evenodd" />
        </svg>
        Add New Educational Content
      </button>
    </div>
  </div>

  <!-- Pagination Controls -->
  <div *ngIf="!isLoading && displayedItems.length > 0" class="bg-white dark:bg-gray-800 shadow rounded-lg mt-6">
    <div class="px-6 py-4 flex flex-col sm:flex-row justify-between items-center">
      <!-- Results Info -->
      <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
        Showing
        <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span>
        to
        <span class="font-medium">{{ currentPage * pageSize > totalItems ? totalItems : currentPage * pageSize }}</span>
        of
        <span class="font-medium">{{ totalItems }}</span>
        items
      </div>

      <!-- Pagination Navigation -->
      <div class="flex items-center space-x-2">
        <!-- Page Size Selector -->
        <div class="flex items-center mr-3">
          <label for="page-size" class="text-sm text-gray-600 dark:text-gray-400 mr-2">Show:</label>
          <select id="page-size" [(ngModel)]="pageSize" (change)="onPageSizeChange($event)"
            class="border border-gray-300 dark:border-gray-600 rounded-md text-sm pl-3 pr-8 py-1.5 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
            <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }}</option>
          </select>
        </div>

        <!-- Previous Button -->
        <button (click)="previousPage()" [disabled]="currentPage === 1"
          class="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
              clip-rule="evenodd" />
          </svg>
        </button>

        <!-- Page Numbers -->
        <div class="hidden md:flex space-x-1">
          <button *ngFor="let page of paginationRange" (click)="goToPage(page)"
            class="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 px-3 py-1.5 text-sm font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2"
            [ngClass]="page === currentPage ? 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-200 border-blue-300 dark:border-blue-700' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'">
            {{ page }}
          </button>
        </div>

        <!-- Next Button -->
        <button (click)="nextPage()" [disabled]="currentPage === totalPages"
          class="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
          <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
