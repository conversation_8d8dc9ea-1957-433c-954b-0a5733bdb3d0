/* User Add Component Styles */

/* Form input styles with dark mode support */
.form-input {
  padding: 0.5rem 0.75rem;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 0.875rem;
  color: #374151;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-color: #3b82f6;
}

/* Dark mode for form inputs */
@media (prefers-color-scheme: dark) {
  .form-input {
    background-color: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
  }

  .form-input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5);
  }

  .form-input::placeholder {
    color: #9ca3af;
  }
}

:root.dark .form-input {
  background-color: #374151;
  border-color: #4b5563;
  color: #f3f4f6;
}

:root.dark .form-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5);
}

:root.dark .form-input::placeholder {
  color: #9ca3af;
}

/* Select dropdown styles */
.filter-select {
  padding: 0.5rem 0.75rem;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 0.875rem;
  color: #374151;
  min-width: 150px;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-color: #3b82f6;
}

/* Dark mode for select */
@media (prefers-color-scheme: dark) {
  .filter-select {
    background-color: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
  }

  .filter-select:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5);
  }
}

:root.dark .filter-select {
  background-color: #374151;
  border-color: #4b5563;
  color: #f3f4f6;
}

:root.dark .filter-select:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5);
}

/* Search input styles */
.search-input {
  padding-left: 2.5rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 0.875rem;
  color: #111827;
  min-width: 250px;
  background-color: white;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-color: #3b82f6;
}

.search-input::placeholder {
  color: #6b7280;
}

/* Dark mode for search input */
@media (prefers-color-scheme: dark) {
  .search-input {
    background-color: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
  }

  .search-input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5);
  }

  .search-input::placeholder {
    color: #9ca3af;
  }
}

:root.dark .search-input {
  background-color: #374151;
  border-color: #4b5563;
  color: #f3f4f6;
}

:root.dark .search-input:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5);
}

:root.dark .search-input::placeholder {
  color: #9ca3af;
}
