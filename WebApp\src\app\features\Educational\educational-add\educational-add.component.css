/* Custom styles for educational-add component */
textarea {
  min-height: 300px;
}

/* Image preview styles */
.image-preview {
  max-height: 300px;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Focus styles for form elements */
:host ::ng-deep .form-control:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  border-color: #3b82f6;
}

/* Custom hover effect for upload area */
.upload-area:hover {
  border-color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
}

/* Reduce opacity for disabled buttons */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Editor.js styles */
:host ::ng-deep .codex-editor {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  padding: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

:host ::ng-deep .codex-editor:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

:host ::ng-deep .ce-block__content {
  max-width: 100% !important;
  margin: 0;
}

:host ::ng-deep .ce-toolbar__content {
  max-width: 100% !important;
}

:host ::ng-deep .ce-toolbar__actions {
  right: 0;
}

/* Style for the placeholder */
:host ::ng-deep .ce-paragraph[data-placeholder]:empty::before {
  color: #9ca3af;
}

/* Style for toolbar buttons */
:host ::ng-deep .ce-toolbar__plus,
:host ::ng-deep .ce-toolbar__settings-btn {
  color: #4b5563;
  background: #f3f4f6;
}

:host ::ng-deep .ce-toolbar__plus:hover,
:host ::ng-deep .ce-toolbar__settings-btn:hover {
  background-color: #e5e7eb;
}

/* Inline toolbar style */
:host ::ng-deep .ce-inline-toolbar {
  border: 1px solid #e5e7eb;
  box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);
}

:host ::ng-deep .ce-inline-tool {
  color: #4b5563;
}

:host ::ng-deep .ce-inline-tool:hover {
  background: #e5e7eb;
}

:host ::ng-deep .ce-inline-tool--active {
  color: #2563eb;
}

/* Editor.js Header Styles - Target actual HTML heading elements */
:host ::ng-deep #editorContainer h1,
:host ::ng-deep #editorContainer h2,
:host ::ng-deep #editorContainer h3,
:host ::ng-deep #editorContainer h4,
:host ::ng-deep #editorContainer h5,
:host ::ng-deep #editorContainer h6 {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  color: #1f2937 !important;
  margin: 0.75em 0 0.5em 0 !important;
  line-height: 1.25 !important;
  display: block !important;
}

/* H1 - Heading 1 (Largest - Very prominent) */
:host ::ng-deep #editorContainer h1 {
  font-size: 2.5rem !important; /* 40px */
  font-weight: 800 !important;
  line-height: 1.1 !important;
  margin: 1em 0 0.5em 0 !important;
  letter-spacing: -0.025em !important;
}

/* H2 - Heading 2 (Large - Section headers) */
:host ::ng-deep #editorContainer h2 {
  font-size: 2rem !important; /* 32px */
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin: 0.9em 0 0.5em 0 !important;
  letter-spacing: -0.025em !important;
}

/* H3 - Heading 3 (Medium-Large - Subsection headers) */
:host ::ng-deep #editorContainer h3 {
  font-size: 1.75rem !important; /* 28px */
  font-weight: 600 !important;
  line-height: 1.3 !important;
  margin: 0.8em 0 0.4em 0 !important;
}

/* H4 - Heading 4 (Medium - Sub-subsection headers) */
:host ::ng-deep #editorContainer h4 {
  font-size: 1.5rem !important; /* 24px */
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 0.75em 0 0.4em 0 !important;
}

/* H5 - Heading 5 (Small-Medium - Minor headers) */
:host ::ng-deep #editorContainer h5 {
  font-size: 1.25rem !important; /* 20px */
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 0.7em 0 0.3em 0 !important;
}

/* H6 - Heading 6 (Small - Smallest headers) */
:host ::ng-deep #editorContainer h6 {
  font-size: 1.125rem !important; /* 18px */
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 0.65em 0 0.3em 0 !important;
}

/* Dark mode support for headers */
:host-context(.dark) ::ng-deep #editorContainer h1,
:host-context(.dark) ::ng-deep #editorContainer h2,
:host-context(.dark) ::ng-deep #editorContainer h3,
:host-context(.dark) ::ng-deep #editorContainer h4,
:host-context(.dark) ::ng-deep #editorContainer h5,
:host-context(.dark) ::ng-deep #editorContainer h6 {
  color: #f9fafb !important;
}

/* Paragraph styles for consistency */
:host ::ng-deep .ce-paragraph {
  font-size: 1rem;
  line-height: 1.6;
  color: #374151;
  margin: 0.5em 0;
}

:host-context(.dark) ::ng-deep .ce-paragraph {
  color: #d1d5db;
}

/* List styles */
:host ::ng-deep .cdx-list {
  margin: 0.5em 0;
}

:host ::ng-deep .cdx-list__item {
  line-height: 1.6;
  color: #374151;
  margin: 0.25em 0;
}

:host-context(.dark) ::ng-deep .cdx-list__item {
  color: #d1d5db;
}
