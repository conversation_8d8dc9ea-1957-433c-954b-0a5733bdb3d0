import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatSliderModule } from '@angular/material/slider';
import { MatTooltipModule } from '@angular/material/tooltip';

export interface ProgressPicture {
  id: string;
  url: string;
  date: Date;
  description?: string;
  patientEmail: string;
  uploadedBy: string;
}

export interface ComparisonView {
  mode: 'side-by-side' | 'overlay' | 'slider';
  beforePicture: ProgressPicture | null;
  afterPicture: ProgressPicture | null;
}

@Component({
  selector: 'app-progress-picture-comparison',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatSelectModule,
    MatSliderModule,
    MatTooltipModule
  ],
  templateUrl: './progress-picture-comparison.component.html',
  styleUrls: ['./progress-picture-comparison.component.css']
})
export class ProgressPictureComparisonComponent implements OnInit, OnDestroy {
  @Input() pictures: ProgressPicture[] = [];
  @Input() patientEmail: string = '';
  @Input() allowSelection: boolean = true;

  comparisonView: ComparisonView = {
    mode: 'side-by-side',
    beforePicture: null,
    afterPicture: null
  };

  // Slider for overlay mode
  overlayOpacity: number = 50;

  // Slider for side-by-side comparison
  sliderPosition: number = 50;

  // Zoom functionality
  zoomLevel: number = 100;
  isPanning: boolean = false;
  panOffset = { x: 0, y: 0 };
  lastPanPoint = { x: 0, y: 0 };

  // Timeline view
  showTimeline: boolean = false;
  selectedTimeRange: 'week' | 'month' | 'quarter' | 'year' | 'all' = 'month';

  // Analysis tools
  showMeasurementTools: boolean = false;
  measurements: any[] = [];

  constructor() { }

  ngOnInit(): void {
    this.initializeComparison();
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  initializeComparison(): void {
    if (this.pictures.length >= 2) {
      // Sort pictures by date
      const sortedPictures = [...this.pictures].sort((a, b) =>
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      // Set default comparison: first and last pictures
      this.comparisonView.beforePicture = sortedPictures[0];
      this.comparisonView.afterPicture = sortedPictures[sortedPictures.length - 1];
    }
  }

  // Picture selection methods
  selectBeforePicture(picture: ProgressPicture): void {
    this.comparisonView.beforePicture = picture;
  }

  selectAfterPicture(picture: ProgressPicture): void {
    this.comparisonView.afterPicture = picture;
  }

  // Comparison mode methods
  setComparisonMode(mode: 'side-by-side' | 'overlay' | 'slider'): void {
    this.comparisonView.mode = mode;
    this.resetZoomAndPan();
  }

  // Zoom and pan methods
  zoomIn(): void {
    this.zoomLevel = Math.min(this.zoomLevel + 25, 400);
  }

  zoomOut(): void {
    this.zoomLevel = Math.max(this.zoomLevel - 25, 25);
  }

  resetZoomAndPan(): void {
    this.zoomLevel = 100;
    this.panOffset = { x: 0, y: 0 };
  }

  onMouseDown(event: MouseEvent): void {
    if (this.zoomLevel > 100) {
      this.isPanning = true;
      this.lastPanPoint = { x: event.clientX, y: event.clientY };
      event.preventDefault();
    }
  }

  onMouseMove(event: MouseEvent): void {
    if (this.isPanning && this.zoomLevel > 100) {
      const deltaX = event.clientX - this.lastPanPoint.x;
      const deltaY = event.clientY - this.lastPanPoint.y;

      this.panOffset.x += deltaX;
      this.panOffset.y += deltaY;

      this.lastPanPoint = { x: event.clientX, y: event.clientY };
    }
  }

  onMouseUp(): void {
    this.isPanning = false;
  }

  // Timeline methods
  toggleTimeline(): void {
    this.showTimeline = !this.showTimeline;
  }

  setTimeRange(range: 'week' | 'month' | 'quarter' | 'year' | 'all'): void {
    this.selectedTimeRange = range;
    // Filter pictures based on time range
    // Implementation would depend on your specific requirements
  }

  getFilteredPicturesByTimeRange(): ProgressPicture[] {
    const now = new Date();
    const cutoffDate = new Date();

    switch (this.selectedTimeRange) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
      case 'all':
        return this.pictures;
    }

    return this.pictures.filter(picture => new Date(picture.date) >= cutoffDate);
  }

  // Analysis methods
  toggleMeasurementTools(): void {
    this.showMeasurementTools = !this.showMeasurementTools;
  }

  addMeasurement(type: 'length' | 'area' | 'angle'): void {
    // Implementation for adding measurements
    // This would involve drawing tools on the images
  }

  // Utility methods
  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  }

  getDaysBetween(date1: Date, date2: Date): number {
    const diffTime = Math.abs(new Date(date2).getTime() - new Date(date1).getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  getProgressAnalysis(): {
    timespan: number;
    improvement: 'positive' | 'negative' | 'stable';
    notes: string;
  } {
    if (!this.comparisonView.beforePicture || !this.comparisonView.afterPicture) {
      return { timespan: 0, improvement: 'stable', notes: 'No comparison available' };
    }

    const timespan = this.getDaysBetween(
      this.comparisonView.beforePicture.date,
      this.comparisonView.afterPicture.date
    );

    // This is a simplified analysis - in a real application,
    // you might use AI/ML for automated progress assessment
    return {
      timespan,
      improvement: 'positive', // Placeholder
      notes: `Progress tracked over ${timespan} days`
    };
  }

  // Export functionality
  exportComparison(): void {
    // Implementation for exporting the comparison as PDF or image
    console.log('Exporting comparison...');
  }

  // Fullscreen functionality
  enterFullscreen(): void {
    const element = document.querySelector('.comparison-container') as HTMLElement;
    if (element && element.requestFullscreen) {
      element.requestFullscreen();
    }
  }

  // TrackBy function for ngFor performance
  trackByPictureId(index: number, picture: ProgressPicture): string {
    return picture.id;
  }
}
