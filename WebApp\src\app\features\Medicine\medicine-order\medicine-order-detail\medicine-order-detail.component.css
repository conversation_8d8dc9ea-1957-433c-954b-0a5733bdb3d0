/* Order Detail Page Styles */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Status Badge Colors */

/* Card styling */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

  /* background: linear-gradient(to right, rgba(59, 130, 246, 0.7), rgba(16, 185, 129, 0.7));
} */

/* Animation for loading states */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* Status transition effect */
.status-badge {
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

/* Card hover effect */
.hover-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Button focus styles */
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Button hover effects */
button {
  position: relative;
  overflow: hidden;
}

button:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

button:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* Detail labels */
.detail-label {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.detail-value {
  color: #111827;
  font-size: 0.875rem;
}

/* Animation for status change */
@keyframes highlight {
  0% {
    background-color: rgba(59, 130, 246, 0.2);
  }
  100% {
    background-color: transparent;
  }
}

.highlight-change {
  animation: highlight 1s ease-out;
}

/* Status badge pulse on update */
.status-updated {
  animation: status-pulse 1.5s ease-in-out;
}

@keyframes status-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Smooth transitions for all interactive elements */
button, a, select, input {
  transition: all 0.2s ease-in-out;
}

/* Fade-in animation for content loading */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Card and Info Panel Styles */
.bg-gray-50 {
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}

.bg-gray-50:hover {
  /* border-color: #e5e7eb; */
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  /* background-color: #f8fafc; */
}

/* Status badge highlight animation */
.status-updated {
  animation: status-highlight 2s ease-in-out;
}

@keyframes status-highlight {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(59, 130, 246, 0);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  }
}

/* Custom heading style */
h3.text-lg.font-semibold {
  position: relative;
  padding-bottom: 8px;
}

h3.text-lg.font-semibold::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(to right, #3b82f6, #60a5fa);
  border-radius: 3px;
}

/* Enhanced fade-in animation */
.fade-in {
  animation: enhancedFadeIn 0.6s ease-in-out;
}

@keyframes enhancedFadeIn {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button pulse effect */
button:active {
  transform: scale(0.97);
}

/* Status Workflow Progress Bar */
.bg-amber-500 {
  background-color: #f59e0b;
  transition: width 0.5s ease-in-out;
}

.bg-blue-500 {
  background-color: #3b82f6;
  transition: width 0.5s ease-in-out;
}

.bg-purple-500 {
  background-color: #8b5cf6;
  transition: width 0.5s ease-in-out;
}

.bg-green-500 {
  background-color: #10b981;
  transition: width 0.5s ease-in-out;
}

.bg-red-500 {
  background-color: #ef4444;
  transition: width 0.5s ease-in-out;
}

.text-amber-600 {
  color: #d97706;
}

.text-blue-600 {
  color: #2563eb;
}

.text-purple-600 {
  color: #7c3aed;
}

.text-green-600 {
  color: #059669;
}

.text-red-600 {
  color: #dc2626;
}

/* Workflow step connections */
.relative {
  position: relative;
}

/* Improved disabled button styling */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
