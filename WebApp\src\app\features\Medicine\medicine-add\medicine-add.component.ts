import { Component, OnInit, OnD<PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { MedicineServiceProxy, Medicine, CreateMedicineRequest, FileServiceProxy, FileParameter, ResponseMessage } from '../../../../shared/service-proxies/service-proxies';
import { DateTime } from 'luxon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { Subject, takeUntil } from 'rxjs';
import { AddCategoryDialogComponent } from './add-category-dialog/add-category-dialog.component';

@Component({
  selector: 'app-medicine-add',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  templateUrl: './medicine-add.component.html',
  styleUrl: './medicine-add.component.css'
})
export class MedicineAddComponent implements OnInit, OnDestroy {
  @ViewChild('medicineForm') medicineForm!: NgForm;
  @ViewChild('fileInput') fileInput!: ElementRef;

  baseUrl = getRemoteServiceBaseUrl();
  medicine = new CreateMedicineRequest();
  isLoading = false;
  isEditing = false;
  medicineId?: string;
  pageTitle = 'Add New Medicine';
  selectedFile: File | null = null;
  isUploading = false;
  uploadError = '';
  imagePreview: string | ArrayBuffer | null = null;
  previousImageName: string | null = null;
  isImageLoading = false;
  imageLoadError = false;
  categoryOptions: string[] = [];
  private destroy$ = new Subject<void>();

  constructor(
    private _medicineService: MedicineServiceProxy,
    private snackBar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute,
    private _fileService: FileServiceProxy,
    private dialog: MatDialog
  ) { }
  ngOnInit(): void {
    this.medicine.price = 0;
    this.medicine.stockQuantity = 0;
    this.loadCategories();

    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['id']) {
        this.medicineId = params['id'];
        this.isEditing = true;
        this.pageTitle = 'Edit Medicine';
        if (this.medicineId) this.loadMedicineDetails(this.medicineId);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadCategories(): void {
    this._medicineService.categories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (categories) => this.categoryOptions = categories || [],
        error: () => this.categoryOptions = ['Pain Relief', 'Antibiotics', 'Vitamins', 'Cold & Flu', 'Others']
      });
  }

  openAddCategoryDialog(): void {
    const dialogRef = this.dialog.open(AddCategoryDialogComponent, {
      width: '450px',
      disableClose: true,
      data: { existingCategories: this.categoryOptions }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Try to save the category to backend first
        this.saveCategoryToBackend(result);
      }
    });
  }

  private saveCategoryToBackend(categoryName: string): void {
    // Here you would typically call an API to save the category
    // For now, we'll just add it locally
    // this._medicineService.addCategory(categoryName).subscribe({
    //   next: () => {
    //     this.addCategoryLocally(categoryName);
    //   },
    //   error: (error) => {
    //     this.showSnackbar('Failed to save category. Adding locally.', true);
    //     this.addCategoryLocally(categoryName);
    //   }
    // });
    
    // For now, just add locally
    this.addCategoryLocally(categoryName);
  }

  private addCategoryLocally(categoryName: string): void {
    // Add the new category to the options
    this.categoryOptions.push(categoryName);
    // Sort categories alphabetically
    this.categoryOptions.sort();
    // Select the newly added category
    this.medicine.category = categoryName;
    this.showSnackbar(`Category "${categoryName}" added successfully`);
  }

  private mapMedicineData = (data: any): CreateMedicineRequest =>
    new CreateMedicineRequest({...data, strength: data.dosage || '', isGeneric: false, brandName: data.name, genericName: data.name, manufacturerDate: undefined, tags: data.category || ''});

  loadMedicineDetails(id: string): void {
    this.isLoading = true;
    this._medicineService.getById(id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data: Medicine) => {
          this.medicine = this.mapMedicineData(data);
          this.previousImageName = this.medicine.imageUrl || null;
          if (this.medicine.expiryDate) {
            this.medicine.expiryDate = new Date(this.medicine.expiryDate.toString()).toISOString().split('T')[0] as any;
          }
          if (this.medicine.imageUrl) this.loadImage(this.medicine.imageUrl);
          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
          this.showSnackbar('Error loading medicine details', true);
        }
      });
  }

  private showSnackbar = (message: string, isError = false): void => {
    this.snackBar.open(message, 'Close', { duration: 3000, panelClass: isError ? 'error-snackbar' : 'success-snackbar' });
  }

  onFileSelected = (event: Event): void => {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      this.selectedFile = input.files[0];
      this.createImagePreview(this.selectedFile);
      this.uploadFile();
    }
  }

  private createImagePreview(file: File): void {
    if (!file.type.startsWith('image/')) {
      this.uploadError = 'Please select an image file';
      return;
    }
    const reader = new FileReader();
    reader.onload = () => this.imagePreview = reader.result;
    reader.readAsDataURL(file);
  } private loadImage(filename: string): void {
    this.isImageLoading = true;
    this.imageLoadError = false;

    fetch(`${this.baseUrl}/api/File/Getfile/${filename}`)
      .then(response => response.ok ? response.blob() : Promise.reject())
      .then(blob => {
        const reader = new FileReader();
        reader.onload = () => {
          this.imagePreview = reader.result;
          this.isImageLoading = false;
        };
        reader.readAsDataURL(blob);
      })
      .catch(() => {
        this.imageLoadError = true;
        this.isImageLoading = false;
        this.showSnackbar('Error loading image', true);
      });
  }

  removeImage = (): void => {
    this.imagePreview = null;
    this.selectedFile = null;
    this.medicine.imageUrl = undefined;
    this.fileInput.nativeElement.value = '';
    this.showSnackbar('Image removed');
  }

  private uploadFile(): void {
    if (!this.selectedFile) {
      this.uploadError = 'Please select a file first';
      return;
    }

    this.isUploading = true;
    this.uploadError = '';

    this._fileService.upload('medicine', [{ data: this.selectedFile, fileName: this.selectedFile.name }])
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: ResponseMessage) => {
          this.isUploading = false;
          if (!response.isError && response.message) {
            const fileNames = response.message.split(', ');
            if (fileNames.length > 0) {
              this.medicine.imageUrl = fileNames[0];
              this.loadImage(this.medicine.imageUrl);
              this.showSnackbar('Image uploaded successfully');
            }
          } else {
            this.uploadError = response.message || 'Upload failed';
            this.showSnackbar(this.uploadError, true);
          }
        },
        error: () => {
          this.isUploading = false;
          this.uploadError = 'Failed to upload image. Please try again.';
          this.showSnackbar(this.uploadError, true);
        }
      });
  } onSubmit = (): void => {
    if (this.medicineForm?.invalid) {
      Object.keys(this.medicineForm.controls).forEach(key => this.medicineForm.controls[key].markAsTouched());
      return;
    }
    if (this.isUploading) return this.showSnackbar('Please wait for the image to finish uploading', true);

    this.isLoading = true;
    const request = this.mapMedicineData(this.medicine as any);

    if (this.medicine.expiryDate) {
      request.expiryDate = typeof this.medicine.expiryDate === 'string'
        ? DateTime.fromJSDate(new Date(this.medicine.expiryDate))
        : this.medicine.expiryDate;
    }

    const operation = this.isEditing && this.medicineId
      ? this._medicineService.update(this.medicineId, request)
      : this._medicineService.create(request);

    operation.pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.isLoading = false;
        this.showSnackbar(`Medicine ${this.isEditing ? 'updated' : 'added'} successfully`);
        this.router.navigate(['/medicine/list']);
      },
      error: () => {
        this.isLoading = false;
        this.showSnackbar(`Error ${this.isEditing ? 'updating' : 'adding'} medicine`, true);
      }
    });
  }

  goBack = (): void => { this.router.navigate(['/medicine/list']); };
}
