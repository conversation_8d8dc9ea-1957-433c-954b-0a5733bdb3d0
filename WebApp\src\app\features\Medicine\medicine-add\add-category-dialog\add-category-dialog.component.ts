import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
  selector: 'app-add-category-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule
  ],
  templateUrl: './add-category-dialog.component.html',
  styleUrls: ['./add-category-dialog.component.css']
})
export class AddCategoryDialogComponent {
  categoryName: string = '';
  showError: boolean = false;
  errorMessage: string = '';
  isSubmitting: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<AddCategoryDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { existingCategories: string[] }
  ) {
    // Auto-focus the input after dialog opens
    setTimeout(() => {
      const input = document.getElementById('categoryName') as HTMLInputElement;
      if (input) {
        input.focus();
      }
    }, 100);
  }

  onSave(): void {
    const trimmedName = this.categoryName.trim();
    
    if (!trimmedName) {
      this.showError = true;
      this.errorMessage = 'Category name is required';
      return;
    }

    if (trimmedName.length < 2) {
      this.showError = true;
      this.errorMessage = 'Category name must be at least 2 characters';
      return;
    }

    if (trimmedName.length > 50) {
      this.showError = true;
      this.errorMessage = 'Category name must not exceed 50 characters';
      return;
    }

    // Check for special characters (allow only letters, numbers, spaces, hyphens, and apostrophes)
    const validNamePattern = /^[a-zA-Z0-9\s\-'&]+$/;
    if (!validNamePattern.test(trimmedName)) {
      this.showError = true;
      this.errorMessage = 'Category name contains invalid characters';
      return;
    }

    // Check if category already exists (case-insensitive)
    const existsAlready = this.data.existingCategories.some(
      category => category.toLowerCase() === trimmedName.toLowerCase()
    );

    if (existsAlready) {
      this.showError = true;
      this.errorMessage = 'This category already exists';
      return;
    }

    this.showError = false;
    this.isSubmitting = true;

    // Simulate API call delay for better UX
    setTimeout(() => {
      // Capitalize first letter of each word
      const formattedName = trimmedName.replace(/\b\w/g, l => l.toUpperCase());
      this.dialogRef.close(formattedName);
    }, 500);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  // Handle Enter key in input
  onKeyUp(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !this.isSubmitting) {
      this.onSave();
    } else if (event.key === 'Escape') {
      this.onCancel();
    }
  }
}
