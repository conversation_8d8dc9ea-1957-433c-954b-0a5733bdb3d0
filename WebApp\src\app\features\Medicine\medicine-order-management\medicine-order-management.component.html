<div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-5">
  <div class="">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-5">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Medicine Order Management</h1>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
        Manage and track all medicine orders from patients
      </p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6 mb-8">
      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Orders</dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{orderStats.total}}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Pending</dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{orderStats.pending}}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Processing</dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{orderStats.processing}}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l2.414 2.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Shipped</dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{orderStats.shipped}}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Delivered</dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-white">{{orderStats.delivered}}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Revenue</dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-white">${{orderStats.totalRevenue.toFixed(2)}}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Search Orders
          </label>
          <input type="text" [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()"
            placeholder="Search by patient, medicine, tracking..."
            class="block w-full px-3 py-2 border border-gray-300 outline-none dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
        </div>

        <!-- Status Filter -->
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Status
          </label>
          <div class="relative">
            <select [(ngModel)]="selectedStatus" (ngModelChange)="onStatusFilterChange()"
              class="block w-full px-3 pr-12 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white appearance-none bg-white">
              <option *ngFor="let status of statusOptions" [value]="status">{{status}}</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
              <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Date Range Filter -->
        <div>
          <label for="dateRange" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Date Range
          </label>
          <div class="relative">
            <select [(ngModel)]="dateRange" (ngModelChange)="onDateRangeChange()"
              class="block w-full px-3 pr-12 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white appearance-none bg-white">
              <option *ngFor="let range of dateRangeOptions" [value]="range">{{range}}</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
              <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Export Button -->
        <div class="flex items-end">
          <button (click)="exportOrders()"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
            Export CSV
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Orders Table -->
    <div *ngIf="!isLoading" class="bg-white dark:bg-gray-800 shadow overflow-auto sm:rounded-md">
      <div class="px-4 py-5 sm:p-6">
        <div class="table-container overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 orders-table">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Order Details
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Patient
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Medicine
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr *ngFor="let order of paginatedOrders" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <!-- Order Details -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    #{{order.id.substring(0, 8)}}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{formatDateTime(order.orderDate)}}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{getRelativeTime(order.orderDate)}}
                  </div>
                </td>

                <!-- Patient -->
                <td class="px-6 py-4">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{order.patientEmail}}
                  </div>
                  <div *ngIf="order.patientName" class="text-sm text-gray-500 dark:text-gray-400">
                    {{order.patientName}}
                  </div>
                </td>

                <!-- Order Items -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    Order #{{order.orderNumber}}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    {{order.orderItems.length || 0}} items
                  </div>
                </td>

                <!-- Status -->
                <td class="px-6 py-4 whitespace-nowrap" style="position: relative; z-index: 1;">
                  <div class="status-dropdown-container relative"
                       [class.dropdown-open]="showStatusDropdown[order.id]"
                       style="z-index: 999999;">
                    <!-- Status Badge (clickable) -->
                    <span
                      class="relative top-0 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                      [class]="getStatusColor(order.status) + ' cursor-pointer hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1'"
                      (click)="toggleStatusDropdown(order.id, $event)"
                      (keydown)="onStatusBadgeKeyDown($event, order.id)"
                      [attr.aria-label]="'Change status from ' + order.status"
                      [attr.aria-expanded]="showStatusDropdown[order.id]"
                      role="button"
                      tabindex="0"
                      style="z-index: 999999; position: relative;"
                      [attr.data-order-id]="order.id">
                      {{order.status}}
                      <svg class="ml-1.5 h-3.5 w-3.5 transition-transform duration-200"
                           [class.rotate-180]="showStatusDropdown[order.id]"
                           fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </span>
                  </div>
                </td>

                <!-- Actions -->
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button (click)="viewOrderDetails(order)"
                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                    View Details
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div *ngIf="paginatedOrders.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No orders found</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            No medicine orders match your current filters.
          </p>
        </div>
      </div>
    </div>

    <!-- Pagination Controls - Place this AFTER the table, inside the main container -->
    <div *ngIf="filteredOrders.length > 0"
      class="bg-gray-50 dark:bg-gray-900 px-6 py-4 border-t border-gray-200 dark:border-gray-700 transition-colors duration-300 z-0">
      <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
        <!-- Results Info -->
        <div class="text-sm text-gray-700 dark:text-gray-300 order-2 sm:order-1">
          Showing <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> to
          <span class="font-medium">{{ Math.min(currentPage * pageSize, filteredOrders.length) }}</span> of
          <span class="font-medium">{{ filteredOrders.length }}</span> orders
        </div>

        <!-- Pagination Navigation -->
        <div class="flex items-center space-x-2 order-1 sm:order-2">
          <!-- Page Size Selector -->
          <div class="flex items-center mr-3">
            <label for="order-page-size" class="text-sm text-gray-600 dark:text-gray-400 mr-2 whitespace-nowrap">Show:</label>
            <select id="order-page-size" [(ngModel)]="pageSize" (change)="onPageSizeChange($event)"
              class="min-w-[60px] py-1 px-2 rounded-md border border-gray-300 dark:border-gray-600 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-colors">
              <option *ngFor="let size of [5,10,20,50]" [value]="size">{{ size }}</option>
            </select>
          </div>

          <!-- Previous Button -->
          <button (click)="previousPage()" [disabled]="currentPage === 1"
            class="w-8 h-8 flex items-center justify-center rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>

          <!-- First Page Button (if not in view) -->
          <button *ngIf="paginationRange[0] > 1" (click)="goToPage(1)"
            class="w-8 h-8 flex items-center justify-center rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 transition-colors">
            1
          </button>

          <!-- Ellipsis (if needed) -->
          <span *ngIf="paginationRange[0] > 2" class="px-1 text-gray-400 dark:text-gray-500">…</span>

          <!-- Page Numbers -->
          <button *ngFor="let page of paginationRange" (click)="goToPage(page)"
            class="w-8 h-8 flex items-center justify-center rounded-md mx-0.5 border transition-colors"
            [ngClass]="page === currentPage
              ? 'bg-blue-600 text-white border-blue-600 font-medium'
              : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 border-gray-300 dark:border-gray-600'">
            {{ page }}
          </button>

          <!-- Ellipsis (if needed) -->
          <span *ngIf="paginationRange[paginationRange.length - 1] < totalPages - 1" class="px-1 text-gray-400 dark:text-gray-500">…</span>

          <!-- Last Page Button (if not in view) -->
          <button *ngIf="paginationRange[paginationRange.length - 1] < totalPages" (click)="goToPage(totalPages)"
            class="w-8 h-8 flex items-center justify-center rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 transition-colors">
            {{ totalPages }}
          </button>

          <!-- Next Button -->
          <button (click)="nextPage()" [disabled]="currentPage === totalPages"
            class="w-8 h-8 flex items-center justify-center rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Smart Status Dropdown Portal - Positions above or below based on available space -->
<div *ngFor="let order of paginatedOrders">
  <div *ngIf="showStatusDropdown[order.id]"
       class="fixed w-48 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-600 py-1"
       [style.top.px]="getDropdownTop(order.id)"
       [style.left.px]="getDropdownLeft(order.id)"
       style="z-index: 999999;">

    <!-- Arrow indicator for above positioning -->
    <div *ngIf="getDropdownDirection(order.id) === 'above'"
         class="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-white dark:border-t-gray-800"
         style="position: absolute; bottom: -8px; left: 20px;"></div>

    <!-- Arrow indicator for below positioning -->
    <div *ngIf="getDropdownDirection(order.id) === 'below'"
         class="w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-white dark:border-b-gray-800"
         style="position: absolute; top: -8px; left: 20px;"></div>

    <button *ngFor="let statusOption of statusOptions.slice(1)"
            (click)="updateStatusFromDropdown(order, statusOption)"
            class="w-full text-left px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors duration-150 rounded-lg mx-1">
      <span class="w-3 h-3 rounded-full mr-3 inline-block"
            [class]="getStatusIndicatorColor(statusOption)"></span>
      {{ statusOption }}
    </button>
  </div>
</div>
