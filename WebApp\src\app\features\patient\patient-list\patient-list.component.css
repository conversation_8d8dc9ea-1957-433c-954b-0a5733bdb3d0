/* Patient List Component Styles */
.patient-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.patient-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px -5px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

/* Hover effect for clickable cards */
.patient-card:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02), rgba(99, 102, 241, 0.02));
  pointer-events: none;
}

/* Action buttons styling */
.patient-card .action-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.patient-card .action-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced hover effects for edit/delete buttons */
.patient-card:hover .action-button {
  animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.sort-button {
  transition: all 0.2s ease-in-out;
}

/* .sort-button:hover {
  background-color: #f3f4f6;
} */

.pagination-button {
  transition: all 0.2s ease-in-out;
}

.pagination-button:hover {
  background-color: #3b82f6;
  color: white;
}

.pagination-button.active {
  background-color: #3b82f6;
  color: white;
}

.action-button {
  transition: all 0.2s ease-in-out;
}

.action-button:hover {
  transform: scale(1.05);
}

.stats-card {
  transition: all 0.2s ease-in-out;
}

.stats-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.patient-avatar {
  transition: all 0.2s ease-in-out;
}

.patient-avatar:hover {
  transform: scale(1.1);
}
