import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export type Theme = 'light' | 'dark' | 'auto';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'selected-theme';
  private currentThemeSubject = new BehaviorSubject<Theme>('light');
  public currentTheme$: Observable<Theme> = this.currentThemeSubject.asObservable();

  constructor() {
    this.loadTheme();
    this.setupSystemThemeListener();
  }

  /**
   * Set the application theme
   */
  setTheme(theme: Theme): void {
    this.currentThemeSubject.next(theme);
    this.saveTheme(theme);
    this.applyTheme(theme);
  }

  /**
   * Get the current theme
   */
  getCurrentTheme(): Theme {
    return this.currentThemeSubject.value;
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    const currentTheme = this.getCurrentTheme();
    if (currentTheme === 'light') {
      this.setTheme('dark');
    } else if (currentTheme === 'dark') {
      this.setTheme('light');
    } else {
      // If auto, set to opposite of current system preference
      const isDarkMode = this.getSystemThemePreference() === 'dark';
      this.setTheme(isDarkMode ? 'light' : 'dark');
    }
  }

  /**
   * Check if dark mode is currently active
   */
  isDarkMode(): boolean {
    const theme = this.getCurrentTheme();
    if (theme === 'auto') {
      return this.getSystemThemePreference() === 'dark';
    }
    return theme === 'dark';
  }

  /**
   * Load theme from localStorage or use system preference
   */
  private loadTheme(): void {
    const savedTheme = localStorage.getItem(this.THEME_KEY) as Theme;
    const theme = savedTheme || 'auto';
    this.currentThemeSubject.next(theme);
    this.applyTheme(theme);
  }

  /**
   * Save theme to localStorage
   */
  private saveTheme(theme: Theme): void {
    localStorage.setItem(this.THEME_KEY, theme);
  }

  /**
   * Apply the theme to the document
   */
  private applyTheme(theme: Theme): void {
    const isDark = theme === 'dark' || (theme === 'auto' && this.getSystemThemePreference() === 'dark');

    if (isDark) {
      document.documentElement.classList.add('dark');
      document.documentElement.setAttribute('data-theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      document.documentElement.setAttribute('data-theme', 'light');
    }

    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(isDark);
  }

  /**
   * Get system theme preference
   */
  private getSystemThemePreference(): 'light' | 'dark' {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }

  /**
   * Setup listener for system theme changes
   */
  private setupSystemThemeListener(): void {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', () => {
        if (this.getCurrentTheme() === 'auto') {
          this.applyTheme('auto');
        }
      });
    }
  }

  /**
   * Update meta theme-color for mobile browsers
   */
  private updateMetaThemeColor(isDark: boolean): void {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');

    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.setAttribute('name', 'theme-color');
      document.head.appendChild(metaThemeColor);
    }

    const color = isDark ? '#1f2937' : '#ffffff';
    metaThemeColor.setAttribute('content', color);
  }

  /**
   * Get theme display name
   */
  getThemeDisplayName(theme: Theme): string {
    switch (theme) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'auto':
        return 'Auto';
      default:
        return 'Light';
    }
  }

  /**
   * Get all available themes
   */
  getAvailableThemes(): Theme[] {
    return ['light', 'dark', 'auto'];
  }
}
