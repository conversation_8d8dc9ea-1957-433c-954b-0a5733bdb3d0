import { Routes } from '@angular/router';
import { AuthGuard, GuestGuard } from './guards/auth.guard';
import { MainLayoutComponent } from './shared/layout/main-layout/main-layout.component';

export const routes: Routes = [
  // Redirect root to dashboard
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full',
  },

  // Auth routes (guest only)
  {
    path: 'auth',
    canActivate: [GuestGuard],
    children: [
      {
        path: 'login',
        loadComponent: () =>
          import('./features/auth/login/login.component').then(
            (m) => m.LoginComponent
          ),
      }, {
        path: 'register',
        loadComponent: () =>
          import('./features/auth/register/register.component').then(
            (m) => m.RegisterComponent
          ),
      },
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full',
      },
    ],
  },

  // Protected routes with layout
  {
    path: '',
    component: MainLayoutComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'dashboard',
        loadComponent: () =>
          import('./features/dashboard/dashboard.component').then(
            (m) => m.DashboardComponent
          ),
      },
      {
        path: 'profile',
        loadComponent: () =>
          import('./features/user/profile/profile.component').then(
            (m) => m.ProfileComponent
          ),
      }, {
        path: 'users',
        loadComponent: () =>
          import('./features/user/user-list/user-list.component').then(
            (m) => m.UserListComponent
          ),
        data: { roles: ['Admin'] }, // Updated to use roles instead of permissions
      }, {
        path: 'request',
        loadComponent: () =>
          import('./features/user/user-request/user-request.component').then(
            (m) => m.UserRequestComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Only Admin and Doctor can manage requests
      },
      {
        path: 'request/:id',
        loadComponent: () =>
          import('./features/user/user-request/user-request-view/user-request-view.component').then(
            (m) => m.UserRequestViewComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Only Admin and Doctor can view request details
      }, {
        path: 'users/add',
        loadComponent: () =>
          import('./features/user/user-add/user-add.component').then(
            (m) => m.UserAddComponent
          ),
        data: { roles: ['Admin'], adminOnly: true }, // Admin only route
      },
      // Educational routes
      {
        path: 'educational/list',
        loadComponent: () =>
          import('./features/Educational/educational-list/educational-list.component').then(
            (m) => m.EducationalListComponent
          ),
      },
      {
        path: 'educational/add',
        loadComponent: () =>
          import('./features/Educational/educational-add/educational-add.component').then(
            (m) => m.EducationalAddComponent
          ),
        data: { roles: ['Admin'], adminOnly: true }, // Admin only route
      },
      {
        path: 'educational/edit/:id',
        loadComponent: () =>
          import('./features/Educational/educational-add/educational-add.component').then(
            (m) => m.EducationalAddComponent
          ),
        data: { roles: ['Admin'], adminOnly: true }, // Admin only route
      },
      {
        path: 'educational/detail/:id',
        loadComponent: () =>
          import('./features/Educational/educational-detail/educational-detail.component').then(
            (m) => m.EducationalDetailComponent
          ),
      },
      {
        path: 'educational',
        redirectTo: 'educational/list',
        pathMatch: 'full'
      },
      // Medicine routes
      {
        path: 'medicine/list',
        loadComponent: () =>
          import('./features/Medicine/medicine-list/medicine-list.component').then(
            (m) => m.MedicineListComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Admin and Doctor can manage medicines
      }, {
        path: 'medicine/add',
        loadComponent: () =>
          import('./features/Medicine/medicine-add/medicine-add.component').then(
            (m) => m.MedicineAddComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Admin and Doctor can add medicines
      }, {
        path: 'medicine/orders',
        loadComponent: () =>
          import('./features/Medicine/medicine-order-management/medicine-order-management.component').then(
            (m) => m.MedicineOrderManagementComponent
          ),
        data: { roles: ['Admin'] }, // Only Admin can manage medicine orders (business logic)
      },
      {
        path: 'medicine/orders/:id',
        loadComponent: () =>
          import('./features/Medicine/medicine-order/medicine-order-detail/medicine-order-detail.component').then(
            (m) => m.MedicineOrderDetailComponent
          ),
        data: { roles: ['Admin'] }, // Only Admin can view order details
      },
      {
        path: 'medicine/edit/:id',
        loadComponent: () =>
          import('./features/Medicine/medicine-add/medicine-add.component').then(
            (m) => m.MedicineAddComponent
          ),
        data: { roles: ['Admin'], adminOnly: true }, // Admin only route
      },

      // Appointment routes
      {
        path: 'appointments',
        loadComponent: () =>
          import('./features/appointments/appointment-list/appointment-list.component').then(
            (m) => m.AppointmentListComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Only Admin and Doctor can manage appointments
      },
      {
        path: 'appointments/add',
        loadComponent: () =>
          import('./features/appointments/appointment-add/appointment-add.component').then(
            (m) => m.AppointmentAddComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Only Admin and Doctor can create appointments
      },
      {
        path: 'appointments/edit/:id',
        loadComponent: () =>
          import('./features/appointments/appointment-add/appointment-add.component').then(
            (m) => m.AppointmentAddComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Only Admin and Doctor can edit appointments
      },
      {
        path: 'appointments/detail/:id',
        loadComponent: () =>
          import('./features/appointments/appointment-detail/appointment-detail.component').then(
            (m) => m.AppointmentDetailComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Only Admin and Doctor can view appointment details
      },

      // Patient routes
      {
        path: 'patients',
        loadComponent: () =>
          import('./features/patient/patient-list/patient-list.component').then(
            (m) => m.PatientListComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Only Admin and Doctor can view patients
      },
      // Patient add/edit routes removed - patients are auto-created when users register through mobile app
      {
        path: 'patients/detail/:id',
        loadComponent: () =>
          import('./features/patient/patient-detail/patient-detail.component').then(
            (m) => m.PatientDetailComponent
          ),
        data: { roles: ['Admin', 'Doctor'] }, // Only Admin and Doctor can view patient details
      },

      // Chat routes
      {
        path: 'chat',
        loadComponent: () =>
          import('./features/chat/chat-container/chat-container.component').then(
            (m) => m.ChatContainerComponent
          ),
        data: { roles: ['Admin', 'Doctor', 'User'] }, // All authenticated users can access chat
      },
      {
        path: 'chat/:doctorId/:patientId',
        loadComponent: () =>
          import('./features/chat/chat-container/chat-container.component').then(
            (m) => m.ChatContainerComponent
          ),
        data: { roles: ['Admin', 'Doctor', 'User'] }, // All authenticated users can access chat
      },
    ],
  },

  // Unauthorized page
  {
    path: 'unauthorized',
    loadComponent: () =>
      import('./shared/pages/unauthorized/unauthorized.component').then(
        (m) => m.UnauthorizedComponent
      ),
  },

  // Wildcard route - must be last
  {
    path: '**',
    redirectTo: '/users',
  },
];
