/* Progress Picture Comparison Component Styles */

.progress-comparison-container {
  max-width: 100%;
  margin: 0 auto;
}

.comparison-container {
  position: relative;
  user-select: none;
  cursor: grab;
}

.comparison-container:active {
  cursor: grabbing;
}

.comparison-container.panning {
  cursor: grabbing;
}

/* Timeline styles */
.timeline-container {
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.timeline-container::-webkit-scrollbar {
  height: 6px;
}

.timeline-container::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Picture selection grid */
.picture-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.picture-thumbnail {
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
}

.picture-thumbnail:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.picture-thumbnail.selected {
  box-shadow: 0 0 0 3px #3b82f6;
}

.picture-thumbnail.before-selected {
  box-shadow: 0 0 0 3px #10b981;
}

.picture-thumbnail.after-selected {
  box-shadow: 0 0 0 3px #3b82f6;
}

/* Comparison modes */
.side-by-side-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  height: 100%;
  min-height: 400px;
}

.comparison-side {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comparison-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

/* Overlay mode */
.overlay-container {
  position: relative;
  height: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-image {
  position: absolute;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Slider mode */
.slider-container {
  position: relative;
  height: 100%;
  min-height: 400px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-image {
  position: absolute;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.slider-divider {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #ffffff, #e5e7eb, #ffffff);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  z-index: 10;
  cursor: ew-resize;
}

.slider-handle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: ew-resize;
}

/* Labels and overlays */
.image-label {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  z-index: 5;
}

.image-label.before {
  background-color: #10b981;
}

.image-label.after {
  background-color: #3b82f6;
}

.image-timestamp {
  position: absolute;
  bottom: 8px;
  left: 8px;
  padding: 2px 6px;
  background: rgba(0, 0, 0, 0.75);
  color: white;
  border-radius: 4px;
  font-size: 11px;
  z-index: 5;
}

/* Controls */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-level {
  min-width: 60px;
  text-align: center;
  font-size: 14px;
  color: #6b7280;
}

/* Measurement tools */
.measurement-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 15;
}

.measurement-tool {
  position: absolute;
  pointer-events: auto;
}

.measurement-line {
  stroke: #ef4444;
  stroke-width: 2;
  fill: none;
}

.measurement-point {
  fill: #ef4444;
  stroke: white;
  stroke-width: 2;
}

.measurement-label {
  background: #ef4444;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

/* Analysis panel */
.analysis-panel {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-indicator.positive {
  color: #10b981;
}

.progress-indicator.negative {
  color: #ef4444;
}

.progress-indicator.stable {
  color: #6b7280;
}

/* Responsive design */
@media (max-width: 768px) {
  .side-by-side-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr;
  }
  
  .picture-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  }
  
  .zoom-controls {
    flex-wrap: wrap;
    gap: 4px;
  }
}

/* Fullscreen styles */
.comparison-container:fullscreen {
  background: black;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comparison-container:fullscreen .comparison-image,
.comparison-container:fullscreen .overlay-image,
.comparison-container:fullscreen .slider-image {
  max-width: 90vw;
  max-height: 90vh;
}

/* Loading states */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles */
.picture-thumbnail:focus,
.slider-handle:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
