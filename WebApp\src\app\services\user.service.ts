import { Injectable } from '@angular/core';
import { Observable, of, throwError, BehaviorSubject } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import {
  User,
  Role,
  UpdateUserRequest,
  AssignRoleRequest,
  CreateUserRequest,
} from '../models/user.model';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private usersSubject = new BehaviorSubject<User[]>([]);
  public users$ = this.usersSubject.asObservable();

  constructor() {
    this.loadMockUsers();
  }

  private loadMockUsers(): void {
    // Mock users data
    const mockUsers: User[] = [
      {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        username: 'admin',
        isActive: true,
        roles: [
          {
            id: '1',
            name: 'Admin',
            description: 'System Administrator',
            permissions: [
              {
                id: '1',
                name: 'Manage Users',
                description: 'Can manage all users',
                resource: 'user',
                action: 'manage',
                isActive: true,
              },
              {
                id: '2',
                name: 'Manage Roles',
                description: 'Can manage all roles',
                resource: 'role',
                action: 'manage',
                isActive: true,
              },
            ],
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '2',
        email: '<EMAIL>',
        firstName: 'Manager',
        lastName: 'User',
        username: 'manager',
        isActive: true,
        roles: [
          {
            id: '2',
            name: 'Manager',
            description: 'Team Manager',
            permissions: [
              {
                id: '3',
                name: 'View Users',
                description: 'Can view users',
                resource: 'user',
                action: 'read',
                isActive: true,
              },
              {
                id: '4',
                name: 'Update Users',
                description: 'Can update users',
                resource: 'user',
                action: 'update',
                isActive: true,
              },
            ],
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '3',
        email: '<EMAIL>',
        firstName: 'Regular',
        lastName: 'User',
        username: 'user',
        isActive: true,
        roles: [
          {
            id: '3',
            name: 'User',
            description: 'Regular User',
            permissions: [
              {
                id: '5',
                name: 'View Dashboard',
                description: 'Can view dashboard',
                resource: 'dashboard',
                action: 'read',
                isActive: true,
              },
            ],
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    this.usersSubject.next(mockUsers);
  }

  getAllUsers(): Observable<User[]> {
    return this.users$;
  }

  getUserById(id: string): Observable<User | null> {
    return this.users$.pipe(
      map((users) => users.find((user) => user.id === id) || null)
    );
  }

  createUser(userData: CreateUserRequest): Observable<User> {
    return new Observable((observer) => {
      setTimeout(() => {
        const users = this.usersSubject.value;

        // Check if email or username already exists
        const emailExists = users.some(
          (user) => user.email.toLowerCase() === userData.email.toLowerCase()
        );
        const usernameExists = users.some(
          (user) =>
            user.username.toLowerCase() === userData.username.toLowerCase()
        );

        if (emailExists) {
          observer.error({ message: 'Email address already exists' });
          return;
        }

        if (usernameExists) {
          observer.error({ message: 'Username already exists' });
          return;
        }

        if (userData.password !== userData.confirmPassword) {
          observer.error({ message: 'Passwords do not match' });
          return;
        }

        // Mock role assignment - in real app, fetch roles by IDs
        const mockRoles = [
          {
            id: '3',
            name: 'User',
            description: 'Regular User',
            permissions: [
              {
                id: '9',
                name: 'View Dashboard',
                description: 'Can access dashboard',
                resource: 'dashboard',
                action: 'read',
                isActive: true,
              },
            ],
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ];

        const newUser: User = {
          id: Math.random().toString(36).substring(2, 11),
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          username: userData.username,
          isActive: userData.isActive,
          roles: mockRoles, // In real app, map roleIds to actual roles
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        users.push(newUser);
        this.usersSubject.next([...users]);

        observer.next(newUser);
        observer.complete();
      }, 1000);
    });
  }

  updateUser(id: string, userData: UpdateUserRequest): Observable<User> {
    return new Observable((observer) => {
      setTimeout(() => {
        const users = this.usersSubject.value;
        const userIndex = users.findIndex((user) => user.id === id);

        if (userIndex === -1) {
          observer.error({ message: 'User not found' });
          return;
        }

        const updatedUser = {
          ...users[userIndex],
          ...userData,
          updatedAt: new Date(),
        };

        users[userIndex] = updatedUser;
        this.usersSubject.next([...users]);

        observer.next(updatedUser);
        observer.complete();
      }, 500);
    });
  }

  deleteUser(id: string): Observable<boolean> {
    return new Observable((observer) => {
      setTimeout(() => {
        const users = this.usersSubject.value;
        const filteredUsers = users.filter((user) => user.id !== id);

        if (filteredUsers.length === users.length) {
          observer.error({ message: 'User not found' });
          return;
        }

        this.usersSubject.next(filteredUsers);
        observer.next(true);
        observer.complete();
      }, 500);
    });
  }

  toggleUserStatus(id: string): Observable<User> {
    return new Observable((observer) => {
      setTimeout(() => {
        const users = this.usersSubject.value;
        const userIndex = users.findIndex((user) => user.id === id);

        if (userIndex === -1) {
          observer.error({ message: 'User not found' });
          return;
        }

        const updatedUser = {
          ...users[userIndex],
          isActive: !users[userIndex].isActive,
          updatedAt: new Date(),
        };

        users[userIndex] = updatedUser;
        this.usersSubject.next([...users]);

        observer.next(updatedUser);
        observer.complete();
      }, 500);
    });
  }

  assignRoles(request: AssignRoleRequest): Observable<User> {
    return new Observable((observer) => {
      setTimeout(() => {
        const users = this.usersSubject.value;
        const userIndex = users.findIndex((user) => user.id === request.userId);

        if (userIndex === -1) {
          observer.error({ message: 'User not found' });
          return;
        }

        // Get all roles from role service for mock purposes
        // In a real app, we would make an API call to assign roles
        this.getRolesFromIds(request.roleIds).subscribe({
          next: (roles) => {
            const updatedUser = {
              ...users[userIndex],
              roles: roles,
              updatedAt: new Date(),
            };

            users[userIndex] = updatedUser;
            this.usersSubject.next([...users]);

            observer.next(updatedUser);
            observer.complete();
          },
          error: (error) => {
            observer.error(error);
          }
        });
      }, 500);
    });
  }

  // Helper method to get role objects from role IDs
  private getRolesFromIds(roleIds: string[]): Observable<Role[]> {
    return new Observable((observer) => {
      // In a real app, this would be a call to the backend
      // For the mock implementation, we'll simulate retrieving roles by IDs

      const allRoles = [
        {
          id: '1',
          name: 'Admin',
          description: 'System Administrator',
          permissions: [
            {
              id: '1',
              name: 'Manage Users',
              description: 'Can manage all users',
              resource: 'user',
              action: 'manage',
              isActive: true,
            },
            {
              id: '2',
              name: 'Manage Roles',
              description: 'Can manage all roles',
              resource: 'role',
              action: 'manage',
              isActive: true,
            },
          ],
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          name: 'Manager',
          description: 'Team Manager',
          permissions: [
            {
              id: '3',
              name: 'View Users',
              description: 'Can view users',
              resource: 'user',
              action: 'read',
              isActive: true,
            },
            {
              id: '4',
              name: 'Update Users',
              description: 'Can update users',
              resource: 'user',
              action: 'update',
              isActive: true,
            },
          ],
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '3',
          name: 'User',
          description: 'Regular User',
          permissions: [
            {
              id: '5',
              name: 'View Dashboard',
              description: 'Can view dashboard',
              resource: 'dashboard',
              action: 'read',
              isActive: true,
            },
          ],
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '4',
          name: 'Support',
          description: 'Support Staff',
          permissions: [
            {
              id: '6',
              name: 'View Users',
              description: 'Can view users',
              resource: 'user',
              action: 'read',
              isActive: true,
            },
          ],
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ];

      const roles = allRoles.filter(role => roleIds.includes(role.id));

      if (roles.length === 0 && roleIds.length > 0) {
        observer.error({ message: 'No roles found for the provided IDs' });
        return;
      }

      observer.next(roles);
      observer.complete();
    });
  }

  searchUsers(query: string): Observable<User[]> {
    return this.users$.pipe(
      map((users) =>
        users.filter(
          (user) =>
            user.firstName.toLowerCase().includes(query.toLowerCase()) ||
            user.lastName.toLowerCase().includes(query.toLowerCase()) ||
            user.email.toLowerCase().includes(query.toLowerCase()) ||
            user.username.toLowerCase().includes(query.toLowerCase())
        )
      )
    );
  }
}
