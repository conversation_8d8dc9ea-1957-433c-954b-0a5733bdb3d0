import { Component, OnInit, ViewChild, ElementRef, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { AppointmentMessage, AppointmentMessageServiceProxy, FileServiceProxy, FileParameter, ResponseMessage } from '../../../../shared/service-proxies/service-proxies';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { DateTime } from 'luxon';
import { AuthService } from '../../../../shared/services/auth.service';

@Component({
  selector: 'app-appointment-add',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatSnackBarModule
  ],
  templateUrl: './appointment-add.component.html',
  styleUrl: './appointment-add.component.css'
})
export class AppointmentAddComponent implements OnInit, OnDestroy {
  @ViewChild('appointmentForm') appointmentForm!: NgForm;
  @ViewChild('fileInput') fileInput!: ElementRef;

  // Base URL for API calls
  baseUrl: string = getRemoteServiceBaseUrl();

  appointment = new AppointmentMessage();
  isLoading = false;
  isEditing = false;
  appointmentId?: string;
  pageTitle = 'Schedule New Appointment';
  isAdmin = false;
  currentUser: any;

  // File upload properties
  selectedFile: File | null = null;
  isUploading = false;
  uploadError = '';
  imagePreview: string | ArrayBuffer | null = null;
  previousImageName: string | null = null;
  isImageLoading = false;
  imageLoadError = false;

  // Form validation
  minDate: string = '';
  maxDate: string = '';

  statusOptions = [
    'Pending',
    'Confirmed',
    'In Progress',
    'Completed',
    'Cancelled',
    'Rescheduled'
  ];

  constructor(
    private appointmentService: AppointmentMessageServiceProxy,
    private fileService: FileServiceProxy,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getUser();
    this.isAdmin = this.authService.hasRole('Admin');

    // Set min date to today and max date to 1 year from now
    const today = new Date();
    this.minDate = today.toISOString().slice(0, 16);
    const maxDate = new Date();
    maxDate.setFullYear(today.getFullYear() + 1);
    this.maxDate = maxDate.toISOString().slice(0, 16);

    // Initialize default values
    this.appointment.createDate = DateTime.now();
    this.appointment.status = 'Pending';

    // Set patient email for non-admin users
    if (!this.isAdmin && this.currentUser) {
      this.appointment.patientEmail = this.currentUser['email'] || this.currentUser['unique_name'];
    }

    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.appointmentId = params['id'];
        this.isEditing = true;
        this.pageTitle = 'Edit Appointment';
        if (this.appointmentId) {
          this.loadAppointmentDetails(this.appointmentId);
        }
      }
    });
  }

  ngOnDestroy(): void {
    // Clean up any resources if needed
  }

  loadAppointmentDetails(id: string): void {
    this.isLoading = true;

    this.appointmentService.getAppointmentById(id).subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data) {
          this.appointment = data;

          // Check if user has permission to edit this appointment
          if (!this.isAdmin && this.currentUser) {
            const userEmail = this.currentUser['email'] || this.currentUser['unique_name'];
            if (this.appointment.patientEmail !== userEmail) {
              this.snackBar.open('You do not have permission to edit this appointment', 'Close', { duration: 3000 });
              this.router.navigate(['/appointments']);
              return;
            }
          }

          // Store original image name for comparison during update
          if (this.appointment.imageUrl) {
            this.previousImageName = this.appointment.imageUrl;
            this.loadImage(this.appointment.imageUrl);
          }
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Error loading appointment details', 'Close', { duration: 3000 });
        console.error('Error loading appointment:', error);
        this.router.navigate(['/appointments']);
      }
    });
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      this.createImagePreview(this.selectedFile);
      this.uploadFile();
    }
  }

  createImagePreview(file: File): void {
    if (!file.type.startsWith('image/')) {
      this.uploadError = 'Please select an image file';
      return;
    }

    const reader = new FileReader();
    reader.onload = () => {
      this.imagePreview = reader.result;
    };
    reader.readAsDataURL(file);
  }

  loadImage(filename: string): void {
    // Reset image states
    this.isImageLoading = true;
    this.imageLoadError = false;

    // Create full URL for image with baseUrl
    const imageUrl = `${this.baseUrl}/api/File/Getfile/${filename}`;

    // Fetch the image
    fetch(imageUrl)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to load image: ${response.status} ${response.statusText}`);
        }
        return response.blob();
      })
      .then(blob => {
        // Create a URL for the blob
        const objectUrl = URL.createObjectURL(blob);
        this.imagePreview = objectUrl;
        this.isImageLoading = false;
      })
      .catch(error => {
        console.error('Error loading image:', error);
        this.imageLoadError = true;
        this.isImageLoading = false;
      });
  }

  uploadFile(): void {
    if (!this.selectedFile) {
      return;
    }

    this.isUploading = true;
    this.uploadError = '';

    // Create a FileParameter object for the upload
    const fileParam: FileParameter = {
      data: this.selectedFile,
      fileName: this.selectedFile.name
    };

    // Upload the file
    this.fileService.upload('appointments', [fileParam]).subscribe({
      next: (response: ResponseMessage) => {
        this.isUploading = false;
        if (response && response.message) {
          // Extract the uploaded filename from the response
          const uploadedFilenames = response.message
            .replace('Files uploaded: ', '')
            .split(', ')
            .filter(name => name.trim().length > 0);

          if (uploadedFilenames.length > 0) {
            // Use the first uploaded filename
            this.appointment.imageUrl = uploadedFilenames[0];
            this.snackBar.open('Image uploaded successfully', 'Close', { duration: 3000 });
          }
        } else {
          this.uploadError = 'Upload succeeded but no filename was returned';
        }
      },
      error: (error) => {
        this.isUploading = false;
        this.uploadError = 'Failed to upload image: ' + (error.message || 'Unknown error');
        this.snackBar.open('Failed to upload image', 'Close', { duration: 3000 });
      }
    });
  }

  onImageError(event: Event): void {
    this.imageLoadError = true;
    const img = event.target as HTMLImageElement;

    // Replace with a generic SVG icon
    img.src = `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="%239CA3AF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>`;

    img.onerror = null; // Prevent infinite loop
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  removeImage(): void {
    this.selectedFile = null;
    this.imagePreview = null;
    this.appointment.imageUrl = undefined;
  }

  saveAppointment(): void {
    if (this.isUploading) {
      this.snackBar.open('Please wait for the image to finish uploading', 'Close', { duration: 3000 });
      return;
    }

    if (!this.appointment.patientEmail) {
      this.snackBar.open('Patient email is required', 'Close', { duration: 3000 });
      return;
    }

    if (!this.appointment.scheduledTime) {
      this.snackBar.open('Scheduled time is required', 'Close', { duration: 3000 });
      return;
    }

    // Validate scheduled time is in the future (for new appointments)
    if (!this.isEditing) {
      const scheduledDateTime = DateTime.fromISO(this.appointment.scheduledTime.toString());
      if (scheduledDateTime <= DateTime.now()) {
        this.snackBar.open('Scheduled time must be in the future', 'Close', { duration: 3000 });
        return;
      }
    }

    this.isLoading = true;

    // Set creation date for new appointments
    if (!this.isEditing) {
      this.appointment.createDate = DateTime.now();
    }

    // Ensure status is set
    if (!this.appointment.status) {
      this.appointment.status = 'Pending';
    }

    this.appointmentService.addOrUpdateAppointment(this.appointment).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.snackBar.open(
          this.isEditing ? 'Appointment updated successfully!' : 'Appointment scheduled successfully!',
          'Close',
          { duration: 3000 }
        );
        this.router.navigate(['/appointments']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Error saving appointment', 'Close', { duration: 3000 });
        console.error('Error saving appointment:', error);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/appointments']);
  }

  isFormValid(): boolean {
    return !!this.appointment.patientEmail &&
           !!this.appointment.scheduledTime &&
           !this.isUploading &&
           !this.isLoading;
  }

  // Convert DateTime to HTML datetime-local format
  getDateTimeLocalValue(dateTime: DateTime | undefined): string {
    if (!dateTime) return '';
    return dateTime.toFormat("yyyy-MM-dd'T'HH:mm");
  }

  // Handle datetime-local input change
  onScheduledTimeChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.value) {
      this.appointment.scheduledTime = DateTime.fromISO(input.value);
    }
  }

  // Get placeholder image URL
  getImageUrl(imageUrl: string | undefined): string {
    if (!imageUrl) return '';
    if (imageUrl.startsWith('http')) return imageUrl;
    return `${this.baseUrl}/api/File/Getfile/${imageUrl}`;
  }
}
