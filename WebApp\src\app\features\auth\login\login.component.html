<div
  class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-600 to-indigo-800 dark:from-gray-800 dark:to-gray-900 py-12 px-4 sm:px-6 lg:px-8 transition-colors duration-300">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto h-16 w-16 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center mb-4 shadow-lg dark:shadow-xl transition-colors duration-300">
        <svg class="h-8 w-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
          </path>
        </svg>
      </div>
      <h2 class="text-3xl font-bold text-white dark:text-gray-100 mb-2 transition-colors duration-300">Secure Login</h2>
      <p class="text-blue-100 dark:text-gray-300 transition-colors duration-300">Sign in to your account to continue</p>
    </div>

    <!-- Login Form -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg dark:shadow-2xl p-8 border border-gray-100 dark:border-gray-700 transition-all duration-300">
      <form #loginForm="ngForm" (ngSubmit)="onSubmit()" class="space-y-6" autocomplete="off">

        <!-- Email Field -->
        <div class="mb-6">
          <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-colors duration-300">
            Email Address <span class="text-red-500 dark:text-red-400">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207">
                </path>
              </svg>
            </div>
            <input id="email" type="email" name="email" [(ngModel)]="user.email" #email="ngModel" required email
              placeholder="Enter your email" autocomplete="email" spellcheck="false" (input)="clearErrorMessage()"
              class="w-full pl-10 px-4 py-3 border outline-none border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200"
              [class.border-red-500]="email.invalid && (email.dirty || email.touched)"
              [class.dark:border-red-400]="email.invalid && (email.dirty || email.touched)" />
          </div>
          <div *ngIf="email.invalid && (email.dirty || email.touched)" class="mt-2 text-sm text-red-600 dark:text-red-400">
            <div *ngIf="email.errors?.['required']">Email is required</div>
            <div *ngIf="email.errors?.['email']">Please enter a valid email address</div>
          </div>
          <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            <div class="flex items-center">
              <svg class="w-3 h-3 mr-1 text-blue-500 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z"
                  clip-rule="evenodd"></path>
              </svg>
              We'll never share your email with anyone else
            </div>
          </div>
        </div>        <!-- Password Field -->
        <div class="mb-4">
          <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-colors duration-300">
            Password <span class="text-red-500 dark:text-red-400">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                </path>
              </svg>
            </div>
            <input id="password" type="password" name="password" [(ngModel)]="user.password" #password="ngModel"
              required minlength="6" placeholder="Enter your password" autocomplete="current-password"
              (input)="clearErrorMessage()"
              class="w-full pl-10 px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 outline-none placeholder-gray-500 dark:placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200"
              [class.border-red-500]="password.invalid && (password.dirty || password.touched)"
              [class.dark:border-red-400]="password.invalid && (password.dirty || password.touched)" />
          </div>
          <div *ngIf="password.invalid && (password.dirty || password.touched)" class="mt-2 text-sm text-red-600 dark:text-red-400">
            <div *ngIf="password.errors?.['required']">Password is required</div>
            <div *ngIf="password.errors?.['minlength']">Password must be at least 6 characters long</div>
          </div>
        </div>

        <!-- Error Message -->
        <div *ngIf="errorMessage" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 animate-pulse transition-colors duration-300 mb-5">
          <div class="flex">
            <svg class="h-5 w-5 text-red-400 dark:text-red-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm text-red-700 dark:text-red-200">{{ errorMessage }}</span>
          </div>
        </div>

        <!-- Add gap between password and Sign In button -->
        <div class="mt-6">
          <app-button type="submit" variant="primary" size="lg" [fullWidth]="true" [disabled]="!loginForm.form.valid">
            Sign In
          </app-button>
        </div>
      </form>


      <!-- Sign Up Link -->
      <div class="mt-6 text-center">
        <div class="border-t border-gray-200 dark:border-gray-700 pt-4 transition-colors duration-300">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Don't have an account?
            <a routerLink="/auth/register"
              class="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 transition-colors duration-200">
              Sign up here
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
