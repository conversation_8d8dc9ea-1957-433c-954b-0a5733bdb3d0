import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ChatListComponent } from '../chat-list/chat-list.component';
import { ChatDetailComponent } from '../chat-detail/chat-detail.component';
import { DoctorPatientChatServiceProxy, StartNewChatRequestDto } from '../../../../shared/service-proxies/service-proxies';
import { PatientChatService } from '../../../services/patient-chat.service';
import { GlobalMessageNotificationService } from '../../../services/global-message-notification.service';
import { NotificationService } from '../../../services/notification.service';
import { HubConnectionState } from '@microsoft/signalr';

@Component({
  selector: 'app-chat-container',
  standalone: true,
  imports: [CommonModule, FormsModule, MatIconModule, ChatListComponent, ChatDetailComponent],
  templateUrl: './chat-container.component.html',
  styleUrls: ['./chat-container.component.css']
})
export class ChatContainerComponent implements OnInit, OnDestroy {
  @ViewChild('chatList') chatList!: ChatListComponent;

  selectedDoctorId: string | null = null;
  selectedPatientId: string | null = null;
  isMobileView = false;
  showChatDetail = false;
  showSearch = false;
  searchQuery = '';

  // SignalR connection status
  connectionState = HubConnectionState.Disconnected;
  HubConnectionState = HubConnectionState; // For template access

  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private chatServiceProxy: DoctorPatientChatServiceProxy,
    private chatService: PatientChatService,
    private globalMessageService: GlobalMessageNotificationService,
    private notificationService: NotificationService,
    private cdr: ChangeDetectorRef
  ) {
    this.checkMobileView();
  }

  ngOnInit(): void {
    // Listen for route changes to update selected chat
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      console.log('🔄 WebApp Chat Container: Route params changed:', params);
      this.selectedDoctorId = params['doctorId'] || null;
      this.selectedPatientId = params['patientId'] || null;
      this.showChatDetail = !!(this.selectedDoctorId && this.selectedPatientId);

      console.log('🔄 WebApp Chat Container: Updated state:', {
        selectedDoctorId: this.selectedDoctorId,
        selectedPatientId: this.selectedPatientId,
        showChatDetail: this.showChatDetail
      });

      // Force change detection to ensure UI updates
      this.cdr.detectChanges();
    });

    // Listen for query parameters to handle new chat requests
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(queryParams => {
      console.log('🔄 WebApp Chat Container: Query params changed:', queryParams);
      
      if (queryParams['startNewChat'] === 'true' && queryParams['patientId']) {
        console.log('🔄 WebApp Chat Container: Starting new chat with patient:', queryParams['patientId']);
        this.startNewChatWithPatient(queryParams['patientId']);
        
        // Clear query params to prevent repeated calls
        this.router.navigate(['/chat'], { replaceUrl: true });
      }
    });

    // Listen for window resize
    window.addEventListener('resize', () => this.checkMobileView());

    // Monitor SignalR connection status
    this.chatService.connectionState$.pipe(takeUntil(this.destroy$)).subscribe(state => {
      this.connectionState = state;
      console.log('SignalR Connection State:', state);
    });

    // Toast notifications are now handled by the StyledToastComponent
    // which subscribes directly to globalMessageService.toastNotification$
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    window.removeEventListener('resize', () => this.checkMobileView());
  }

  getConnectionStatusText(): string {
    const status = this.chatService.getConnectionStatus();

    switch (this.connectionState) {
      case HubConnectionState.Connected:
        return 'Live';
      case HubConnectionState.Reconnecting:
        return status.reconnectAttempts && status.reconnectAttempts > 1
          ? `Reconnecting... (${status.reconnectAttempts})`
          : 'Connecting...';
      case HubConnectionState.Disconnected:
        return 'Offline';
      default:
        return 'Unknown';
    }
  }

  getConnectionStatusTooltip(): string {
    const status = this.chatService.getConnectionStatus();

    switch (this.connectionState) {
      case HubConnectionState.Connected:
        const connectedTime = status.lastConnected
          ? `Connected at ${status.lastConnected.toLocaleTimeString()}`
          : 'Connected to real-time chat';
        return `✅ ${connectedTime}\nReal-time messaging is active`;

      case HubConnectionState.Reconnecting:
        const attempts = status.reconnectAttempts || 0;
        return `🔄 Attempting to reconnect...\nAttempt ${attempts}\nMessages may be delayed`;

      case HubConnectionState.Disconnected:
        return `❌ Disconnected from chat server\nMessages will not be delivered in real-time\nCheck your internet connection`;

      default:
        return 'Connection status unknown';
    }
  }

  private checkMobileView(): void {
    this.isMobileView = window.innerWidth < 768;
  }

  onChatSelected(event: { doctorId: string; patientId: string }): void {
    console.log('🔄 WebApp Chat Container: Chat selected:', event);

    // If doctorId is empty, this is a new chat request from doctor to patient
    if (!event.doctorId) {
      console.log('🔄 WebApp Chat Container: Starting new chat with patient:', event.patientId);
      this.startNewChatWithPatient(event.patientId);
      return;
    }

    this.selectedDoctorId = event.doctorId;
    this.selectedPatientId = event.patientId;
    this.showChatDetail = true;

    console.log('🔄 WebApp Chat Container: Navigating to chat:', {
      doctorId: event.doctorId,
      patientId: event.patientId
    });

    // Navigate to the chat detail route
    this.router.navigate(['/chat', event.doctorId, event.patientId]);
  }

  private startNewChatWithPatient(patientId: string): void {
    console.log('🔄 WebApp Chat Container: Starting new chat with patient ID:', patientId);
    
    // Create a new chat request without initial message - let user type their own
    const request = new StartNewChatRequestDto({
      doctorId: '00000000-0000-0000-0000-000000000000', // Empty GUID - will be determined by backend from current user
      patientId: patientId,
      initialMessage: '', // No automatic message - user will type their own
      topic: 'Medical Consultation'
    });

    this.chatServiceProxy.startNewChat(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (chatThread) => {
          console.log('✅ WebApp Chat Container: New chat created successfully:', chatThread);
          
          // Update component state
          this.selectedDoctorId = chatThread.doctorId;
          this.selectedPatientId = chatThread.patientId;
          this.showChatDetail = true;
          
          // Navigate to the new chat with proper URL structure
          this.router.navigate(['/chat', chatThread.doctorId, chatThread.patientId], { replaceUrl: true });
          
          // Force change detection
          this.cdr.detectChanges();
          
          // Refresh chat list to show the new conversation
          if (this.chatList) {
            this.chatList.refreshChats();
          }
        },
        error: (error) => {
          console.error('❌ WebApp Chat Container: Error starting new chat:', error);
          
          // Show user-friendly error message using notification service
          const errorMessage = error?.error?.message || 'Failed to start chat. Please try again.';
          this.notificationService.showError(errorMessage);
          
          // Navigate back to chat list on error
          this.router.navigate(['/chat'], { replaceUrl: true });
        }
      });
  }

  onBackToList(): void {
    this.showChatDetail = false;
    this.selectedDoctorId = null;
    this.selectedPatientId = null;
    this.router.navigate(['/chat']);
  }

  get shouldShowList(): boolean {
    return !this.isMobileView || !this.showChatDetail;
  }

  get shouldShowDetail(): boolean {
    return !this.isMobileView || this.showChatDetail;
  }

  onMessagesMarkedAsRead(): void {
    // Instead of full refresh, use real-time updates to update specific thread
    console.log('Messages marked as read, updating chat list intelligently...');
    if (this.chatList) {
      // Clear loading state for the selected thread
      this.chatList.selectedThreadId = null;
      // Refresh the chat list to update unread counts
      console.log('📦 Refreshing chat list to update unread counts');
      this.chatList.refreshChats();
    }
  }

  onMessageSent(message: any): void {
    // Update the chat list immediately when a message is sent
    if (this.chatList) {
      this.chatList.updateChatThreadWithNewMessage(message);
    }
  }

  toggleSearch(): void {
    this.showSearch = !this.showSearch;
    if (!this.showSearch) {
      this.searchQuery = '';
    }
  }

  onSearchInput(event: any): void {
    this.searchQuery = event.target.value;
    // TODO: Implement search functionality
    console.log('Searching for:', this.searchQuery);
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.showSearch = false;
  }
}
