/* Layout container */
.layout-container {
  display: flex;
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

/* Mobile overlay */
.mobile-sidebar-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  z-index: 40;
  transition: opacity 0.3s ease-in-out;
}

.mobile-sidebar-overlay.show {
  opacity: 1;
}

.mobile-sidebar-overlay.hide {
  opacity: 0;
}

/* Sidebar base styles */
.sidebar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 16rem;
  /* 64 in Tailwind = 16rem */
  flex-shrink: 0;
  overflow-y: auto;
  transition: transform 0.3s ease-in-out, background-color 0.3s ease, border-color 0.3s ease;
  z-index: 50;
}

/* Mobile sidebar - hidden by default on mobile */
@media (max-width: 1023px) {
  .sidebar-fixed {
    transform: translateX(-100%);
  }

  .sidebar-fixed.mobile-open {
    transform: translateX(0);
  }
}

/* Desktop sidebar - always visible */
@media (min-width: 1024px) {
  .sidebar-fixed {
    position: fixed;
    transform: translateX(0);
    z-index: 1000;
  }
}

/* Mobile header styles */
.mobile-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  padding-bottom: env(safe-area-inset-bottom);
}

.dark .mobile-header {
  background: rgba(31, 41, 55, 0.95);
  border-bottom-color: rgba(75, 85, 99, 0.8);
}

/* Add bottom padding for mobile devices */
@media (max-width: 1023px) {
  .mobile-header {
    padding-bottom: 8px;
    /* Additional bottom padding for mobile */
    min-height: 64px;
    /* Ensure consistent height */
  }

  /* Ensure proper spacing for content below mobile header */
  .main-content-mobile {
    padding-top: 80px;
    /* Account for header height + padding */
  }

  /* Mobile sidebar should also account for header */
  .mobile-sidebar {
    top: 72px;
    /* Position below mobile header */
    height: calc(100vh - 72px);
  }

  /* Right side button group spacing */
  .mobile-header .flex.items-center.space-x-2>*+* {
    margin-left: 8px;
  }

  /* User avatar and menu button sizing */
  .mobile-header button {
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

/* Mobile navigation improvements */
@media (max-width: 1023px) {

  /* Enhanced touch targets */
  .sidebar-fixed nav a {
    min-height: 48px;
    padding-top: 12px;
    padding-bottom: 12px;
  }

  /* Profile dropdown positioning */
  .user-dropdown {
    max-height: 300px;
    overflow-y: auto;
  }

  /* Sidebar content padding for mobile */
  .sidebar-fixed nav {
    padding-bottom: 120px;
    /* Extra space at bottom for mobile */
  }
}

/* Ensure main content scrolls properly */
main {
  height: 100%;
  overflow-y: auto;
}

/* Dropdown animations and styling */
.user-dropdown {
  animation: slideUp 0.2s ease-out;
  transform-origin: bottom;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Rotate animation for chevron */
.rotate-180 {
  transform: rotate(180deg);
}

/* Ensure dropdown is above other elements */
.dropdown-menu {
  z-index: 9999;
}

/* Navigation link hover effects */
.sidebar-fixed nav a:hover {
  transform: translateX(2px);
  transition: all 0.2s ease;
}

/* Custom scrollbar for sidebar */
.sidebar-fixed::-webkit-scrollbar {
  width: 4px;
}

.sidebar-fixed::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-fixed::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

html.dark .sidebar-fixed::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.sidebar-fixed::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

html.dark .sidebar-fixed::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.8);
}

/* Theme toggle specific styles */
.theme-toggle-container {
  position: relative;
}

.theme-dropdown {
  z-index: 1000;
}

/* Smooth transitions for all interactive elements */
button,
a,
.user-dropdown,
.sidebar-fixed {
  transition: all 0.2s ease;
}

/* Smooth animations for all interactive elements */
button,
a {
  transition: all 0.2s ease-in-out;
}

button:active,
a:active {
  transform: scale(0.98);
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .sidebar-fixed h1 {
    font-size: 1.25rem;
  }

  .sidebar-fixed nav a span {
    font-size: 0.875rem;
  }
}

/* Safe area support for devices with notches */
.pb-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Mobile button enhancements */
.hamburger-menu svg {
  transition: transform 0.2s ease-in-out;
}

.hamburger-menu:active svg {
  transform: scale(0.95);
}

/* Icon transition animations */
.hamburger-menu svg,
.mobile-header svg {
  transition: all 0.2s ease-in-out;
}

/* Hover effects for mobile buttons */
@media (hover: hover) {
  .mobile-header button:hover {
    transform: translateY(-1px);
  }
}

/* Toast Notification Animations */
.toast-container {
  pointer-events: none;
}

.toast-notification {
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-notification.animate-slide-in {
  transform: translateX(0);
  opacity: 1;
}

.toast-notification.animate-slide-out {
  transform: translateX(100%);
  opacity: 0;
}

/* Toast notification hover effects */
.toast-notification:hover {
  transform: translateX(-6px) scale(1.03);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(59, 130, 246, 0.1);
}

.toast-notification.animate-slide-in:hover {
  transform: translateX(-6px) scale(1.03);
}

/* Enhanced toast styling */
.toast-notification {
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode adjustments for toast */
@media (prefers-color-scheme: dark) {
  .toast-notification {
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .toast-notification:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(59, 130, 246, 0.2);
  }
}

/* Pulse animation for message icon */
@keyframes message-pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}