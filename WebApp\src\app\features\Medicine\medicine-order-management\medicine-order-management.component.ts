import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AuthService } from '../../../../shared/services/auth.service';
import { OrderServiceProxy, Order, OrderItem, UpdateOrderStatusRequest, MedicineServiceProxy, Medicine } from '../../../../shared/service-proxies/service-proxies';
import { DateTime } from 'luxon';
import { Subject, takeUntil } from 'rxjs';

interface OrderWithMedicines {
  id: string;
  orderNumber: string;
  patientEmail: string;
  patientName?: string;
  patientPhone?: string;
  deliveryAddress?: string;
  status: string;
  orderDate: string;
  totalAmount?: number;
  notes?: string;
  processedDate?: string;
  shippedDate?: string;
  deliveredDate?: string;
  trackingNumber?: string;
  orderItems: OrderItem[];
  medicines?: { [medicineId: string]: Medicine };
}

@Component({
  selector: 'app-medicine-order-management',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, MatSnackBarModule, MatDialogModule],
  templateUrl: './medicine-order-management.component.html',
  styleUrls: ['./medicine-order-management.component.css']
})
export class MedicineOrderManagementComponent implements OnInit, OnDestroy {
  Math = Math;
  orders: OrderWithMedicines[] = [];
  filteredOrders: OrderWithMedicines[] = [];
  isLoading = true;
  isUpdatingStatus = false;
  selectedStatus = 'All';
  selectedPatient = 'All';
  searchTerm = '';
  dateRange = 'All';
  currentPage = 1;
  pageSize = 5;
  totalPages = 1;
  orderStats = { total: 0, pending: 0, processing: 0, shipped: 0, delivered: 0, cancelled: 0, totalRevenue: 0 };
  statusOptions = ['All', 'Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'];
  dateRangeOptions = ['All', 'Today', 'This Week', 'This Month', 'Last 30 Days'];
  showStatusDropdown: { [orderId: string]: boolean } = {};
  dropdownPosition: { [key: string]: 'above' | 'below' } = {};
  dropdownPositions: { [orderId: string]: { top: number; left: number } } = {};
  dropdownDirection: { [orderId: string]: 'above' | 'below' } = {};
  private destroy$ = new Subject<void>();

  constructor(
    private orderService: OrderServiceProxy,
    private medicineService: MedicineServiceProxy,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    if (!this.authService.hasRole('Admin')) {
      this.showSnackbar('Access denied. Admin privileges required for order management.', true);
      this.router.navigate(['/dashboard']);
      return;
    }
    this.loadOrders();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private showSnackbar = (message: string, isError = false): void => {
    this.snackBar.open(message, 'Close', { duration: 3000, panelClass: isError ? 'error-snackbar' : 'success-snackbar' });
  }

  private mapOrder = (order: Order): OrderWithMedicines => ({
    id: order.id || '', orderNumber: order.orderNumber || '', patientEmail: order.patientEmail || '',
    patientName: order.patientName, patientPhone: order.patientPhone, deliveryAddress: order.deliveryAddress || '',
    status: order.status || 'Pending', orderDate: order.orderDate?.toString() || new Date().toISOString(),
    totalAmount: order.totalAmount || 0, notes: order.notes, processedDate: order.processedDate?.toString(),
    shippedDate: order.shippedDate?.toString(), deliveredDate: order.deliveredDate?.toString(),
    trackingNumber: order.trackingNumber, orderItems: order.orderItems || [], medicines: {}
  });

  loadOrders = (): void => {
    this.isLoading = true;
    this.orderService.getAll().pipe(takeUntil(this.destroy$)).subscribe({
      next: async (orders: Order[]) => {
        this.orders = (orders || []).map(this.mapOrder);

        const allMedicineIds = new Set<string>();
        this.orders.forEach(order => order.orderItems?.forEach(item => {
          if (item.medicineId) allMedicineIds.add(item.medicineId.toString());
        }));

        const medicineMap = new Map<string, Medicine>();
        const medicinePromises = Array.from(allMedicineIds).map(async (medicineId) => {
          try {
            const medicine = await this.medicineService.getById(medicineId).pipe(takeUntil(this.destroy$)).toPromise();
            if (medicine) medicineMap.set(medicineId, medicine);
          } catch {
            medicineMap.set(medicineId, { id: medicineId, name: this.getMedicineNameById(medicineId),
              description: 'Medicine details not available', category: 'General', price: 0, stockQuantity: 0 } as any);
          }
        });

        await Promise.all(medicinePromises);
        this.orders.forEach(order => {
          order.medicines = {};
          order.orderItems?.forEach(item => {
            if (item.medicineId) {
              const medicine = medicineMap.get(item.medicineId.toString());
              if (medicine) order.medicines![item.medicineId.toString()] = medicine;
            }
          });
        });

        this.calculateStatistics();
        this.applyFilters();
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
        this.showSnackbar('Error loading orders. Please try again.', true);
      }
    });
  }

  private calculateStatistics = (): void => {
    const statusCounts = this.orders.reduce((acc, o) => {
      acc[o.status.toLowerCase()] = (acc[o.status.toLowerCase()] || 0) + 1;
      if (o.status === 'Delivered') acc.totalRevenue += o.totalAmount || 0;
      return acc;
    }, { pending: 0, processing: 0, shipped: 0, delivered: 0, cancelled: 0, totalRevenue: 0 } as any);

    this.orderStats = { total: this.orders.length, ...statusCounts };
  }

  private getDateRange = (): DateTime => {
    const now = DateTime.now();
    switch (this.dateRange) {
      case 'Today': return now.startOf('day');
      case 'This Week': return now.startOf('week');
      case 'This Month': return now.startOf('month');
      case 'Last 30 Days': return now.minus({ days: 30 });
      default: return DateTime.fromMillis(0);
    }
  }

  applyFilters = (): void => {
    let filtered = this.orders.filter(order =>
      (this.selectedStatus === 'All' || order.status === this.selectedStatus) &&
      (this.dateRange === 'All' || DateTime.fromISO(order.orderDate) >= this.getDateRange()) &&
      (!this.searchTerm.trim() || this.matchesSearch(order))
    );

    this.filteredOrders = filtered.sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime());
    this.updatePagination();
  }

  private matchesSearch = (order: OrderWithMedicines): boolean => {
    const term = this.searchTerm.toLowerCase();
    return order.patientEmail?.toLowerCase().includes(term) ||
           order.patientName?.toLowerCase().includes(term) ||
           order.orderItems?.some(item => item.medicineName?.toLowerCase().includes(term) ||
                                         item.medicineCategory?.toLowerCase().includes(term)) ||
           order.trackingNumber?.toLowerCase().includes(term) ||
           order.id?.toLowerCase().includes(term) || false;
  }

  private updatePagination = (): void => {
    this.totalPages = Math.ceil(this.filteredOrders.length / this.pageSize);
    if (this.currentPage > this.totalPages) this.currentPage = 1;
  }

  get paginatedOrders(): OrderWithMedicines[] {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    return this.filteredOrders.slice(startIndex, startIndex + this.pageSize);
  }

  onSearchChange = (): void => { this.currentPage = 1; this.applyFilters(); }
  onStatusFilterChange = (): void => { this.currentPage = 1; this.applyFilters(); }
  onDateRangeChange = (): void => { this.currentPage = 1; this.applyFilters(); }

  onStatusBadgeKeyDown(event: KeyboardEvent, orderId: string): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.toggleStatusDropdown(orderId, event);
    } else if (event.key === 'Escape') {
      this.showStatusDropdown[orderId] = false;
    }
  }

  updateStatusFromDropdown(order: OrderWithMedicines, newStatus: string): void {
    // Close the dropdown
    this.showStatusDropdown[order.id] = false;

    // Update the status
    this.updateOrderStatus(order, newStatus);
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;

    // Check if click is on a status badge or inside a dropdown
    const isStatusBadgeClick = target.closest('[data-order-id]');
    const isDropdownClick = target.closest('.pointer-events-auto[role="menu"]');
    const isDropdownContainer = target.closest('.status-dropdown-container');

    // If click is outside badge, dropdown, and container, close all dropdowns
    if (!isStatusBadgeClick && !isDropdownClick && !isDropdownContainer) {
      Object.keys(this.showStatusDropdown).forEach(orderId => {
        this.showStatusDropdown[orderId] = false;
      });
    }
  }

  updateOrderStatus(order: OrderWithMedicines, newStatus: string): void {
    if (newStatus === order.status) {
      return; // No change
    }

    const trackingNumber = newStatus === 'Shipped' ?
      prompt('Enter tracking number (optional):') : undefined;

    if (newStatus === 'Shipped' && trackingNumber === null) {
      return; // User cancelled
    }

    this.isUpdatingStatus = true;

    // Update local order immediately for better UX
    const index = this.orders.findIndex(o => o.id === order.id);
    if (index !== -1) {
      this.orders[index].status = newStatus;
      if (trackingNumber) {
        this.orders[index].trackingNumber = trackingNumber;
      }

      // Set appropriate date fields
      const now = new Date().toISOString();
      switch (newStatus) {
        case 'Processing':
          this.orders[index].processedDate = now;
          break;
        case 'Shipped':
          this.orders[index].shippedDate = now;
          break;
        case 'Delivered':
          this.orders[index].deliveredDate = now;
          break;
      }
    }

    // Call the actual API to update order status
    const updateRequest = new UpdateOrderStatusRequest({
      status: newStatus,
      trackingNumber: trackingNumber || undefined
    });

    this.orderService.updateStatus(order.id, updateRequest).subscribe({
      next: (updatedOrder: any) => {
        this.isUpdatingStatus = false;
        this.snackBar.open(`Order status updated to ${newStatus}`, 'Close', { duration: 3000 });

        // Update the order in our local array with the response from server
        const orderIndex = this.orders.findIndex(o => o.id === order.id);
        if (orderIndex !== -1 && updatedOrder) {
          this.orders[orderIndex] = {
            ...this.orders[orderIndex],
            status: updatedOrder.status || newStatus,
            processedDate: updatedOrder.processedDate?.toString(),
            shippedDate: updatedOrder.shippedDate?.toString(),
            deliveredDate: updatedOrder.deliveredDate?.toString(),
            trackingNumber: updatedOrder.trackingNumber || trackingNumber || undefined
          };
        }

        this.calculateStatistics();
        this.applyFilters();
      },
      error: (error: any) => {
        this.isUpdatingStatus = false;
        console.error('Error updating order status:', error);

        // Revert the local change on error
        const orderIndex = this.orders.findIndex(o => o.id === order.id);
        if (orderIndex !== -1) {
          this.orders[orderIndex].status = order.status; // Revert to original status
        }

        this.snackBar.open('Error updating order status. Please try again.', 'Close', { duration: 3000 });
        this.applyFilters();
      }
    });
  }

  viewOrderDetails(order: OrderWithMedicines): void {
    // Navigate to order details page
    this.router.navigate(['/medicine/orders', order.id]);
  }

  exportOrders = (): void => {
    const csvData = this.filteredOrders.map(order => [
      order.id, order.patientEmail, order.patientName || 'N/A', order.orderItems?.length || 0,
      order.totalAmount || 0, order.status, this.formatDateTime(order.orderDate), order.trackingNumber || 'N/A'
    ]);

    const csv = ['Order ID,Patient Email,Patient Name,Items,Total Amount,Status,Order Date,Tracking Number',
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))].join('\n');

    const link = document.createElement('a');
    link.href = URL.createObjectURL(new Blob([csv], { type: 'text/csv' }));
    link.download = 'medicine-orders.csv';
    link.click();
  }

  getStatusColor = (status: string): string => {
    const colors = {
      pending: 'inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold border bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-700',
      processing: 'inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold border bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700',
      shipped: 'inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold border bg-purple-50 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-700',
      delivered: 'inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold border bg-green-50 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-700',
      cancelled: 'inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold border bg-red-50 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700'
    };
    return colors[status?.toLowerCase() as keyof typeof colors] || 'inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold border bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700';
  }

  getStatusIndicatorColor = (status: string): string => {
    const colors = {
      pending: 'bg-yellow-400',
      processing: 'bg-blue-400',
      shipped: 'bg-purple-400',
      delivered: 'bg-green-400',
      cancelled: 'bg-red-400'
    };
    return colors[status?.toLowerCase() as keyof typeof colors] || 'bg-gray-400';
  }

  formatDateTime = (dateTime: any): string => {
    if (!dateTime) return 'N/A';
    try {
      const dt = DateTime.fromISO(dateTime);
      return dt.isValid ? dt.toFormat('MMM dd, yyyy • hh:mm a') : 'Invalid date';
    } catch { return 'Invalid date'; }
  }

  getRelativeTime = (dateTime: any): string => {
    if (!dateTime) return '';
    try {
      const dt = DateTime.fromISO(dateTime);
      return dt.isValid ? dt.toRelative() || '' : '';
    } catch { return ''; }
  }

  refreshOrders(): void {
    this.loadOrders();
  }

  bulkUpdateStatus(selectedOrders: OrderWithMedicines[], newStatus: string): void {
    if (selectedOrders.length === 0) {
      this.snackBar.open('Please select orders to update', 'Close', { duration: 3000 });
      return;
    }

    this.isUpdatingStatus = true;
    const updatePromises = selectedOrders.map(order =>
      this.orderService.updateStatus(order.id, new UpdateOrderStatusRequest({
        status: newStatus,
        trackingNumber: undefined
      })).toPromise()
    );

    Promise.all(updatePromises)
      .then(() => {
        this.isUpdatingStatus = false;
        this.snackBar.open(`${selectedOrders.length} orders updated to ${newStatus}`, 'Close', { duration: 3000 });
        this.loadOrders(); // Reload to get fresh data
      })
      .catch(error => {
        this.isUpdatingStatus = false;
        console.error('Error in bulk update:', error);
        this.snackBar.open('Error updating some orders. Please try again.', 'Close', { duration: 3000 });
      });
  }

  getOrderTotal = (order: OrderWithMedicines): number => order.totalAmount || 0;
  previousPage = (): void => { if (this.currentPage > 1) this.currentPage--; }
  nextPage = (): void => { if (this.currentPage < this.totalPages) this.currentPage++; }
  goToPage = (page: number): void => { if (page >= 1 && page <= this.totalPages) this.currentPage = page; }

  get paginationRange(): number[] {
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);
    return Array.from({length: end - start + 1}, (_, i) => start + i);
  }

  getMedicineNameById = (id: string): string => {
    const names = { '1': 'Paracetamol 500mg', '2': 'Amoxicillin 250mg', '3': 'Ibuprofen 400mg',
                   '4': 'Aspirin 325mg', '5': 'Vitamin D3' };
    return names[id as keyof typeof names] || `Medicine #${id}`;
  }
    onPageSizeChange(event: any): void {
    const value = event.target.value;
    this.pageSize = parseInt(value, 10);
    this.currentPage = 1;
    this.updatePagination(); // Make sure this method recalculates pagination and paginatedOrders
  }

  // Smart dropdown positioning - position above or below based on available space
  toggleStatusDropdown(orderId: string, event: Event): void {
    event.stopPropagation();

    // Close all other dropdowns first
    Object.keys(this.showStatusDropdown).forEach(id => {
      if (id !== orderId) {
        this.showStatusDropdown[id] = false;
      }
    });

    // Toggle the current dropdown
    this.showStatusDropdown[orderId] = !this.showStatusDropdown[orderId];

    if (this.showStatusDropdown[orderId]) {
      // Calculate position when opening
      setTimeout(() => {
        const button = event.target as HTMLElement;
        const rect = button.getBoundingClientRect();

        // Estimated dropdown height (5 status options * ~40px each + padding)
        const dropdownHeight = 220;

        // Check available space below and above the button
        const spaceBelow = window.innerHeight - rect.bottom;
        const spaceAbove = rect.top;

        let top: number;
        let left: number = rect.left + window.scrollX;

        // Position dropdown based on available space
        if (spaceBelow >= dropdownHeight + 10) {
          // Enough space below - position below the button
          top = rect.bottom + window.scrollY + 5;
          this.dropdownDirection[orderId] = 'below';
        } else if (spaceAbove >= dropdownHeight + 10) {
          // Not enough space below but enough above - position above the button
          top = rect.top + window.scrollY - dropdownHeight - 5;
          this.dropdownDirection[orderId] = 'above';
        } else {
          // Not enough space in either direction - position below but with viewport adjustment
          top = rect.bottom + window.scrollY + 5;
          this.dropdownDirection[orderId] = 'below';
        }

        // Ensure dropdown doesn't go off the right edge of screen
        const dropdownWidth = 192; // w-48 = 12rem = 192px
        if (left + dropdownWidth > window.innerWidth) {
          left = rect.right + window.scrollX - dropdownWidth;
        }

        // Ensure dropdown doesn't go off the left edge
        if (left < 10) {
          left = 10;
        }

        this.dropdownPositions[orderId] = { top, left };
      }, 0);
    }
  }

  getDropdownTop(orderId: string): number {
    return this.dropdownPositions[orderId]?.top || 0;
  }

  getDropdownLeft(orderId: string): number {
    return this.dropdownPositions[orderId]?.left || 0;
  }

  getDropdownDirection(orderId: string): 'above' | 'below' {
    return this.dropdownDirection[orderId] || 'below';
  }

  private calculateDropdownPosition(orderId: string): void {
    console.log('🎯 Calculating position for order:', orderId);
    const badge = document.querySelector(`[data-order-id="${orderId}"]`) as HTMLElement;
    console.log('📍 Badge element found:', badge);

    if (badge) {
      const rect = badge.getBoundingClientRect();
      console.log('📐 Badge rect:', rect);
      console.log('� Window scroll:', { scrollY: window.scrollY, scrollX: window.scrollX });

      // Simple calculation - position dropdown directly below the badge
      const top = rect.bottom + window.scrollY + 5; // 5px gap below
      const left = rect.left + window.scrollX;

      console.log('✅ Final position calculated:', { top, left });

      this.dropdownPositions[orderId] = { top, left };
      console.log('💾 Position stored:', this.dropdownPositions[orderId]);
    } else {
      console.error('❌ Badge element not found for orderId:', orderId);
    }
  }
}
