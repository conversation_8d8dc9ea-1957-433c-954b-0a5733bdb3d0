import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { PatientInfo, PatientInfoServiceProxy, UserAccountServiceProxy, WoundRequestServiceProxy, WoundRequestDto } from '../../../../shared/service-proxies/service-proxies';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AuthService } from '../../../../shared/services/auth.service';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { ConfirmationDialogComponent } from '../../user/user-request/confirmation-dialog/confirmation-dialog.component';
import { Subject, takeUntil, forkJoin } from 'rxjs';

@Component({
  selector: 'app-patient-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  templateUrl: './patient-list.component.html',
  styleUrl: './patient-list.component.css'
})
export class PatientListComponent implements OnInit, OnDestroy {
  baseUrl = getRemoteServiceBaseUrl();
  patients: PatientInfo[] = [];
  allPatients: PatientInfo[] = []; // Store all patients
  patientsWithRequests: string[] = []; // Store emails of patients with requests
  filteredPatients: PatientInfo[] = [];
  isLoading = false;
  searchTerm = '';
  isAdmin = false;
  canManagePatients = false;
  currentPage = 1;
  patientsPerPage = 6; // Changed from itemsPerPage to match HTML
  totalPages = 0;
  sortBy: 'fullName' | 'email' | 'dateOfBirth' = 'fullName';
  sortDirection: 'asc' | 'desc' = 'asc';
  availableDoctors: any[] = [];
  assignedPatients: Map<string, string> = new Map();
  isAssigning = false;
  currentDoctorEmail = '';
  assignmentFilter: 'all' | 'assigned' | 'unassigned' | 'my-patients' = 'all';
  selectedDoctorFilter = '';
  private destroy$ = new Subject<void>();

  constructor(
    private patientService: PatientInfoServiceProxy,
    private userAccountService: UserAccountServiceProxy,
    private woundRequestService: WoundRequestServiceProxy,
    private router: Router,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.isAdmin = this.authService.hasRole('Admin');
    this.canManagePatients = this.authService.hasRole('Admin') || this.authService.hasRole('Doctor');
    this.currentDoctorEmail = this.authService.getUser()?.email || '';
    this.assignmentFilter = 'all';
    this.loadDoctors();
    this.loadPatients();
    this.loadPatientAssignments();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private showSnackbar = (message: string, isError = false): void => {
    this.snackBar.open(message, 'Close', { duration: 3000, panelClass: isError ? 'error-snackbar' : 'success-snackbar' });
  }

  loadPatients = (): void => {
    this.isLoading = true;

    // Load both patients and requests in parallel
    const patientsCall = this.patientService.getAllPatientsInfo();

    // Choose the appropriate request call based on user role
    const requestsCall = this.isAdmin
      ? this.woundRequestService.getAll()
      : this.woundRequestService.getAllForDoctor(this.currentDoctorEmail);

    forkJoin({
      patients: patientsCall,
      requests: requestsCall
    }).pipe(takeUntil(this.destroy$)).subscribe({
      next: ({ patients, requests }) => {
        console.log('Raw patients data:', patients);
        console.log('Raw requests data:', requests);

        // Store all patients
        this.allPatients = patients || [];

        // Extract patient emails from requests
        this.patientsWithRequests = [...new Set(
          (requests || [])
            .map(request => request.userEmail)
            .filter((email): email is string => email !== undefined && email !== null && email.trim() !== '')
        )];

        console.log('Patients with requests emails:', this.patientsWithRequests);
        console.log('All patients emails:', this.allPatients.map(p => p.email));

        // For debugging - temporarily show all patients if no requests are found
        if (this.patientsWithRequests.length === 0) {
          console.log('No patients with requests found, showing all patients for debugging');
          this.patients = this.allPatients;
        } else {
          // Filter patients to only show those with requests
          this.patients = this.allPatients.filter(patient =>
            patient.email && this.patientsWithRequests.includes(patient.email)
          );
        }

        console.log('Final filtered patients:', this.patients.length, 'out of', this.allPatients.length);

        this.applyFiltersAndSort();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading patients or requests:', error);

        // Fallback: Load all patients if there's an error with requests
        console.log('Loading all patients as fallback due to error');
        this.patientService.getAllPatientsInfo().pipe(takeUntil(this.destroy$)).subscribe({
          next: (patients) => {
            this.allPatients = patients || [];
            this.patients = this.allPatients;
            this.applyFiltersAndSort();
            this.isLoading = false;
            this.showSnackbar('Loaded all patients (requests service unavailable)', false);
          },
          error: (patientsError) => {
            console.error('Error loading patients:', patientsError);
            this.isLoading = false;
            this.showSnackbar('Error loading patients', true);
          }
        });
      }
    });
  }

  private applyFiltersAndSort = (): void => {
    const searchLower = this.searchTerm.toLowerCase();

    let filtered = this.patients.filter(patient =>
      (!this.searchTerm.trim() ||
        patient.fullName?.toLowerCase().includes(searchLower) ||
        patient.email?.toLowerCase().includes(searchLower) ||
        patient.phoneNumber?.includes(searchLower)) &&
      (!this.isAdmin || this.applyAssignmentFilter(patient)) &&
      (!this.selectedDoctorFilter ||
        (patient.email && this.assignedPatients.get(patient.email) === this.selectedDoctorFilter))
    );

    filtered.sort((a, b) => {
      const getValue = (patient: PatientInfo): any => {
        switch (this.sortBy) {
          case 'fullName': return patient.fullName?.toLowerCase() || '';
          case 'email': return patient.email?.toLowerCase() || '';
          case 'dateOfBirth': return patient.dateOfBirth?.toMillis() || 0;
        }
      };
      const aVal = getValue(a), bVal = getValue(b);
      const result = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
      return this.sortDirection === 'asc' ? result : -result;
    });

    this.filteredPatients = filtered;
    this.calculatePagination();
  }

  private applyAssignmentFilter = (patient: PatientInfo): boolean => {
    if (!patient.email) return true;
    switch (this.assignmentFilter) {
      case 'assigned': return this.isPatientAssigned(patient.email);
      case 'unassigned': return !this.isPatientAssigned(patient.email);
      case 'my-patients': return this.isPatientAssignedToCurrentDoctor(patient.email);
      default: return true;
    }
  }

  private calculatePagination = (): void => {
    this.totalPages = Math.ceil(this.filteredPatients.length / this.patientsPerPage); // Fixed property name
    if (this.currentPage > this.totalPages) this.currentPage = 1;
  }

  getPaginatedPatients = (): PatientInfo[] => {
    const startIndex = (this.currentPage - 1) * this.patientsPerPage; // Fixed property name
    return this.filteredPatients.slice(startIndex, startIndex + this.patientsPerPage); // Fixed property name
  }

  // Add missing pagination methods
  onPageSizeChange(): void {
    this.currentPage = 1; // Reset to first page when changing page size
    this.calculatePagination();
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  previousPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  }

  changePage(page: number): void {
    this.goToPage(page);
  }

  getPageNumbers(): number[] {
    return this.getPaginationRange();
  }

  getPaginationRange(): number[] {
    const total = this.totalPages;
    const current = this.currentPage;
    const delta = 2;
    const range = [];
    let l: number;

    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
      range.push(i);
    }
    if (current - delta > 2) {
      range.unshift(-1); // -1 will be used for ellipsis
    }
    if (current + delta < total - 1) {
      range.push(-2); // -2 will be used for ellipsis
    }
    range.unshift(1);
    if (total > 1) range.push(total);
    return range;
  }

  getStartIndex(): number {
    return ((this.currentPage - 1) * this.patientsPerPage) + 1; // Fixed property name
  }

  getEndIndex(): number {
    const maxItem = this.currentPage * this.patientsPerPage; // Fixed property name
    return Math.min(maxItem, this.filteredPatients.length);
  }

  // Update existing methods to use correct property name
  getEndIndex_old = (): number => Math.min(this.currentPage * this.patientsPerPage, this.filteredPatients.length); // Fixed
  getStartIndex_old = (): number => (this.currentPage - 1) * this.patientsPerPage + 1; // Fixed

  syncPatients = (): void => {
    if (!this.isAdmin) return this.showSnackbar('Only admins can sync patients', true);

    this.patientService.syncPatientsFromUserAccounts().pipe(takeUntil(this.destroy$)).subscribe({
      next: (response: any) => {
        const syncedCount = response?.syncedCount || 0;
        const message = response?.message || 'Sync completed';

        if (syncedCount === 0) {
          this.showSnackbar('No users available to be added');
        } else {
          this.showSnackbar(message);
          this.loadPatients();
        }
      },
      error: (error) => this.showSnackbar(`Error syncing patients: ${error.error?.message || 'Unknown error'}`, true)
    });
  }

  viewPatientDetails = (patient: PatientInfo): void => { this.router.navigate(['/patients/detail', patient.id]); };
  editPatient = (patient: PatientInfo): void => { this.router.navigate(['/patients/edit', patient.id]); };

  deletePatient = (patient: PatientInfo): void => {
    this.dialog.open(ConfirmationDialogComponent, {
      width: '450px',
      data: {
        title: 'Delete Patient',
        message: 'Are you sure you want to delete this patient? This action cannot be undone and will permanently remove all patient data including appointments and medical records.',
        confirmText: 'Delete Patient', cancelText: 'Cancel', type: 'danger',
        itemName: patient.fullName || 'Unnamed Patient'
      }
    }).afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.patientService.deletePatientInfo(patient.id).pipe(takeUntil(this.destroy$)).subscribe({
          next: () => {
            this.isLoading = false;
            this.showSnackbar('Patient deleted successfully');
            this.loadPatients();
          },
          error: () => {
            this.isLoading = false;
            this.showSnackbar('Error deleting patient', true);
          }
        });
      }
    });
  }

  getPatientAge = (dateOfBirth: any): number => {
    if (!dateOfBirth) return 0;
    const today = new Date(), birthDate = new Date(dateOfBirth.toJSDate());
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) age--;
    return age;
  }

  formatDate = (date: any): string => date ? date.toFormat('MMM dd, yyyy') : '-';

  getImageUrl = (imageUrl: string | undefined): string =>
    !imageUrl ? '' : imageUrl.startsWith('http') ? imageUrl : `${this.baseUrl}/api/File/Getfile/${imageUrl}`;

  getPatientInitials = (patient: PatientInfo): string => {
    if (!patient.fullName) return 'P';
    const names = patient.fullName.split(' ');
    return names.length >= 2 ? (names[0].charAt(0) + names[1].charAt(0)).toUpperCase() : patient.fullName.charAt(0).toUpperCase();
  }

  onImageError = (event: Event): void => {
    const img = event.target as HTMLImageElement;
    img.src = `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="%239CA3AF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>`;
    img.onerror = null;
  }

  // ...rest of existing methods remain the same...

  private loadDoctors = (): void => {
    this.userAccountService.getAllDoctors().pipe(takeUntil(this.destroy$)).subscribe({
      next: (doctors) => {
        this.availableDoctors = (doctors || []).map(doctor => ({
          email: doctor.email,
          name: doctor.name || doctor.email,
          specialization: 'General Practice' // TODO: Add specialization field to UserDto
        }));
      },
      error: () => {
        this.showSnackbar('Error loading doctors', true);
        this.availableDoctors = []; // Fallback to empty array
      }
    });
  }

  private loadPatientAssignments = (): void => {
    // TODO: Implement patient assignment API when available
    // For now, initialize empty assignments - assignments will be loaded when assignment service is implemented
    this.assignedPatients.clear();

    // Future implementation:
    // this.patientAssignmentService.getAllAssignments().pipe(takeUntil(this.destroy$)).subscribe({
    //   next: (assignments) => {
    //     this.assignedPatients.clear();
    //     assignments.forEach(assignment => {
    //       this.assignedPatients.set(assignment.patientEmail, assignment.doctorEmail);
    //     });
    //   },
    //   error: () => this.showSnackbar('Error loading patient assignments', true)
    // });
  }

  assignPatientToDoctor = (patientEmail: string, doctorEmail: string): void => {
    this.isAssigning = true;

    // TODO: Implement with real patient assignment API when available
    // For now, update local state only
    this.assignedPatients.set(patientEmail, doctorEmail);
    this.isAssigning = false;
    const doctor = this.availableDoctors.find(d => d.email === doctorEmail);
    this.showSnackbar(`Patient assigned to ${doctor?.name || doctorEmail}`);
    this.applyFiltersAndSort();

    // Future implementation:
    // const request = new AssignDoctorRequest({
    //   patientEmail: patientEmail,
    //   doctorEmail: doctorEmail
    // });
    //
    // this.patientAssignmentService.assignPatientToDoctor(request).pipe(takeUntil(this.destroy$)).subscribe({
    //   next: () => {
    //     this.assignedPatients.set(patientEmail, doctorEmail);
    //     this.isAssigning = false;
    //     const doctor = this.availableDoctors.find(d => d.email === doctorEmail);
    //     this.showSnackbar(`Patient assigned to ${doctor?.name || doctorEmail}`);
    //     this.applyFiltersAndSort();
    //   },
    //   error: () => {
    //     this.isAssigning = false;
    //     this.showSnackbar('Error assigning patient to doctor', true);
    //   }
    // });
  }

  unassignPatient = (patientEmail: string): void => {
    this.isAssigning = true;

    // TODO: Implement with real patient assignment API when available
    // For now, update local state only
    this.assignedPatients.delete(patientEmail);
    this.isAssigning = false;
    this.showSnackbar('Patient unassigned successfully');
    this.applyFiltersAndSort();

    // Future implementation:
    // this.patientAssignmentService.unassignPatient(patientEmail).pipe(takeUntil(this.destroy$)).subscribe({
    //   next: () => {
    //     this.assignedPatients.delete(patientEmail);
    //     this.isAssigning = false;
    //     this.showSnackbar('Patient unassigned successfully');
    //     this.applyFiltersAndSort();
    //   },
    //   error: () => {
    //     this.isAssigning = false;
    //     this.showSnackbar('Error unassigning patient', true);
    //   }
    // });
  }

  getAssignedDoctor = (patientEmail: string): any => {
    const doctorEmail = this.assignedPatients.get(patientEmail);
    return this.availableDoctors.find(d => d.email === doctorEmail);
  }

  isPatientAssigned = (patientEmail: string): boolean => this.assignedPatients.has(patientEmail);

  isPatientAssignedToCurrentDoctor = (patientEmail: string): boolean =>
    this.assignedPatients.get(patientEmail) === this.currentDoctorEmail;

  onAssignmentFilterChange = (): void => { this.currentPage = 1; this.applyFiltersAndSort(); }
  onDoctorFilterChange = (): void => { this.currentPage = 1; this.applyFiltersAndSort(); }

  onSearchChange(): void {
    // Filter patients based on searchTerm and reset to first page
    this.currentPage = 1;
    this.applyFiltersAndSort?.();
  }

  onSortChange(sortBy: 'fullName' | 'email' | 'dateOfBirth'): void {
    if (this.sortBy === sortBy) {
      // Toggle sort direction if same field
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      // Set new sort field and default to ascending
      this.sortBy = sortBy;
      this.sortDirection = 'asc';
    }
    this.currentPage = 1; // Reset to first page when sorting
    this.applyFiltersAndSort();
  }

  getAssignmentStats = (): { total: number; assigned: number; unassigned: number; myPatients: number } => {
    const total = this.patients.length;
    const assigned = this.patients.filter(p => p.email && this.isPatientAssigned(p.email)).length;
    const myPatients = this.patients.filter(p => p.email && this.isPatientAssignedToCurrentDoctor(p.email)).length;
    return { total, assigned, unassigned: total - assigned, myPatients };
  }

  startChatWithPatient = (patient: PatientInfo): void => {
    if (patient.id) {
      // Navigate to chat container without specific doctor/patient IDs
      // The chat container will handle starting a new chat with the patient
      this.router.navigate(['/chat'], {
        queryParams: {
          startNewChat: 'true',
          patientId: patient.id,
          patientName: patient.fullName || 'Patient'
        }
      });
    }
  }
}
