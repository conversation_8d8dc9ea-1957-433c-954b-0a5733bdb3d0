/* Dialog Container Styling */
:host ::ng-deep .mat-mdc-dialog-container {
  padding: 0;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

.dialog-header {
  background: linear-gradient(135deg, #4f46e5, #2563eb);
  color: white;
  padding: 1.25rem 1.5rem;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dialog-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.dialog-content {
  padding: 1.5rem;
  max-height: 65vh;
  overflow-y: auto;
  background-color: #ffffff;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

/* Form Styling */
.routine-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #1e293b;
  font-size: 0.875rem;
}

.input-container, .custom-select-container, .textarea-container {
  position: relative;
  width: 100%;
}

.custom-input, .custom-select, .custom-textarea {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.2s;
  background-color: white;
  color: #1e293b;
}

.custom-textarea {
  padding: 0.75rem 1rem;
  resize: vertical;
  min-height: 6rem;
}

.custom-input:focus, .custom-select:focus, .custom-textarea:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.custom-input.invalid, .custom-select.invalid, .custom-textarea.invalid {
  border-color: #ef4444;
}

.input-icon, .select-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0.75rem;
  color: #64748b;
  pointer-events: none;
}

.select-icon {
  right: 0.75rem;
  left: auto;
}

.error-message {
  font-size: 0.8rem;
  color: #ef4444;
  margin-top: 0.25rem;
}

/* Button Styling */
.btn {
  padding: 0.625rem 1.25rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #4f46e5, #2563eb);
  color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.btn-primary:hover:not([disabled]) {
  background: linear-gradient(135deg, #4338ca, #1d4ed8);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
}

.btn-cancel {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-cancel:hover {
  background: #f8fafc;
  color: #334155;
}
