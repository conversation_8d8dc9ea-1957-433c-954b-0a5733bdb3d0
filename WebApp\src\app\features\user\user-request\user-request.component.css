/* Animation for cards */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Card hover effects */
.bg-white {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.bg-white:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Status tag pulse animation for pending */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4); }
  70% { box-shadow: 0 0 0 6px rgba(245, 158, 11, 0); }
  100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0); }
}

.bg-amber-100 {
  animation: pulse 2s infinite;
}

/* Category styles */
.project-category {
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: rgba(79, 70, 229, 0.1);
  color: #4F46E5;
  display: inline-block;
  transition: all 0.2s ease;
}

/* Table row hover effect */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  background-color: rgba(236, 253, 245, 0.4);
}

/* Line clamp for long text */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Button transitions */
button {
  transition: all 0.2s ease;
}

/* Card animations */
.rounded-xl {
  animation: fadeIn 0.5s ease-out forwards;
}

.rounded-xl:nth-child(2) {
  animation-delay: 0.1s;
}

.rounded-xl:nth-child(3) {
  animation-delay: 0.2s;
}

/* Progress Card Animation */
.bg-purple-500.h-2 {
  position: relative;
  overflow: hidden;
}

.bg-purple-500.h-2::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Staggered animation for the cards */
.grid > div:nth-child(1) {
  animation-delay: 0.1s;
}

.grid > div:nth-child(2) {
  animation-delay: 0.2s;
}

.grid > div:nth-child(3) {
  animation-delay: 0.3s;
}

.grid > div:nth-child(4) {
  animation-delay: 0.4s;
}

/* Pulsing effect for progress count */
.text-purple-600 {
  animation: pulse 2s infinite;
}

/* PrimeNG Toast override styles for better theming */
:host ::ng-deep .p-toast .p-toast-message.p-toast-message-success {
  background: #434444;
/* MatSnackBar Styles - Matching Medicine Order Management */
::ng-deep .success-snackbar {
  background: #464747 !important;
  color: white !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.4), 0 4px 6px -2px rgba(16, 185, 129, 0.1) !important;
}

::ng-deep .success-snackbar .mat-mdc-snack-bar-label {
  color: white !important;
  font-weight: 500 !important;
}

::ng-deep .success-snackbar .mat-mdc-button {
  color: white !important;
  font-weight: 600 !important;
}

::ng-deep .error-snackbar {
  background: #ef4444 !important;
  color: white !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.4), 0 4px 6px -2px rgba(239, 68, 68, 0.1) !important;
}

::ng-deep .error-snackbar .mat-mdc-snack-bar-label {
  color: white !important;
  font-weight: 500 !important;
}

::ng-deep .error-snackbar .mat-mdc-button {
  color: white !important;
  font-weight: 600 !important;
}

/* Snackbar container positioning and animation */
::ng-deep .mat-mdc-snack-bar-container {
  border-radius: 8px !important;
  animation: slideInFromTop 0.3s ease-out !important;
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
}
