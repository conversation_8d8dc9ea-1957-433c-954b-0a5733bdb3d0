<div class="p-4 bg-white dark:bg-gray-900 min-h-screen transition-colors duration-200">
  <!-- Header with gradient background -->
  <div
    class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-lg shadow-sm border border-blue-100 dark:border-gray-600 mb-5 overflow-hidden">
    <div class="px-5 py-4 border-b border-blue-100 dark:border-gray-600 flex items-center justify-between">
      <div class="flex items-center">
        <div class="bg-blue-500 dark:bg-blue-600 p-2 rounded-lg mr-3">
          <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
          </svg>
        </div>
        <div>
          <h1 class="text-xl font-bold text-gray-800 dark:text-white">Add New User</h1>
          <p class="text-gray-600 dark:text-gray-300 text-sm">Quick user creation form</p>
        </div>
      </div>
      <a routerLink="/users"
        class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200">
        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m0 7h18"></path>
        </svg>
        Back
      </a>
    </div>
  </div>

  <!-- Add User Form -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <form #userForm="ngForm" (ngSubmit)="onSubmit()" class="p-5">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
        <!-- No section header needed - cleaner design --> <!-- Name with icon -->
        <div> <label for="name" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200 mb-1.5">
            <svg class="h-4 w-4 text-gray-500 dark:text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            Full Name <span class="text-red-500 ml-0.5">*</span>
          </label>
          <div class="relative">
            <input id="name" name="name" type="text" [(ngModel)]="user.name" #name="ngModel" required minlength="2"
              placeholder="Enter full name"
              class="w-full pl-10 pr-4 py-2.5 border outline-none border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              [class.border-red-500]="getFieldError('name')" />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z">
                </path>
              </svg>
            </div>
          </div>
          <div *ngIf="getFieldError('name')" class="mt-1.5 text-xs text-red-600">
            {{ getFieldError('name') }}
          </div>
        </div>

        <!-- Email with icon -->
        <div> <label for="email" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200 mb-1.5">
            <svg class="h-4 w-4 text-gray-500 dark:text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
              </path>
            </svg>
            Email Address <span class="text-red-500 ml-0.5">*</span>
          </label>
          <div class="relative">
            <input id="email" name="email" type="email" [(ngModel)]="user.email" #email="ngModel" required email
              placeholder="Enter email address"
              class="w-full pl-10 pr-4 py-2.5 outline-none border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              [class.border-red-500]="getFieldError('email')" />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207">
                </path>
              </svg>
            </div>
          </div>
          <div *ngIf="getFieldError('email')" class="mt-1.5 text-xs text-red-600">
            {{ getFieldError('email') }}
          </div>
          <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            A username will be automatically generated from the email address
          </div>
        </div>        <!-- Password Field (No need for section header - cleaner design) -->
        <div>
          <label for="password" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-200 mb-1.5">
            <svg class="h-4 w-4 text-gray-500 dark:text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
              </path>
            </svg>
            Password <span class="text-red-500 ml-0.5">*</span>
          </label>
          <div class="relative">
            <input id="password" name="password" type="password" [(ngModel)]="user.password" #password="ngModel"
              required minlength="8" (ngModelChange)="onPasswordChange($event)" placeholder="Create a strong password"
              class="w-full pl-10 pr-4 py-2.5 outline-none border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              [class.border-red-500]="getFieldError('password')" />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z">
                </path>
              </svg>
            </div>
          </div>
          <div *ngIf="getFieldError('password')" class="mt-1.5 text-xs text-red-600">
            {{ getFieldError('password') }}
          </div>

          <!-- Enhanced Password Strength Indicator -->
          <div *ngIf="passwordStrength && user.password" class="mt-2">
            <div class="flex items-center space-x-2">
              <div class="flex-1 bg-gray-100 dark:bg-gray-600 rounded-full h-1.5 overflow-hidden">
                <div class="h-full rounded-full transition-all duration-300"
                  [style.width.%]="(passwordStrength.score / 5) * 100" [ngClass]="{
                    'bg-gradient-to-r from-red-400 to-red-500': passwordStrength.score <= 2,
                    'bg-gradient-to-r from-yellow-300 to-yellow-400': passwordStrength.score === 3,
                    'bg-gradient-to-r from-green-400 to-green-500': passwordStrength.score >= 4
                  }">
                </div>
              </div>
              <span class="text-xs font-medium" [ngClass]="{
                      'text-red-600': passwordStrength.score <= 2,
                      'text-yellow-600': passwordStrength.score === 3,
                      'text-green-600': passwordStrength.score >= 4
                    }">
                {{ getPasswordStrengthText() }}
              </span>
            </div>
            <div *ngIf="passwordStrength.feedback.length > 0" class="mt-1">
              <ul class="text-xs text-gray-500 dark:text-gray-400 space-y-0.5">
                <li *ngFor="let feedback of passwordStrength.feedback" class="flex items-start">
                  <svg class="h-3 w-3 text-blue-500 dark:text-blue-400 mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span>{{ feedback }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Role Selection -->
        <div>
          <label for="role" class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
            Role <span class="text-red-500">*</span>
          </label>
          <select id="role" name="role" [(ngModel)]="user.role" #role="ngModel" required (change)="onRoleSelect($event)"
            class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            [class.border-red-500]="getFieldError('role')">
            <option value="">Select a role</option>
            <option value="Admin" data-custom="admin-data">Admin</option>
            <option value="Doctor" data-custom="doctor-data">Doctor</option>
            <option value="User" data-custom="user-data">User</option>
          </select>
          <div *ngIf="getFieldError('role')" class="mt-2 text-sm text-red-600">
            {{ getFieldError('role') }}
          </div>
        </div>
        <div class="md:col-span-2 mt-6">
          <div>
            <label for="skills" class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
              Skills
            </label>
            <textarea id="skills" name="skills" [(ngModel)]="user.skills" rows="3"
              placeholder="Skills will be auto-populated based on the selected role"
              class="w-full px-4 py-3 outline-none border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"></textarea>
            <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
              These skills will be associated with the user's profile. You can modify them as needed.
            </div>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div *ngIf="errorMessage" class="mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex">
          <svg class="h-5 w-5 text-red-400 dark:text-red-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd"></path>
          </svg>
          <span class="text-sm text-red-700 dark:text-red-200">{{ errorMessage }}</span>
        </div>
      </div>

      <!-- Success Message -->
      <div *ngIf="successMessage" class="mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <div class="flex">
          <svg class="h-5 w-5 text-green-400 dark:text-green-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd"></path>
          </svg>
          <span class="text-sm text-green-700 dark:text-green-200">{{ successMessage }}</span>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="mt-8 flex justify-end space-x-4">
        <app-button type="button" variant="secondary" (clicked)="onCancel()">
          Cancel
        </app-button>

        <app-button type="submit" variant="primary" [loading]="isSubmitting"
          [disabled]="userForm.invalid || !user.role">
          Create User
        </app-button>
      </div>
    </form>
  </div>
