import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, map, take, of } from 'rxjs';
import { AuthService } from '../../shared/services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild {

  constructor(
    private authService: AuthService,
    private router: Router
  ) { }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    return this.checkAuth(route, state);
  }

  canActivateChild(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    return this.checkAuth(route, state);
  }

  private checkAuth(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    console.log('AuthGuard: Checking authentication for route:', state.url);

    // Check if we have a token first (for page reload scenarios)
    const token = this.authService.getToken();
    if (!token) {
      console.log('AuthGuard: No token found, redirecting to login');
      this.saveRequestedUrl(state.url);
      this.router.navigate(['/auth/login'], {
        queryParams: { returnUrl: state.url }
      });
      return of(false);
    }

    // Check if token is valid
    if (!this.isTokenValid()) {
      console.log('AuthGuard: Token is invalid, redirecting to login');
      this.authService.logout();
      this.saveRequestedUrl(state.url);
      this.router.navigate(['/auth/login'], {
        queryParams: { returnUrl: state.url }
      });
      return of(false);
    }

    console.log('AuthGuard: Valid token found, proceeding with role checks');

    // For users with only the "User" role (patients), deny access to web app
    if (this.authService.isOnlyUserRole()) {
      console.log('Auth guard: Patient user trying to access web app - logging out and redirecting');
      this.authService.logout(); // Clear the session
      this.router.navigate(['/auth/login'], {
        queryParams: { error: 'patient_access_denied' }
      });
      return of(false);
    }

    // Check if user has valid roles for web app (Admin/Doctor only)
    const userRoles = this.authService.getUserRoles();
    const allowedRoles = ['Admin', 'Doctor'];
    const hasValidRole = allowedRoles.some(role => userRoles && userRoles.includes(role));

    if (!hasValidRole) {
      console.log('Auth guard: User does not have admin/doctor role - logging out and redirecting');
      this.authService.logout(); // Clear the invalid session
      this.router.navigate(['/auth/login'], {
        queryParams: { error: 'unauthorized_role' }
      });
      return of(false);
    }

    // Check for required roles
    const requiredRoles = route.data?.['roles'] as string[];
    if (requiredRoles && requiredRoles.length > 0) {
      const userRoles = this.authService.getUserRoles();
      const hasRequiredRole = requiredRoles.some(role =>
        userRoles && userRoles.includes(role)
      );

      // Check if user has the 'User' role to potentially show app download banner
      this.checkForAppDownloadBanner(userRoles);

      if (!hasRequiredRole) {
        console.log('Auth guard: User lacks required role - redirecting to unauthorized');
        this.router.navigate(['/unauthorized']);
        return of(false);
      }
    }

    // Check if user is admin for admin-only routes
    const adminOnly = route.data?.['adminOnly'] === true;
    if (adminOnly && !this.authService.isAdmin()) {
      console.log('Auth guard: Admin-only route - redirecting to unauthorized');
      this.router.navigate(['/unauthorized']);
      return of(false);
    }

    // User is authenticated and authorized for this route
    console.log('Auth guard: Access granted to route', state.url);
    return of(true);
  }

  /**
   * Check if the token is valid and not expired
   */
  private isTokenValid(): boolean {
    // During hard refresh, be more lenient with token validation
    const token = this.authService.getToken();
    if (!token) {
      return false;
    }

    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return false;
      }

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      // Only check if token is actually expired (no buffer for auth guard)
      return payload['exp'] && payload['exp'] > currentTime;
    } catch (error) {
      return false;
    }
  }

  /**
   * Save the requested URL before redirecting to login
   */
  private saveRequestedUrl(url: string): void {
    // Store the attempted URL for redirecting after successful login
    if (url && url !== '/') {
      sessionStorage.setItem('redirectUrl', url);
    }
  }

  /**
   * Check if the user has the "User" role to potentially show the app download banner
   */
  private checkForAppDownloadBanner(userRoles: string[]): void {
    if (userRoles.includes('User') && !userRoles.includes('Admin')) {
      // If they are a regular user, set a flag to show the app download banner
      // Only set if it hasn't been dismissed by the user
      if (!localStorage.getItem('app_download_banner_dismissed')) {
        sessionStorage.setItem('show_app_download_banner', 'true');
      }
    }
  }
}

@Injectable({
  providedIn: 'root'
})
export class GuestGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) { }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | boolean {
    // Check if user is already logged in
    if (this.authService.isUserLoggedIn) {
      console.log('Guest guard: User already logged in - redirecting to dashboard');

      // Get saved redirect URL or use dashboard as default
      const redirectUrl = sessionStorage.getItem('redirectUrl') || '/dashboard';
      sessionStorage.removeItem('redirectUrl'); // Clear it after use

      this.router.navigate([redirectUrl]);
      return false;
    }

    // User is not logged in, allow access to guest-only routes
    return true;
  }
}
