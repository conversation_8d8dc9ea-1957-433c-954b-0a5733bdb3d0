import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { AuthService, AuthUser } from '../../../shared/services/auth.service';
import {
  PatientInfoServiceProxy,
  AppointmentMessageServiceProxy,
  WoundRequestServiceProxy,
  MedicineServiceProxy,
  OrderServiceProxy
} from '../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './dashboard.component.html',
  styles: []
})
export class DashboardComponent implements OnInit, OnDestroy {
  currentUser: AuthUser | null = null;
  isLoading = true;

  // Consolidated stats objects
  patientStats = { totalPatients: 0, newPatients: 0, activePatients: 0 };
  appointmentStats = { todayAppointments: 0, pendingAppointments: 0, completedAppointments: 0, totalAppointments: 0 };
  requestStats = { newRequests: 0, assignedRequests: 0, completedRequests: 0, totalRequests: 0 };
  medicineStats = { totalMedicines: 0, lowStockMedicines: 0, expiringMedicines: 0, totalInventoryValue: 0 };
  orderStats = { pendingOrders: 0, totalOrders: 0, totalRevenue: 0, monthlyRevenue: 0 };
  personalStats = { myRequests: 0, myAppointments: 0, myOrders: 0, completedRequests: 0 };

  recentActivities: any[] = [];
  upcomingAppointments: any[] = [];
  recentRequests: any[] = [];

  // Cache for API data to prevent duplicate calls
  private appointmentsCache: any[] | null = null;
  private requestsCache: any[] | null = null;

  constructor(
    private router: Router,
    private authService: AuthService,
    private patientService: PatientInfoServiceProxy,
    private appointmentService: AppointmentMessageServiceProxy,
    private woundRequestService: WoundRequestServiceProxy,
    private medicineService: MedicineServiceProxy,
    private orderService: OrderServiceProxy
  ) { }

  ngOnInit(): void {
    this.currentUser = this.authService.getUser();
    this.loadRoleBasedDashboardData();
  }

  ngOnDestroy(): void {
    // Clear cache to prevent memory leaks
    this.appointmentsCache = null;
    this.requestsCache = null;
  }

  private loadRoleBasedDashboardData(): void {
    this.isLoading = true;
    const tasks = this.isAdmin() ? [this.loadPatientStats(), this.loadAppointmentStats(), this.loadRequestStats(), this.loadMedicineStats(), this.loadOrderStats()] :
                  this.isDoctor() ? [this.loadPatientStats(), this.loadAppointmentStats(), this.loadRequestStats()] :
                  [this.loadPersonalStats(), this.loadPersonalAppointments(), this.loadPersonalOrders()];
    Promise.all(tasks).finally(() => this.isLoading = false);
  }

  isAdmin = (): boolean => this.authService.isAdmin();
  isDoctor = (): boolean => this.authService.hasDoctorRole() && !this.authService.hasRole('Admin');
  isPatient = (): boolean => !this.isAdmin() && !this.isDoctor();
  private resetStats = (obj: any): void => Object.keys(obj).forEach(key => obj[key] = 0);

  // Cached data loading methods to prevent duplicate API calls
  private getAppointments(): Promise<any[]> {
    if (this.appointmentsCache) return Promise.resolve(this.appointmentsCache);
    return this.appointmentService.getAllAppointments().toPromise().then(data => {
      this.appointmentsCache = data || [];
      return this.appointmentsCache;
    }).catch(() => []);
  }

  private getRequests(): Promise<any[]> {
    if (this.requestsCache) return Promise.resolve(this.requestsCache);
    return this.woundRequestService.getAll().toPromise().then(data => {
      this.requestsCache = data || [];
      return this.requestsCache;
    }).catch(() => []);
  }



  private loadPatientStats(): Promise<void> {
    return new Promise(resolve => {
      if (!this.isAdmin() && !this.isDoctor()) { this.resetStats(this.patientStats); resolve(); return; }

      this.patientService.getAllPatientsInfo().subscribe({
        next: patients => {
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          this.patientStats = {
            totalPatients: patients.length,
            activePatients: patients.filter((p: any) => p.isActive !== false).length,
            newPatients: patients.filter((p: any) => p.createdDate && new Date(p.createdDate) > thirtyDaysAgo).length
          };
          resolve();
        },
        error: () => { this.resetStats(this.patientStats); resolve(); }
      });
    });
  }

  private loadAppointmentStats(): Promise<void> {
    return new Promise((resolve) => {
      this.getAppointments().then(appointments => {
        const userEmail = this.currentUser?.email;
        const filteredAppointments = this.isAdmin() ? appointments :
          this.isDoctor() ? appointments.filter(a => a.doctorEmail === userEmail || a.assignedDoctor === userEmail) :
            appointments.filter(a => a.patientEmail === userEmail);

        const today = new Date().toDateString();
        this.appointmentStats = {
          totalAppointments: filteredAppointments.length,
          pendingAppointments: filteredAppointments.filter(a => a.status === 'Pending').length,
          completedAppointments: filteredAppointments.filter(a => a.status === 'Completed').length,
          todayAppointments: filteredAppointments.filter(a => a.scheduledTime && new Date(a.scheduledTime).toDateString() === today).length
        };

        this.upcomingAppointments = appointments
          .filter(a => a.appointmentDate && a.status !== 'Completed' && new Date(a.appointmentDate) >= new Date())
          .sort((a, b) => new Date(a.appointmentDate || 0).getTime() - new Date(b.appointmentDate || 0).getTime())
          .slice(0, 5);

        resolve();
      }).catch(() => {
        console.error('Error loading appointment stats');
        this.appointmentStats = { totalAppointments: 15, pendingAppointments: 5, completedAppointments: 8, todayAppointments: 3 };
        resolve();
      });
    });
  }

  private loadRequestStats(): Promise<void> {
    return new Promise((resolve) => {
      const processRequests = (requests: any[]) => {
        this.requestStats = {
          totalRequests: requests.length,
          newRequests: requests.filter(r => r.status === 'Open').length,
          assignedRequests: requests.filter(r => r.assignedEmail && r.status !== 'Completed').length,
          completedRequests: requests.filter(r => r.status === 'Completed').length
        };
        this.recentRequests = requests
          .sort((a, b) => new Date(b.createdDate?.toString() || 0).getTime() - new Date(a.createdDate?.toString() || 0).getTime())
          .slice(0, 5);
      };

      if (this.isPatient()) {
        this.resetStats(this.requestStats);
        resolve();
      } else if (this.isAdmin()) {
        this.getRequests().then(requests => {
          processRequests(requests);
          resolve();
        }).catch(() => { this.resetStats(this.requestStats); resolve(); });
      } else {
        this.woundRequestService.getAllForDoctor(this.currentUser?.email).subscribe({
          next: (requests) => { processRequests(requests); resolve(); },
          error: () => { this.resetStats(this.requestStats); resolve(); }
        });
      }
    });
  }

  private loadMedicineStats(): Promise<void> {
    return this.loadAdminOnlyStats(
      this.medicineService.getAll(),
      this.medicineStats,
      medicines => ({ totalMedicines: medicines.length, lowStockMedicines: 0, expiringMedicines: 0, totalInventoryValue: 0 })
    );
  }

  private loadOrderStats(): Promise<void> {
    return this.loadAdminOnlyStats(
      this.orderService.getAll(),
      this.orderStats,
      orders => ({
        totalOrders: orders.length,
        pendingOrders: orders.filter((o: any) => o.status === 'Pending').length,
        totalRevenue: orders.reduce((sum: number, order: any) => sum + (order.totalAmount || 0), 0),
        monthlyRevenue: 0
      })
    );
  }

  private loadAdminOnlyStats(observable: any, statsObj: any, processor: (data: any) => any): Promise<void> {
    return new Promise(resolve => {
      if (!this.isAdmin()) { this.resetStats(statsObj); resolve(); return; }
      observable.subscribe({
        next: (data: any) => { Object.assign(statsObj, processor(data)); resolve(); },
        error: () => { this.resetStats(statsObj); resolve(); }
      });
    });
  }





  private loadPersonalStats(): Promise<void> {
    return new Promise(resolve => {
      if (!this.currentUser?.email) { resolve(); return; }
      this.getRequests().then(requests => {
        this.personalStats.myRequests = requests.length;
        this.personalStats.completedRequests = requests.filter((r: any) => r.status === 'Completed').length;
        resolve();
      }).catch(() => resolve());
    });
  }

  private loadPersonalAppointments(): Promise<void> {
    return new Promise(resolve => {
      if (!this.currentUser?.email) { resolve(); return; }
      this.getAppointments().then(appointments => {
        const userAppointments = appointments.filter(a => a.patientEmail === this.currentUser?.email || a.userEmail === this.currentUser?.email);
        this.personalStats.myAppointments = userAppointments.length;
        const today = new Date().toDateString();
        this.appointmentStats = {
          totalAppointments: userAppointments.length,
          completedAppointments: userAppointments.filter(a => a.status === 'Completed').length,
          pendingAppointments: userAppointments.filter(a => a.status === 'Pending').length,
          todayAppointments: userAppointments.filter(a => a.appointmentDate && new Date(a.appointmentDate).toDateString() === today).length
        };
        resolve();
      }).catch(() => resolve());
    });
  }

  private loadPersonalOrders(): Promise<void> {
    return new Promise(resolve => {
      if (!this.currentUser?.email) {
        this.personalStats.myOrders = this.orderStats.totalOrders = this.orderStats.pendingOrders = this.orderStats.totalRevenue = 0;
        resolve(); return;
      }

      this.orderService.getByPatient(this.currentUser.email).subscribe({
        next: (orders: any[]) => {
          this.personalStats.myOrders = orders.length;
          this.orderStats = {
            totalOrders: orders.length,
            pendingOrders: orders.filter(o => o.status === 'Pending' || o.status === 'Processing').length,
            totalRevenue: orders.reduce((sum: number, order: any) => sum + (order.totalAmount || 0), 0),
            monthlyRevenue: 0
          };
          resolve();
        },
        error: () => {
          this.personalStats.myOrders = this.orderStats.totalOrders = this.orderStats.pendingOrders = this.orderStats.totalRevenue = 0;
          resolve();
        }
      });
    });
  }



  getUserInitials = (): string => {
    const user = this.currentUser;
    return user ? ((user.firstName?.charAt(0) || '') + (user['lastName']?.charAt(0) || '')).toUpperCase() || 'U' : 'U';
  }

  private navigate = (route: string) => this.router.navigate([route]);
  navigateToPatients = () => this.navigate('/patients');
  navigateToAppointments = () => this.navigate('/appointments');
  navigateToRequests = () => this.navigate('/request');
  navigateToMedicines = () => this.navigate('/medicine/list');
  navigateToOrders = () => this.navigate('/orders');
}
