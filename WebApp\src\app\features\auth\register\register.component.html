<div
  class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-600 to-indigo-800 dark:from-blue-950 dark:to-indigo-950 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto h-16 w-16 bg-white dark:bg-gray-800 dark:text-white rounded-full flex items-center justify-center mb-4 shadow-lg">
        <svg class="h-8 w-8 text-blue-600 dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
        </svg>
      </div>
      <h2 class="text-3xl font-bold text-white mb-2">Secure Registration</h2>
      <p class="text-blue-100">Create a new account with enhanced security</p>
    </div>

    <!-- Registration Form -->
    <div class="bg-white dark:bg-gray-800 dark:border-gray-900 rounded-xl shadow-lg p-8 border border-gray-100">
      <form #registerForm="ngForm" (ngSubmit)="onSubmit()" class="space-y-6" autocomplete="off">

        <!-- Name -->
        <div class="mb-4">
          <label for="name" class="block text-sm font-medium dark:text-white text-gray-700 mb-2">
            Full Name <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <input id="name" type="text" name="name" [(ngModel)]="registerData.name" #name="ngModel" required
              minlength="2" maxlength="100" placeholder="Enter your full name"
              class="w-full pl-10 px-4 py-3 outline-none border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              [class.border-red-500]="name.invalid && (name.dirty || name.touched)" />
          </div>
          <div *ngIf="name.invalid && (name.dirty || name.touched)" class="mt-2 text-sm text-red-600">
            <div *ngIf="name.errors?.['required']">Name is required</div>
            <div *ngIf="name.errors?.['minlength']">Name must be at least 2 characters</div>
          </div>
        </div>

        <!-- Email Address -->
        <div class="mb-4">
          <label for="email" class="block text-sm font-medium dark:text-white text-gray-700 mb-2">
            Email Address <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207">
                </path>
              </svg>
            </div>
            <input id="email" type="email" name="email" [(ngModel)]="registerData.email" #email="ngModel" required email
              placeholder="Enter your email"
              class="w-full pl-10 px-4 py-3 outline-none border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              [class.border-red-500]="email.invalid && (email.dirty || email.touched)" />
          </div>
          <div *ngIf="email.invalid && (email.dirty || email.touched)" class="mt-2 text-sm text-red-600">
            <div *ngIf="email.errors?.['required']">Email is required</div>
            <div *ngIf="email.errors?.['email']">Please enter a valid email address</div>
          </div>
        </div>

        <!-- Password -->
        <div class="mb-4">
          <label for="password" class="block text-sm font-medium dark:text-white text-gray-700 mb-2">
            Password <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                </path>
              </svg>
            </div>
            <input id="password" type="password" name="password" [(ngModel)]="registerData.password" #password="ngModel"
              required minlength="8" placeholder="Create a password"
              class="w-full pl-10 px-4 py-3 border outline-none border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              [class.border-red-500]="password.invalid && (password.dirty || password.touched)" />
          </div>
          <div *ngIf="password.invalid && (password.dirty || password.touched)" class="mt-2 text-sm text-red-600">
            <div *ngIf="password.errors?.['required']">Password is required</div>
            <div *ngIf="password.errors?.['minlength']">Password must be at least 8 characters long</div>
          </div>
        </div>

        <!-- Role -->
        <!-- <div class="mb-4">
          <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
            Role <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                </path>
              </svg>
            </div>
            <select id="role" name="role" [(ngModel)]="registerData.role" #role="ngModel" required
              class="w-full pl-10 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 appearance-none"
              [class.border-red-500]="role.invalid && (role.dirty || role.touched)">
              <option value="" disabled selected>Select your role</option>
              <option value="doctor">Doctor</option>
              <option value="nurse">Nurse</option>
              <option value="admin">Administrator</option>
              <option value="patient">Patient</option>
            </select>
          </div>
          <div *ngIf="role.invalid && (role.dirty || role.touched)" class="mt-2 text-sm text-red-600">
            <div *ngIf="role.errors?.['required']">Role is required</div>
          </div>
        </div> -->

        <!-- Skills -->
        <div class="mb-4">
          <label for="skills" class="block text-sm font-medium dark:text-white text-gray-700 mb-2">
            Skills <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z">
                </path>
              </svg>
            </div>
            <textarea id="skills" name="skills" [(ngModel)]="registerData.skills" #skills="ngModel" required
              placeholder="Enter your skills, separated by commas"
              class="w-full pl-10 px-4 py-3 border outline-none border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              [class.border-red-500]="skills.invalid && (skills.dirty || skills.touched)" rows="3"></textarea>
          </div>
          <div *ngIf="skills.invalid && (skills.dirty || skills.touched)" class="mt-2 text-sm text-red-600">
            <div *ngIf="skills.errors?.['required']">Skills are required</div>
          </div>
        </div>

        <!-- Error Message -->
        <div *ngIf="errorMessage" class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex">
            <svg class="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm text-red-700">{{ errorMessage }}</span>
          </div>
        </div>

        <!-- Success Message -->
        <div *ngIf="successMessage" class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="flex">
            <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm text-green-700">{{ successMessage }}</span>
          </div>
        </div>

        <!-- Submit Button -->
        <app-button type="submit" variant="primary" size="lg" [fullWidth]="true" [loading]="isLoading"
          [disabled]="!registerForm.form.valid">
          Create Account
        </app-button>
      </form>

      <!-- Sign In Link -->
      <div class="mt-6 text-center">
        <div class="border-t dark:border-gray-900 border-gray-200 pt-4">
          <p class="text-sm dark:text-white text-gray-600">
            Already have an account?
            <a routerLink="/auth/login"
              class="font-medium text-blue-500 hover:text-blue-500 transition-colors duration-200">
              Sign in here
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
