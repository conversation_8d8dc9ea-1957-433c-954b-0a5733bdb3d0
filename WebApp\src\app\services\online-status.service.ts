import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { HubConnection, HubConnectionBuilder, HubConnectionState, LogLevel } from '@microsoft/signalr';
import { AuthService } from '../../shared/services/auth.service';
import { getRemoteServiceBaseUrl } from '../app.config';

@Injectable({
  providedIn: 'root'
})
export class OnlineStatusService {
  private hubConnection: HubConnection | null = null;
  private readonly apiUrl = getRemoteServiceBaseUrl();

  // Super simple - just store emails of online users
  private onlineUsers = new Set<string>();
  private onlineUsersSubject = new BehaviorSubject<Set<string>>(new Set());

  // Public observable
  public onlineUsers$ = this.onlineUsersSubject.asObservable();

  // Heartbeat
  private heartbeatInterval: any;

  constructor(private authService: AuthService) {
    // Auto-connect when user is authenticated
    this.authService.user$.subscribe(user => {
      if (user && this.authService.isAuthenticated()) {
        console.log('🔄 OnlineStatusService: User authenticated, connecting...');
        setTimeout(() => this.connect(), 1000);
      } else {
        console.log('🔴 OnlineStatusService: User logged out or not authenticated, disconnecting...');
        // Force disconnect immediately on logout
        this.forceDisconnect();
      }
    });

    // Also check on page load if user is already authenticated
    if (this.authService.isAuthenticated()) {
      console.log('🔄 OnlineStatusService: User already authenticated on load, connecting...');
      setTimeout(() => this.connect(), 2000);
    }

    // Listen for logout events from AuthService
    window.addEventListener('auth-logout', (event: any) => {
      console.log('🚨 OnlineStatusService: Received logout event, handling immediately');
      this.handleLogout();
    });

    // Handle page unload
    window.addEventListener('beforeunload', () => {
      this.disconnect();
    });
  }

  /**
   * Connect to SignalR hub
   */
  async connect(): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      console.log('🔄 OnlineStatusService: Already connected');
      return;
    }

    // Don't try to connect if we're already connecting
    if (this.hubConnection?.state === HubConnectionState.Connecting) {
      console.log('🔄 OnlineStatusService: Already connecting');
      return;
    }

    try {
      const token = this.getTokenFromCookie();
      if (!token) {
        console.warn('⚠️ OnlineStatusService: No token available');
        // Check if user is actually authenticated before retrying
        if (this.authService.isAuthenticated()) {
          console.log('User is authenticated but no token in cookie, retrying in 5 seconds...');
          setTimeout(() => this.connect(), 5000);
        } else {
          console.log('User is not authenticated, not retrying connection');
        }
        return;
      }

      console.log('🔄 OnlineStatusService: Starting connection with token:', token.substring(0, 20) + '...');

      this.hubConnection = new HubConnectionBuilder()
        .withUrl(`${this.apiUrl}/doctorpatientchathub`, {
          accessTokenFactory: () => {
            const currentToken = this.getTokenFromCookie();
            console.log('🔑 OnlineStatusService: Token factory called, token available:', !!currentToken);
            return currentToken || '';
          },
          withCredentials: false,
          transport: 1,
          skipNegotiation: true
        })
        .withAutomaticReconnect([0, 2000, 10000, 30000]) // Custom retry delays
        .configureLogging(LogLevel.Information)
        .build();

      this.setupEventHandlers();
      this.setupConnectionHandlers();

      await this.hubConnection.start();
      console.log('✅ OnlineStatusService: Connected successfully');

      this.startHeartbeat();
      this.requestOnlineUsers();

    } catch (error) {
      console.error('❌ OnlineStatusService: Connection failed:', error);
      // Retry connection after 10 seconds on failure
      setTimeout(() => this.connect(), 10000);
    }
  }

  /**
   * Disconnect from SignalR hub
   */
  async disconnect(): Promise<void> {
    if (this.hubConnection) {
      try {
        this.stopHeartbeat();
        await this.hubConnection.stop();
        console.log('🔴 OnlineStatusService: Disconnected');
      } catch (error) {
        console.error('❌ OnlineStatusService: Error during disconnect:', error);
      }
    }
    this.onlineUsers.clear();
    this.onlineUsersSubject.next(new Set(this.onlineUsers));
  }

  /**
   * Force disconnect immediately (used for logout)
   */
  private async forceDisconnect(): Promise<void> {
    console.log('🚨 OnlineStatusService: Force disconnecting due to logout...');

    // Stop heartbeat immediately
    this.stopHeartbeat();

    // Clear online users immediately
    this.onlineUsers.clear();
    this.onlineUsersSubject.next(new Set(this.onlineUsers));

    // Force stop SignalR connection
    if (this.hubConnection && this.hubConnection.state === HubConnectionState.Connected) {
      try {
        // Try to explicitly notify server before disconnecting
        const userEmail = this.authService.getUser()?.email;
        if (userEmail) {
          console.log('📤 OnlineStatusService: Notifying server of logout for user:', userEmail);
          // Give a short timeout to send the logout notification
          await Promise.race([
            this.hubConnection.invoke('NotifyLogout', userEmail),
            new Promise(resolve => setTimeout(resolve, 1000)) // 1 second timeout
          ]);
        }
      } catch (error) {
        console.warn('⚠️ OnlineStatusService: Could not notify server of logout:', error);
      }

      try {
        // Force stop the connection with short timeout
        await Promise.race([
          this.hubConnection.stop(),
          new Promise(resolve => setTimeout(resolve, 2000)) // 2 second timeout
        ]);
        console.log('✅ OnlineStatusService: Force disconnect completed');
      } catch (error) {
        console.error('❌ OnlineStatusService: Error during force disconnect:', error);
      } finally {
        // Ensure connection is nullified regardless of what happens
        this.hubConnection = null;
      }
    } else if (this.hubConnection) {
      // Connection exists but not connected, just nullify it
      console.log('🔄 OnlineStatusService: Connection not active, nullifying...');
      this.hubConnection = null;
    }

    // Final safety check - clear any remaining intervals
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Setup SignalR event handlers - super simple
   */
  private setupEventHandlers(): void {
    if (!this.hubConnection) return;

    // Simple events - just email strings
    this.hubConnection.on('AllOnlineUsers', (emails: string[]) => {
      console.log('👥 WebApp OnlineStatusService: All online users:', emails);
      this.onlineUsers.clear();
      emails.forEach(email => this.onlineUsers.add(email));
      this.onlineUsersSubject.next(new Set(this.onlineUsers));
    });

    this.hubConnection.on('UserOnline', (email: string) => {
      console.log('✅ WebApp OnlineStatusService: User online:', email);
      this.onlineUsers.add(email);
      this.onlineUsersSubject.next(new Set(this.onlineUsers));
    });

    this.hubConnection.on('UserOffline', (email: string) => {
      console.log('❌ WebApp OnlineStatusService: User offline:', email);
      this.onlineUsers.delete(email);
      this.onlineUsersSubject.next(new Set(this.onlineUsers));
    });

    this.hubConnection.on('UserOnlineStatus', (email: string, isOnline: boolean) => {
      console.log('👤 WebApp OnlineStatusService: User status:', email, isOnline);
      if (isOnline) {
        this.onlineUsers.add(email);
      } else {
        this.onlineUsers.delete(email);
      }
      this.onlineUsersSubject.next(new Set(this.onlineUsers));
    });
  }

  /**
   * Setup connection state handlers
   */
  private setupConnectionHandlers(): void {
    if (!this.hubConnection) return;

    this.hubConnection.onclose((error) => {
      console.log('🔴 OnlineStatusService: Connection closed', error);
      this.onlineUsers.clear();
      this.onlineUsersSubject.next(new Set(this.onlineUsers));
    });

    this.hubConnection.onreconnecting((error) => {
      console.log('🔄 OnlineStatusService: Reconnecting...', error);
    });

    this.hubConnection.onreconnected((connectionId) => {
      console.log('✅ OnlineStatusService: Reconnected with connectionId:', connectionId);
      // Add delay to ensure server is ready before requesting online users
      setTimeout(() => {
        this.requestOnlineUsers(); // Re-request online users after reconnection
        this.startHeartbeat(); // Restart heartbeat to maintain status
      }, 1000);
    });
  }

  /**
   * Start heartbeat
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();

    this.heartbeatInterval = setInterval(async () => {
      if (this.hubConnection?.state === HubConnectionState.Connected) {
        try {
          await this.hubConnection.invoke('UpdateOnlineStatus');
        } catch (error) {
          console.error('❌ OnlineStatusService: Heartbeat failed:', error);
        }
      }
    }, 10000); // 10 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Request current online users
   */
  private async requestOnlineUsers(): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('GetOnlineUsers');
      } catch (error) {
        console.error('❌ OnlineStatusService: Failed to get online users:', error);
      }
    }
  }

  /**
   * Check if user is online - super simple
   */
  isUserOnline(email: string): boolean {
    return this.onlineUsers.has(email);
  }

  /**
   * Get all online users
   */
  getAllOnlineUsers(): string[] {
    return Array.from(this.onlineUsers);
  }

  /**
   * Public method to handle logout - can be called directly from AuthService
   */
  public handleLogout(): void {
    console.log('🚨 OnlineStatusService: Explicit logout called');

    // Use a more aggressive approach for explicit logout
    this.stopHeartbeat();

    // Clear online users immediately
    this.onlineUsers.clear();
    this.onlineUsersSubject.next(new Set(this.onlineUsers));

    // Try multiple approaches to ensure logout notification
    if (this.hubConnection) {
      const userEmail = this.authService.getUser()?.email;

      if (userEmail && this.hubConnection.state === HubConnectionState.Connected) {
        // Fire-and-forget approach - don't wait
        this.hubConnection.invoke('NotifyLogout', userEmail).catch(error => {
          console.warn('⚠️ OnlineStatusService: Logout notification failed:', error);
        });

        // Give very short time then force stop
        setTimeout(() => {
          if (this.hubConnection) {
            this.hubConnection.stop().catch(() => { });
            this.hubConnection = null;
          }
        }, 500); // Only 500ms
      } else {
        // Connection not ready, just stop it
        this.hubConnection.stop().catch(() => { });
        this.hubConnection = null;
      }
    }

    console.log('✅ OnlineStatusService: Explicit logout completed');
  }

  /**
   * Get JWT token from cookies (same method as AuthService)
   */
  private getTokenFromCookie(): string | null {
    try {
      const TOKEN_KEY = 'jwt_token';
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, ...valueParts] = cookie.trim().split('=');
        if (name === TOKEN_KEY) {
          const value = valueParts.join('='); // Handle tokens with = in them
          if (value && value.length > 0) {
            return value;
          }
        }
      }
      return null;
    } catch (error) {
      console.error('OnlineStatusService: Error reading token from cookies:', error);
      return null;
    }
  }
}
