/* Card styles */
.educational-card {
  transition: all 0.3s ease;
  overflow: hidden;
}

.educational-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Image hover effects */
.educational-card:hover .card-image img {
  transform: scale(1.05);
}

.card-image img {
  transition: transform 0.3s ease;
}

/* Line clamp utility for content */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Content title styling */
.content-title {
  font-weight: 600;
  line-height: 1.4;
  transition: color 0.2s ease;
}

/* Content preview styling */
.content-preview {
  line-height: 1.5;
  transition: color 0.2s ease;
}

/* Ensure proper color inheritance for dynamic content */
:host ::ng-deep .content-title * {
  color: inherit !important;
}

:host ::ng-deep .content-preview * {
  color: inherit !important;
}

/* Empty content styling with theme support */
.empty-content {
  font-style: italic;
  opacity: 0.7;
}

/* Force dark mode colors when needed */
:host ::ng-deep .dark .content-title {
  color: #f3f4f6 !important;
}

:host ::ng-deep .dark .content-preview {
  color: #e5e7eb !important;
}

:host ::ng-deep .dark .empty-content {
  color: #9ca3af !important;
}

/* Smooth transitions for theme changes */
.content-title,
.content-preview {
  transition: color 0.2s ease-in-out;
}

/* Badge animations */
.category-badge {
  transition: all 0.2s ease;
}

.category-badge:hover {
  transform: scale(1.05);
}

/* Button hover effects */
button:not(:disabled) {
  transition: all 0.2s ease;
}

button:not(:disabled):hover {
  transform: translateY(-1px);
}

/* Pagination styling */
.pagination-button-active {
  background-color: #ebf5ff;
  border-color: #3b82f6;
  color: #2563eb;
  font-weight: 500;
}

/* Image hover effects */
.image-container {
  overflow: hidden;
}

.image-container img {
  transition: transform 0.5s ease;
}

.image-container:hover img {
  transform: scale(1.05);
}

/* Image error handling */
.image-error {
  position: relative;
  background-color: #f3f4f6; /* gray-100 */
  height: 100%;
}

.image-error::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(107, 114, 128, 0.1); /* gray-500 with opacity */
}
