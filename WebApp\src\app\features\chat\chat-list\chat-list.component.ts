import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { DoctorPatientChatServiceProxy, ChatThreadDto, AvailablePatientDto, ChatMessageResponseDto } from '../../../../shared/service-proxies/service-proxies';
import { PatientChatService } from '../../../services/patient-chat.service';
import { ChatCacheService } from '../../../services/chat-cache.service';
import { OnlineStatusService } from '../../../services/online-status.service';
import { GlobalMessageNotificationService } from '../../../services/global-message-notification.service';
import { LocalTimePipe } from '../../../shared/pipes/local-time.pipe';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'

@Component({
  selector: 'app-chat-list',
  standalone: true,
  imports: [CommonModule, FormsModule, LocalTimePipe, MatIconModule, MatProgressSpinnerModule],
  templateUrl: './chat-list.component.html',
  styleUrls: ['./chat-list.component.css']
})
export class ChatListComponent implements OnInit, OnDestroy {
  @Output() chatSelected = new EventEmitter<{ doctorId: string; patientId: string }>();
  @Input() selectedDoctorId: string | null = null; // Currently active doctor ID
  @Input() selectedPatientId: string | null = null; // Currently active patient ID

  chatThreads: ChatThreadDto[] = [];
  availablePatients: AvailablePatientDto[] = [];
  filteredPatients: AvailablePatientDto[] = [];
  searchTerm: string = '';
  loading = true;
  error: string | null = null;
  showNewChatModal = false;
  selectedThreadId: string | null = null; // Track which chat is being opened
  private destroy$ = new Subject<void>();

  constructor(
    private chatServiceProxy: DoctorPatientChatServiceProxy,
    private chatService: PatientChatService,
    private chatCacheService: ChatCacheService,
    private onlineStatusService: OnlineStatusService,
    private globalMessageService: GlobalMessageNotificationService,
    private cdr: ChangeDetectorRef
  ) { }

  async ngOnInit(): Promise<void> {
    // Initialize SignalR connection
    await this.initializeSignalR();
    this.loadData();
    this.subscribeToGlobalMessageService();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  public loadData(): void {
    this.loading = true;
    this.error = null;

    // Try to load from cache first
    this.loadFromCacheOrApi();
  }

  private loadFromCacheOrApi(): void {
    // Check cache for chat threads
    const cachedThreads = this.chatCacheService.getCachedChatThreads();
    const cachedPatients = this.chatCacheService.getCachedAvailablePatients();

    if (cachedThreads && cachedPatients) {
      // Use cached data
      console.log('📦 Using cached chat data');
      this.chatThreads = cachedThreads;
      this.availablePatients = this.filterAvailablePatients(cachedPatients);
      this.filteredPatients = [...this.availablePatients]; // Initialize filtered list
      this.loading = false;
    } else {
      // Load from API and cache the results
      console.log('🌐 Loading chat data from API');
      this.loadChatThreads().then(() => {
        this.loadAvailablePatients();
      });
    }
  }

  private loadAvailablePatients(): void {
    // Check if already loading to prevent duplicate calls
    if (this.chatCacheService.isLoadingAvailablePatientsState()) {
      console.log('⚠️ WEBAPP: Already loading available patients, skipping duplicate call');
      return;
    }

    console.log('🔍 WEBAPP: Loading available patients from API...');
    console.log('🔍 WEBAPP: Call stack:', new Error().stack);

    this.chatCacheService.setLoadingAvailablePatients(true);
    this.chatServiceProxy.getAvailablePatients()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (patients) => {
          console.log('🏥 Available patients loaded from API:', patients);
          console.log('🔍 Patient count:', patients.length);
          console.log('📋 First patient sample:', patients[0]);
          // Cache the patients data
          this.chatCacheService.setAvailablePatients(patients);
          // Filter out patients that already have active chats
          this.availablePatients = this.filterAvailablePatients(patients);
          this.filteredPatients = [...this.availablePatients]; // Initialize filtered list
          console.log('✅ Filtered available patients:', this.availablePatients);
          this.loading = false;
          // Reset loading state
          this.chatCacheService.setLoadingAvailablePatients(false);
        },
        error: (error: any) => {
          console.error('❌ Error loading available patients from API:', error);
          console.error('🔧 Status:', error.status);
          console.error('🔧 Message:', error.message);
          this.loading = false;
          // Reset loading state
          this.chatCacheService.setLoadingAvailablePatients(false);
          // For testing, let's create some mock data if the API fails
          console.warn('⚠️ API failed, creating mock patients for testing...');
          this.createMockPatients();
        }
      });
  }

  private filterAvailablePatients(patients: AvailablePatientDto[]): AvailablePatientDto[] {
    // Get list of patient IDs that already have active chats
    const existingPatientIds = this.chatThreads.map(thread => thread.patientId);

    // Filter out patients that already have active chats
    const filteredPatients = patients.filter(patient => !existingPatientIds.includes(patient.id));

    console.log('Filtered patients (excluding existing chats):', filteredPatients);
    return filteredPatients;
  }

  private createMockPatients(): void {
    console.log('Creating mock patients for testing...');
    this.availablePatients = [
      new AvailablePatientDto({
        id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
        fullName: 'John Doe',
        email: '<EMAIL>',
        phoneNumber: '******-1001',
        dateOfBirth: undefined,
        isOnline: false, // Not used anymore, but keeping for DTO compatibility
        profilePicture: undefined,
        address: '123 Main St, City, State',
        emergencyContact: '******-9001'
      }),
      new AvailablePatientDto({
        id: 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
        fullName: 'Jane Smith',
        email: '<EMAIL>',
        phoneNumber: '******-1002',
        dateOfBirth: undefined,
        isOnline: false, // Not used anymore, but keeping for DTO compatibility
        profilePicture: undefined,
        address: '456 Oak Ave, City, State',
        emergencyContact: '******-9002'
      }),
      new AvailablePatientDto({
        id: 'cccccccc-cccc-cccc-cccc-cccccccccccc',
        fullName: 'Robert Johnson',
        email: '<EMAIL>',
        phoneNumber: '******-1003',
        dateOfBirth: undefined,
        isOnline: false, // Not used anymore, but keeping for DTO compatibility
        profilePicture: undefined,
        address: '789 Pine Rd, City, State',
        emergencyContact: '******-9003'
      })
    ];
    this.filteredPatients = [...this.availablePatients]; // Initialize filtered list
    console.log('Mock patients created:', this.availablePatients);
  }

  private loadChatThreads(): Promise<void> {
    // Check if already loading to prevent duplicate calls
    if (this.chatCacheService.isLoadingChatThreadsState()) {
      console.log('⚠️ WEBAPP: Already loading chat threads, skipping duplicate call');
      return Promise.resolve();
    }

    console.log('🔍 WEBAPP: Loading existing chat threads from API...');
    console.log('🔍 WEBAPP: Call stack:', new Error().stack);

    this.chatCacheService.setLoadingChatThreads(true);
    return new Promise((resolve) => {
      this.chatServiceProxy.getMyChats()
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (threads: ChatThreadDto[]) => {
            console.log('Chat threads loaded:', threads);
            // Cache the threads data
            this.chatCacheService.setChatThreads(threads);
            this.chatThreads = threads;

            // Initialize global message service with unread counts
            this.globalMessageService.initializeUnreadCounts(threads);

            // Preload patient emails for existing threads for online status
            this.preloadPatientEmailsForThreads(threads);

            // Reset loading state
            this.chatCacheService.setLoadingChatThreads(false);
            resolve();
          },
          error: (error: any) => {
            console.error('Error loading chat threads:', error);
            this.chatThreads = []; // Set empty array on error
            // Reset loading state
            this.chatCacheService.setLoadingChatThreads(false);
            resolve(); // Still resolve to continue with loading patients
          }
        });
    });
  }

  openChat(thread: ChatThreadDto): void {
    // Set loading state for this specific thread
    this.selectedThreadId = `${thread.doctorId}-${thread.patientId}`;

    // Emit chat selection event
    this.chatSelected.emit({
      doctorId: thread.doctorId,
      patientId: thread.patientId
    });

    // Clear loading state after a short delay (will be cleared when chat loads)
    setTimeout(() => {
      this.selectedThreadId = null;
    }, 2000);
  }

  formatLastMessageDate(localDate: Date | null): string {
    if (!localDate) return '';

    const now = new Date();
    const messageDate = localDate;

    // Ensure we have a valid date
    if (isNaN(messageDate.getTime())) {
      return 'Invalid date';
    }

    const diffInMs = now.getTime() - messageDate.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    // Fix the logic - if difference is negative, it means future time (timezone issue)
    if (diffInMs < 0) {
      return 'Just now'; // Treat future dates as "just now"
    }

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      // For older messages, show the actual date
      return messageDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: messageDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  }

  getDisplayName(thread: ChatThreadDto): string {
    // For doctors, show patient name prominently
    return thread.patientName || 'Unknown Patient';
  }

  getPatientName(thread: ChatThreadDto): string {
    return thread.patientName || 'Unknown Patient';
  }

  getThreadPatientInitials(thread: ChatThreadDto): string {
    const name = thread.patientName || 'Unknown Patient';
    return name.split(' ').map((n: string) => n[0]).join('').toUpperCase().substring(0, 2);
  }

  getLastMessagePreview(message: string | undefined): string {
    if (!message) return 'No messages yet';
    return message.length > 50 ? message.substring(0, 50) + '...' : message;
  }

  hasUnreadMessages(thread: ChatThreadDto): boolean {
    // Use global service for accurate unread count
    const globalCount = this.globalMessageService.getUnreadCount(thread.doctorId, thread.patientId);
    return globalCount > 0;
  }

  isThreadLoading(thread: ChatThreadDto): boolean {
    return this.selectedThreadId === `${thread.doctorId}-${thread.patientId}`;
  }

  isThreadSelected(thread: ChatThreadDto): boolean {
    return this.selectedDoctorId === thread.doctorId && this.selectedPatientId === thread.patientId;
  }

  isUrgent(thread: ChatThreadDto): boolean {
    // Consider urgent if more than 5 unread messages or last message is older than 24 hours
    const unreadCount = thread.unreadCount || 0;

    if (!thread.lastMessageDate) return false;

    let lastMessageDate: Date;

    // Handle both Date objects and DateTime objects from Luxon
    if (thread.lastMessageDate instanceof Date) {
      lastMessageDate = thread.lastMessageDate;
    } else if (thread.lastMessageDate && typeof thread.lastMessageDate === 'object' && (thread.lastMessageDate as any).toJSDate) {
      // Luxon DateTime object
      lastMessageDate = (thread.lastMessageDate as any).toJSDate();
    } else if (typeof thread.lastMessageDate === 'string') {
      lastMessageDate = new Date(thread.lastMessageDate);
    } else {
      return false;
    }

    const now = new Date();
    const hoursDiff = (now.getTime() - lastMessageDate.getTime()) / (1000 * 60 * 60);

    return unreadCount > 5 || (unreadCount > 0 && hoursDiff > 24);
  }

  startNewChat(): void {
    this.openNewChatModal();
  }

  // New chat functionality
  openNewChatModal(): void {
    console.log('🚀 Opening new chat modal...');
    console.log('👥 Available patients to show:', this.availablePatients);
    console.log('📊 Patient count:', this.availablePatients.length);
    this.showNewChatModal = true;
    this.searchTerm = ''; // Reset search term when opening modal
    this.filteredPatients = [...this.availablePatients]; // Reset filtered list
  }

  closeNewChatModal(): void {
    this.showNewChatModal = false;
    this.searchTerm = ''; // Reset search term when closing modal
  }

  // Search functionality
  onSearchTermChange(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.filterPatients();
  }

  private filterPatients(): void {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      // If no search term, show all available patients
      this.filteredPatients = [...this.availablePatients];
    } else {
      const searchTermLower = this.searchTerm.toLowerCase().trim();

      // Filter patients by name and email
      this.filteredPatients = this.availablePatients.filter(patient => {
        const nameMatch = patient.fullName?.toLowerCase().includes(searchTermLower) || false;
        const emailMatch = patient.email?.toLowerCase().includes(searchTermLower) || false;

        return nameMatch || emailMatch;
      });
    }

    console.log('🔍 Search term:', this.searchTerm);
    console.log('📋 Filtered patients:', this.filteredPatients.length, 'out of', this.availablePatients.length);
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.filteredPatients = [...this.availablePatients];
  }

  startChatWithPatient(patient: AvailablePatientDto): void {
    // For now, just emit the selection to start a new chat
    // The parent component will handle creating the chat thread
    this.chatSelected.emit({
      doctorId: '', // Will be determined by the backend based on current user
      patientId: patient.id
    });
    this.closeNewChatModal();
  }

  getPatientInitials(patient: AvailablePatientDto): string {
    if (!patient.fullName) return 'PT';
    const names = patient.fullName.split(' ');
    if (names.length >= 2) {
      return (names[0][0] + names[names.length - 1][0]).toUpperCase();
    }
    return patient.fullName.substring(0, 2).toUpperCase();
  }

  calculateAge(dateOfBirth: any): number {
    if (!dateOfBirth) return 0;

    let birthDate: Date;
    if (dateOfBirth instanceof Date) {
      birthDate = dateOfBirth;
    } else if (typeof dateOfBirth === 'string') {
      birthDate = new Date(dateOfBirth);
    } else if (dateOfBirth && typeof dateOfBirth === 'object' && dateOfBirth.toJSDate) {
      birthDate = dateOfBirth.toJSDate();
    } else {
      return 0;
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  }

  refreshChats(): void {
    this.loadData();
  }

  trackByThreadId(_index: number, thread: ChatThreadDto): string {
    return `${thread.doctorId}-${thread.patientId}`;
  }

  // Check if we have recent cached data - optimized to avoid unnecessary refreshes
  hasRecentChatData(): boolean {
    const cachedThreads = this.chatCacheService.getCachedChatThreads();
    // Only require cached threads for recent data check
    // Available patients can be loaded separately when needed
    const hasRecentThreads = !!cachedThreads;
    console.log('📦 Cache check - Recent threads:', hasRecentThreads);
    return hasRecentThreads;
  }

  // 🚀 SignalR Real-time Methods
  private async initializeSignalR(): Promise<void> {
    try {
      // Start SignalR connection
      await this.chatService.connect();

      // Subscribe to real-time messages to update chat list
      this.chatService.message$.pipe(takeUntil(this.destroy$)).subscribe((message: ChatMessageResponseDto) => {
        console.log('Received real-time message in chat list:', message);
        this.updateChatThreadWithNewMessage(message);
      });

      // Subscribe to message read notifications to update unread counts
      this.chatService.messageRead$.pipe(takeUntil(this.destroy$)).subscribe((readNotification: any) => {
        console.log('📖 Messages marked as read:', readNotification);
        this.handleMessagesMarkedAsRead(readNotification);
      });

      // Subscribe to chat thread creation notifications to refresh the list
      this.chatService.chatThreadCreated$.pipe(takeUntil(this.destroy$)).subscribe((threadCreated: any) => {
        console.log('🆕 New chat thread created, refreshing chat list:', threadCreated);
        this.refreshChats();
      });

    } catch (error) {
      console.error('Error initializing SignalR in chat list:', error);
    }
  }

  updateChatThreadWithNewMessage(message: ChatMessageResponseDto): void {
    const threadIndex = this.chatThreads.findIndex(
      thread => thread.doctorId === message.doctorId && thread.patientId === message.patientId
    );

    if (threadIndex >= 0) {
      // Get the thread to update
      const thread = this.chatThreads[threadIndex];

      // Update existing thread properties
      thread.lastMessage = message.message;
      thread.lastMessageDate = message.createdDate;

      // Sync unread count with global service (don't modify it here, just read)
      this.syncThreadUnreadCount(thread);

      // Only move to top if it's not already at the top to reduce flickering
      if (threadIndex > 0) {
        // Move to top of list for most recent activity
        this.chatThreads.splice(threadIndex, 1);
        this.chatThreads.unshift(thread);
      }

      // Force change detection to update UI
      this.cdr.detectChanges();
    } else {
      // This might be a new chat thread, reload the data
      this.loadChatThreads();
    }
  }

  private handleMessagesMarkedAsRead(readNotification: any): void {
    console.log('🔄 Handling messages marked as read:', readNotification);

    // Find the thread and sync with global service
    const threadIndex = this.chatThreads.findIndex(
      thread => thread.doctorId === readNotification.doctorId && thread.patientId === readNotification.patientId
    );

    if (threadIndex >= 0) {
      console.log('📊 Syncing unread count for thread:', this.chatThreads[threadIndex]);
      // Sync with global service
      this.syncThreadUnreadCount(this.chatThreads[threadIndex]);

      // Force change detection to update UI
      this.cdr.detectChanges();
    }
  }

  /**
   * Subscribe to global message service for real-time unread count updates
   */
  private subscribeToGlobalMessageService(): void {
    // Subscribe to unread counts changes from global service
    this.globalMessageService.unreadCounts$
      .pipe(takeUntil(this.destroy$))
      .subscribe(unreadCounts => {
        console.log('📊 Global unread counts updated:', unreadCounts);
        this.syncAllThreadsWithGlobalCounts(unreadCounts);
      });
  }

  /**
   * Sync all chat threads with global unread counts
   */
  private syncAllThreadsWithGlobalCounts(unreadCounts: { [threadId: string]: number }): void {
    let hasChanges = false;

    this.chatThreads.forEach(thread => {
      const threadId = `${thread.doctorId}-${thread.patientId}`;
      const globalCount = unreadCounts[threadId] || 0;

      if (thread.unreadCount !== globalCount) {
        thread.unreadCount = globalCount;
        hasChanges = true;
      }
    });

    if (hasChanges) {
      this.cdr.detectChanges();
    }
  }

  /**
   * Sync a single thread's unread count with global service
   */
  private syncThreadUnreadCount(thread: ChatThreadDto): void {
    const globalCount = this.globalMessageService.getUnreadCount(thread.doctorId, thread.patientId);
    if (thread.unreadCount !== globalCount) {
      thread.unreadCount = globalCount;
    }
  }

  private isMessageFromCurrentUser(message: ChatMessageResponseDto): boolean {
    try {
      // Get current user info from JWT token
      const token = this.getTokenFromCookie();
      if (!token) return false;

      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentUserId = payload.sub || payload.unique_name || payload.nameid;
      const currentUserRole = payload.role || payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'];

      // Check if message is from current user based on sender ID and role
      if (message.senderId && currentUserId) {
        return message.senderId === currentUserId;
      }

      // Fallback: check based on role and IDs
      if (currentUserRole === 'Doctor' && message.senderType === 'Doctor') {
        return message.doctorId === currentUserId;
      } else if (currentUserRole === 'Patient' && message.senderType === 'Patient') {
        return message.patientId === currentUserId;
      }

      return false;
    } catch (error) {
      console.error('Error checking if message is from current user:', error);
      return false;
    }
  }

  private getTokenFromCookie(): string | null {
    try {
      const cookies = document.cookie.split(';');
      for (let cookie of cookies) {
        const [name, ...valueParts] = cookie.trim().split('=');
        if (name === 'access_token') {
          const value = valueParts.join('=');
          if (value && value.length > 0) {
            return value;
          }
        }
      }
      return null;
    } catch (error) {
      console.error('Error reading token from cookies:', error);
      return null;
    }
  }

  // Online Status Methods
  isPatientOnline(patientEmail: string): boolean {
    return this.onlineStatusService.isUserOnline(patientEmail);
  }

  // Helper method to load and cache patient email for existing threads
  private patientEmailCache = new Map<string, string>();

  // Optimized: Use cached data instead of API calls
  private loadPatientEmailForThread(patientId: string): void {
    // Check if we already have it cached
    if (this.patientEmailCache.has(patientId)) {
      return;
    }

    // Use cached available patients (unfiltered) instead of making API call
    const cachedPatients = this.chatCacheService.getCachedAvailablePatients();
    if (cachedPatients) {
      const patient = cachedPatients.find(p => p.id === patientId);
      if (patient && patient.email) {
        this.patientEmailCache.set(patientId, patient.email);
        this.cdr.detectChanges();
        return;
      }
    }

    // If not found in cache, patient might not exist or be inactive
    console.log('⚠️ Patient not found in available patients cache:', patientId);
  }

  isPatientOnlineById(patientId: string): boolean {
    // First try to find patient email by ID from available patients
    const availablePatient = this.availablePatients.find(p => p.id === patientId);
    if (availablePatient && availablePatient.email) {
      return this.onlineStatusService.isUserOnline(availablePatient.email);
    }

    // Check cached patient emails
    const cachedEmail = this.patientEmailCache.get(patientId);
    if (cachedEmail) {
      return this.onlineStatusService.isUserOnline(cachedEmail);
    }

    // If not found in available patients, fallback: try to load patient info from API (cache for future use)
    const thread = this.chatThreads.find(t => t.patientId === patientId);
    if (thread) {
      this.loadPatientEmailForThread(patientId);
    }

    return false;
  }

  // Optimized: Use cached patient data instead of additional API calls
  private preloadPatientEmailsForThreads(threads: ChatThreadDto[]): void {
    if (!threads || threads.length === 0) return;

    console.log('🔍 Using cached patient data for online status (no additional API calls)...');

    // Use cached available patients (unfiltered) instead of making another API call
    const cachedPatients = this.chatCacheService.getCachedAvailablePatients();
    if (cachedPatients) {
      threads.forEach(thread => {
        if (thread.patientId && !this.patientEmailCache.has(thread.patientId)) {
          // Find patient in cached available patients
          const patient = cachedPatients.find(p => p.id === thread.patientId);
          if (patient && patient.email) {
            this.patientEmailCache.set(thread.patientId, patient.email);
          }
        }
      });
    }

    console.log('✅ Patient emails cached from available patients:', this.patientEmailCache.size);

    // Trigger change detection to update UI with correct online status
    this.cdr.detectChanges();
  }
}
