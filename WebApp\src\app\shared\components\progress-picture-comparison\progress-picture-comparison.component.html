<!-- Progress Picture Comparison Component -->
<div class="progress-comparison-container bg-white rounded-lg shadow-lg overflow-hidden">
  <!-- Header -->
  <div class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold">Progress Picture Comparison</h3>
        <p class="text-blue-100 text-sm" *ngIf="patientEmail">Patient: {{ patientEmail }}</p>
      </div>
      <div class="flex items-center space-x-2">
        <!-- Comparison Mode Selector -->
        <mat-select [(value)]="comparisonView.mode" (selectionChange)="setComparisonMode($event.value)"
                    class="bg-white text-gray-900 rounded">
          <mat-option value="side-by-side">Side by Side</mat-option>
          <mat-option value="overlay">Overlay</mat-option>
          <mat-option value="slider">Slider</mat-option>
        </mat-select>
        
        <!-- Action Buttons -->
        <button mat-icon-button (click)="toggleTimeline()" matTooltip="Toggle Timeline">
          <mat-icon>timeline</mat-icon>
        </button>
        <button mat-icon-button (click)="toggleMeasurementTools()" matTooltip="Measurement Tools">
          <mat-icon>straighten</mat-icon>
        </button>
        <button mat-icon-button (click)="exportComparison()" matTooltip="Export Comparison">
          <mat-icon>download</mat-icon>
        </button>
        <button mat-icon-button (click)="enterFullscreen()" matTooltip="Fullscreen">
          <mat-icon>fullscreen</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Timeline View -->
  <div *ngIf="showTimeline" class="border-b border-gray-200 p-4 bg-gray-50">
    <div class="flex items-center justify-between mb-4">
      <h4 class="font-medium text-gray-900">Picture Timeline</h4>
      <div class="flex space-x-2">
        <button *ngFor="let range of ['week', 'month', 'quarter', 'year', 'all']"
                (click)="setTimeRange(range)"
                [class]="selectedTimeRange === range ? 
                  'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'"
                class="px-3 py-1 rounded text-sm font-medium transition-colors">
          {{ range | titlecase }}
        </button>
      </div>
    </div>
    
    <!-- Timeline Pictures -->
    <div class="flex space-x-2 overflow-x-auto pb-2">
      <div *ngFor="let picture of getFilteredPicturesByTimeRange(); trackBy: trackByPictureId"
           class="flex-shrink-0 cursor-pointer group">
        <div class="relative">
          <img [src]="picture.url" 
               [alt]="'Progress picture from ' + formatDate(picture.date)"
               class="w-20 h-20 object-cover rounded-lg border-2 transition-all duration-200"
               [class]="(comparisonView.beforePicture?.id === picture.id || comparisonView.afterPicture?.id === picture.id) ? 
                 'border-blue-500 shadow-lg' : 'border-gray-300 group-hover:border-gray-400'">
          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200"></div>
        </div>
        <p class="text-xs text-gray-600 mt-1 text-center">{{ formatDate(picture.date) }}</p>
      </div>
    </div>
  </div>

  <!-- Picture Selection (if allowSelection is true) -->
  <div *ngIf="allowSelection && pictures.length > 0" class="border-b border-gray-200 p-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Before Picture Selection -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Before Picture</label>
        <div class="grid grid-cols-4 gap-2">
          <div *ngFor="let picture of pictures; trackBy: trackByPictureId"
               (click)="selectBeforePicture(picture)"
               class="cursor-pointer group">
            <img [src]="picture.url" 
                 [alt]="'Picture from ' + formatDate(picture.date)"
                 class="w-full h-16 object-cover rounded border-2 transition-all"
                 [class]="comparisonView.beforePicture?.id === picture.id ? 
                   'border-green-500 shadow-md' : 'border-gray-300 group-hover:border-gray-400'">
            <p class="text-xs text-gray-600 mt-1 text-center truncate">{{ formatDate(picture.date) }}</p>
          </div>
        </div>
      </div>

      <!-- After Picture Selection -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">After Picture</label>
        <div class="grid grid-cols-4 gap-2">
          <div *ngFor="let picture of pictures; trackBy: trackByPictureId"
               (click)="selectAfterPicture(picture)"
               class="cursor-pointer group">
            <img [src]="picture.url" 
                 [alt]="'Picture from ' + formatDate(picture.date)"
                 class="w-full h-16 object-cover rounded border-2 transition-all"
                 [class]="comparisonView.afterPicture?.id === picture.id ? 
                   'border-blue-500 shadow-md' : 'border-gray-300 group-hover:border-gray-400'">
            <p class="text-xs text-gray-600 mt-1 text-center truncate">{{ formatDate(picture.date) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Comparison Controls -->
  <div class="border-b border-gray-200 p-4 bg-gray-50">
    <div class="flex items-center justify-between">
      <!-- Zoom Controls -->
      <div class="flex items-center space-x-2">
        <span class="text-sm font-medium text-gray-700">Zoom:</span>
        <button mat-icon-button (click)="zoomOut()" [disabled]="zoomLevel <= 25">
          <mat-icon>zoom_out</mat-icon>
        </button>
        <span class="text-sm text-gray-600 min-w-[60px] text-center">{{ zoomLevel }}%</span>
        <button mat-icon-button (click)="zoomIn()" [disabled]="zoomLevel >= 400">
          <mat-icon>zoom_in</mat-icon>
        </button>
        <button mat-button (click)="resetZoomAndPan()" class="text-sm">Reset</button>
      </div>

      <!-- Overlay Opacity (for overlay mode) -->
      <div *ngIf="comparisonView.mode === 'overlay'" class="flex items-center space-x-2">
        <span class="text-sm font-medium text-gray-700">Opacity:</span>
        <mat-slider [(value)]="overlayOpacity" min="0" max="100" step="1" class="w-32"></mat-slider>
        <span class="text-sm text-gray-600 min-w-[40px]">{{ overlayOpacity }}%</span>
      </div>

      <!-- Progress Analysis -->
      <div class="text-right">
        <div class="text-sm text-gray-600">
          <span *ngIf="comparisonView.beforePicture && comparisonView.afterPicture">
            {{ getProgressAnalysis().timespan }} days progress
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Comparison Area -->
  <div class="comparison-container relative bg-gray-100 min-h-[400px] overflow-hidden"
       (mousedown)="onMouseDown($event)"
       (mousemove)="onMouseMove($event)"
       (mouseup)="onMouseUp()"
       (mouseleave)="onMouseUp()">

    <!-- No Pictures Selected State -->
    <div *ngIf="!comparisonView.beforePicture || !comparisonView.afterPicture" 
         class="flex items-center justify-center h-96">
      <div class="text-center text-gray-500">
        <mat-icon class="text-6xl mb-4">compare</mat-icon>
        <p class="text-lg font-medium">Select pictures to compare</p>
        <p class="text-sm">Choose before and after pictures to see progress</p>
      </div>
    </div>

    <!-- Side by Side Comparison -->
    <div *ngIf="comparisonView.mode === 'side-by-side' && comparisonView.beforePicture && comparisonView.afterPicture"
         class="grid grid-cols-2 h-full">
      <!-- Before Picture -->
      <div class="relative border-r border-gray-300">
        <div class="absolute top-2 left-2 bg-green-600 text-white px-2 py-1 rounded text-sm font-medium z-10">
          Before
        </div>
        <img [src]="comparisonView.beforePicture.url"
             [alt]="'Before: ' + formatDate(comparisonView.beforePicture.date)"
             class="w-full h-full object-contain transition-transform"
             [style.transform]="'scale(' + (zoomLevel/100) + ') translate(' + panOffset.x + 'px, ' + panOffset.y + 'px)'">
        <div class="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
          {{ formatDate(comparisonView.beforePicture.date) }}
        </div>
      </div>

      <!-- After Picture -->
      <div class="relative">
        <div class="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-sm font-medium z-10">
          After
        </div>
        <img [src]="comparisonView.afterPicture.url"
             [alt]="'After: ' + formatDate(comparisonView.afterPicture.date)"
             class="w-full h-full object-contain transition-transform"
             [style.transform]="'scale(' + (zoomLevel/100) + ') translate(' + panOffset.x + 'px, ' + panOffset.y + 'px)'">
        <div class="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
          {{ formatDate(comparisonView.afterPicture.date) }}
        </div>
      </div>
    </div>

    <!-- Overlay Comparison -->
    <div *ngIf="comparisonView.mode === 'overlay' && comparisonView.beforePicture && comparisonView.afterPicture"
         class="relative h-full">
      <!-- Base Image (Before) -->
      <img [src]="comparisonView.beforePicture.url"
           [alt]="'Before: ' + formatDate(comparisonView.beforePicture.date)"
           class="absolute inset-0 w-full h-full object-contain transition-transform"
           [style.transform]="'scale(' + (zoomLevel/100) + ') translate(' + panOffset.x + 'px, ' + panOffset.y + 'px)'">
      
      <!-- Overlay Image (After) -->
      <img [src]="comparisonView.afterPicture.url"
           [alt]="'After: ' + formatDate(comparisonView.afterPicture.date)"
           class="absolute inset-0 w-full h-full object-contain transition-all"
           [style.opacity]="overlayOpacity / 100"
           [style.transform]="'scale(' + (zoomLevel/100) + ') translate(' + panOffset.x + 'px, ' + panOffset.y + 'px)'">

      <!-- Labels -->
      <div class="absolute top-2 left-2 space-y-1">
        <div class="bg-green-600 text-white px-2 py-1 rounded text-sm font-medium">
          Before (Base)
        </div>
        <div class="bg-blue-600 text-white px-2 py-1 rounded text-sm font-medium"
             [style.opacity]="overlayOpacity / 100">
          After ({{ overlayOpacity }}% opacity)
        </div>
      </div>
    </div>

    <!-- Slider Comparison -->
    <div *ngIf="comparisonView.mode === 'slider' && comparisonView.beforePicture && comparisonView.afterPicture"
         class="relative h-full overflow-hidden">
      <!-- Before Image (Left side) -->
      <img [src]="comparisonView.beforePicture.url"
           [alt]="'Before: ' + formatDate(comparisonView.beforePicture.date)"
           class="absolute inset-0 w-full h-full object-contain transition-transform"
           [style.transform]="'scale(' + (zoomLevel/100) + ') translate(' + panOffset.x + 'px, ' + panOffset.y + 'px)'"
           [style.clip-path]="'inset(0 ' + (100 - sliderPosition) + '% 0 0)'">

      <!-- After Image (Right side) -->
      <img [src]="comparisonView.afterPicture.url"
           [alt]="'After: ' + formatDate(comparisonView.afterPicture.date)"
           class="absolute inset-0 w-full h-full object-contain transition-transform"
           [style.transform]="'scale(' + (zoomLevel/100) + ') translate(' + panOffset.x + 'px, ' + panOffset.y + 'px)'"
           [style.clip-path]="'inset(0 0 0 ' + sliderPosition + '%)'">

      <!-- Slider Line -->
      <div class="absolute top-0 bottom-0 w-1 bg-white shadow-lg z-10 cursor-ew-resize"
           [style.left.%]="sliderPosition">
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center">
          <mat-icon class="text-gray-600 text-sm">drag_indicator</mat-icon>
        </div>
      </div>

      <!-- Labels -->
      <div class="absolute top-2 left-2 bg-green-600 text-white px-2 py-1 rounded text-sm font-medium">
        Before
      </div>
      <div class="absolute top-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-sm font-medium">
        After
      </div>

      <!-- Slider Control -->
      <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
        <mat-slider [(value)]="sliderPosition" min="0" max="100" step="1" class="w-64"></mat-slider>
      </div>
    </div>
  </div>

  <!-- Analysis Footer -->
  <div class="border-t border-gray-200 p-4 bg-gray-50">
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-600">
        <span *ngIf="comparisonView.beforePicture && comparisonView.afterPicture">
          Progress Analysis: {{ getProgressAnalysis().notes }}
        </span>
      </div>
      <div class="flex space-x-2">
        <button mat-button color="primary" (click)="exportComparison()">
          <mat-icon>download</mat-icon>
          Export Report
        </button>
      </div>
    </div>
  </div>
</div>
