import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AppointmentMessage, AppointmentMessageServiceProxy, UserAccountServiceProxy, AssignDoctorRequest, UpdateToReviewingRequest, ConfirmAppointmentRequest, CompleteAppointmentRequest } from '../../../../shared/service-proxies/service-proxies';
import { AuthService } from '../../../../shared/services/auth.service';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { DateTime } from 'luxon';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-appointment-detail',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './appointment-detail.component.html',
  styleUrl: './appointment-detail.component.css',
})
export class AppointmentDetailComponent implements OnInit, OnDestroy {
  baseUrl = getRemoteServiceBaseUrl();
  appointment: AppointmentMessage | null = null;
  isLoading = true;
  appointmentId = '';
  isAdmin = false;
  isDoctor = false;
  currentUser: any;
  isUpdatingStatus = false;
  isImageLoading = false;
  imageLoadError = false;
  doctors: any[] = [];
  isAssigningDoctor = false;
  private doctorsLoading = false;
  private doctorsLoaded = false;
  private destroy$ = new Subject<void>();
  statusOptions = ['Pending', 'Confirmed', 'In Progress', 'Completed', 'Cancelled', 'Rescheduled'];

  // Cancel appointment dialog properties
  showCancelDialog = false;
  cancellationReason = '';
  isCancelling = false;
  showValidationError = false;

  // Display properties for cancelled appointments
  cancellationDisplayNote = '';
  cancelledDisplayDate = '';

  constructor(
    private appointmentService: AppointmentMessageServiceProxy,
    private userAccountService: UserAccountServiceProxy,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getUser();
    this.isAdmin = this.authService.hasRole('Admin');
    this.isDoctor = this.authService.hasRole('Doctor');

    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.appointmentId = params['id'];
      if (this.appointmentId) this.loadAppointmentDetails();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private showSnackbar = (message: string, isError = false): void => {
    this.snackBar.open(message, 'Close', { duration: 3000, panelClass: isError ? 'error-snackbar' : 'success-snackbar' });
  }

  private getUserEmail = (): string => this.currentUser?.['email'] || this.currentUser?.['unique_name'] || '';

  loadAppointmentDetails = (): void => {
    this.isLoading = true;
    this.appointmentService.getAppointmentById(this.appointmentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.appointment = data;
          this.isImageLoading = !!data?.imageUrl;
          this.imageLoadError = false;

          // Parse cancellation info if appointment is cancelled
          if (data?.status === 'Cancelled' && data?.message?.startsWith('CANCELLED:')) {
            const messageLines = data.message.split('\n');
            if (messageLines.length > 0) {
              this.cancellationDisplayNote = messageLines[0].replace('CANCELLED: ', '');
              this.cancelledDisplayDate = this.formatDateTime(DateTime.now()); // You might want to store this in a better way
            }
          }

          if (!this.isAdmin && this.currentUser) {
            const userEmail = this.getUserEmail();
            const hasPermission = data?.patientEmail === userEmail ||
              (this.isDoctor && data?.assignedDoctorEmail === userEmail);

            if (!hasPermission) {
              this.showSnackbar('You do not have permission to view this appointment', true);
              this.router.navigate(['/appointments']);
              return;
            }
          }

          if (this.isDoctor && data && data.assignedDoctorEmail === this.getUserEmail() &&
              data.status === 'Doctor Assigned') {
            this.updateAppointmentToReviewing();
          }
          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
          this.showSnackbar('Error loading appointment details', true);
          this.router.navigate(['/appointments']);
        }
      });
  }

  editAppointment = (): void => { this.router.navigate(['/appointments/edit', this.appointmentId]); };
  goBack = (): void => { this.router.navigate(['/appointments']); };

  getStatusColor = (status: string | undefined): string => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-200 dark:border-yellow-800',
      'doctor assigned': 'bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900 dark:text-indigo-200 dark:border-indigo-800',
      reviewing: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900 dark:text-purple-200 dark:border-purple-800',
      confirmed: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-800',
      completed: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-800',
      cancelled: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-200 dark:border-red-800',
      rescheduled: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900 dark:text-orange-200 dark:border-orange-800'
    };
    return colors[status?.toLowerCase() as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900 dark:text-gray-200 dark:border-gray-800';
  }

  formatDateTime = (dateTime: DateTime | undefined): string =>
    dateTime ? dateTime.toFormat('EEEE, MMMM dd, yyyy • h:mm a') : 'Not scheduled';

  formatDateTimeShort = (dateTime: DateTime | undefined): string =>
    dateTime ? dateTime.toFormat('MMM dd, yyyy • h:mm a') : 'Not set';

  getImageUrl = (imageUrl: string | undefined): string =>
    !imageUrl ? '' : imageUrl.startsWith('http') ? imageUrl : `${this.baseUrl}/api/File/Getfile/${imageUrl}`;

  updateStatus = (newStatus: string): void => {
    if (!this.appointment) return;

    this.isUpdatingStatus = true;
    const updatedAppointment = new AppointmentMessage();
    Object.assign(updatedAppointment, this.appointment, { status: newStatus });

    this.appointmentService.addOrUpdateAppointment(updatedAppointment)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isUpdatingStatus = false;
          this.appointment = response;
          this.showSnackbar('Status updated successfully');
        },
        error: () => {
          this.isUpdatingStatus = false;
          this.showSnackbar('Error updating status', true);
        }
      });
  }

  canEditAppointment = (): boolean =>
    !!this.appointment && this.appointment.patientEmail === this.getUserEmail();

  getStatusIcon = (status: string | undefined): string => {
    const icons = {
      pending: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z',
      confirmed: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
      'in progress': 'M13 10V3L4 14h7v7l9-11h-7z',
      completed: 'M5 13l4 4L19 7',
      cancelled: 'M6 18L18 6M6 6l12 12',
      rescheduled: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
    };
    return icons[status?.toLowerCase() as keyof typeof icons] || 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
  }

  getRelativeTime = (dateTime: DateTime | undefined): string => {
    if (!dateTime) return '';
    const diff = dateTime.diff(DateTime.now());
    return diff.as('milliseconds') < 0 ? dateTime.toRelative() || 'Past' : dateTime.toRelative() || 'Future';
  }

  onImageLoad = (): void => { this.isImageLoading = false; this.imageLoadError = false; };
  onImageError = (event?: any): void => {
    this.isImageLoading = false;
    this.imageLoadError = true;
    if (event?.target) event.target.onerror = null;
  };

  canAssignDoctor = (): boolean =>
    this.isAdmin && this.appointment?.status === 'Pending' && !this.appointment?.assignedDoctorEmail;

  canUpdateToReviewing = (): boolean =>
    this.isDoctor && this.appointment?.status === 'Doctor Assigned' &&
    this.appointment?.assignedDoctorEmail === this.getUserEmail();

  canConfirmAppointment = (): boolean =>
    this.isDoctor && this.appointment?.status === 'Reviewing' &&
    this.appointment?.assignedDoctorEmail === this.getUserEmail();

  canCompleteAppointment = (): boolean =>
    this.isDoctor && this.appointment?.status === 'Confirmed' &&
    this.appointment?.assignedDoctorEmail === this.getUserEmail();



  loadDoctors = (): void => {
    if (this.doctorsLoading || this.doctorsLoaded) return;

    this.doctorsLoading = true;
    this.userAccountService.getAllDoctors().pipe(takeUntil(this.destroy$)).subscribe({
      next: (doctors) => {
        this.doctors = (doctors || []).map(doctor => ({
          email: doctor.email,
          name: doctor.name || doctor.email
        }));
        this.doctorsLoaded = true;
        this.doctorsLoading = false;

        if (this.doctors.length === 0) {
          this.showSnackbar('No doctors found in the system. Please add doctors first.', true);
        }
      },
      error: () => {
        this.doctorsLoading = false;
        this.showSnackbar('Error loading doctors', true);
      }
    });
  }

  openDoctorSelectionDialog(): void {
    // Load doctors data only when needed
    if (!this.doctorsLoaded && !this.doctorsLoading) {
      this.loadDoctors();
    }

    // Check if doctors are already loaded, if not wait for them to load
    const checkDoctorsAndOpenDialog = () => {
      if (
        this.doctorsLoaded ||
        (!this.doctorsLoading && this.doctors.length > 0)
      ) {
        import(
          '../../user/user-request/doctor-selection-dialog/doctor-selection-dialog.component'
        )
          .then(({ DoctorSelectionDialogComponent }) => {
            const dialogRef = this.dialog.open(DoctorSelectionDialogComponent, {
              width: '500px',
              data: { doctors: this.doctors },
            });

            dialogRef.afterClosed().subscribe((result) => {
              if (result && this.appointmentId) {
                this.assignDoctorToAppointment(result.email);
              }
            });
          })
          .catch((error) => {
            console.error('Error loading doctor selection dialog:', error);
            this.snackBar.open(
              'Error opening doctor selection dialog.',
              'Close',
              { duration: 3000 }
            );
          });
      } else if (this.doctorsLoading) {
        // If still loading, wait a bit and try again
        setTimeout(checkDoctorsAndOpenDialog, 500);
      } else {
        // No doctors available and not loading
        this.snackBar.open(
          'No doctors available. Please try again later.',
          'Close',
          { duration: 3000 }
        );
      }
    };

    checkDoctorsAndOpenDialog();
  }

  assignDoctorToAppointment(doctorEmail: string): void {
    if (!doctorEmail || !this.appointmentId) {
      this.snackBar.open(
        'Could not assign doctor. Missing doctor email or appointment ID.',
        'Close',
        { duration: 3000 }
      );
      return;
    }

    this.isAssigningDoctor = true;

    const request = new AssignDoctorRequest({
      appointmentId: this.appointmentId,
      doctorEmail: doctorEmail,
    });

    this.appointmentService.assignDoctor(request).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.isAssigningDoctor = false;
        this.showSnackbar('Doctor assigned successfully to appointment!');
        this.loadAppointmentDetails();
      },
      error: () => {
        this.isAssigningDoctor = false;
        this.showSnackbar('Error assigning doctor. Please try again.', true);
      }
    });
  }

  updateAppointmentToReviewing(): void {
    if (!this.appointmentId) {
      return;
    }

    const request = new UpdateToReviewingRequest({
      appointmentId: this.appointmentId,
    });

    this.appointmentService.updateToReviewing(request).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        if (this.appointment) this.appointment.status = 'Reviewing';
        this.showSnackbar('Appointment status updated to Reviewing');
      },
      error: () => {} // Silent error for automatic status update
    });
  }

  confirmAppointment(): void {
    if (!this.appointmentId) {
      return;
    }

    // Open custom dialog for confirmation note
    import('../notes-input-dialog/notes-input-dialog.component')
      .then(({ NotesInputDialogComponent }) => {
        const dialogRef = this.dialog.open(NotesInputDialogComponent, {
          width: '500px',
          data: {
            title: 'Confirm Appointment',
            placeholder:
              'Enter confirmation notes (e.g., appointment details confirmed with patient, time slot verified, etc.)',
            confirmButtonText: 'Confirm Appointment',
          },
        });

        dialogRef.afterClosed().subscribe((confirmationNote) => {
          if (confirmationNote) {
            this.processConfirmation(confirmationNote);
          }
        });
      })
      .catch((error) => {
        console.error('Error loading notes dialog:', error);
        this.snackBar.open('Error opening notes dialog.', 'Close', {
          duration: 3000,
        });
      });
  }

  private processConfirmation(confirmationNote: string): void {
    this.isUpdatingStatus = true;

    const request = new ConfirmAppointmentRequest({
      appointmentId: this.appointmentId,
      confirmationNote: confirmationNote,
    });

    this.appointmentService.confirmAppointment(request).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.isUpdatingStatus = false;
        this.showSnackbar('Appointment confirmed successfully!');
        if (this.appointment) {
          this.appointment.status = 'Confirmed';
          this.appointment.confirmationNote = confirmationNote;
        }
      },
      error: () => {
        this.isUpdatingStatus = false;
        this.showSnackbar('Error confirming appointment. Please try again.', true);
      }
    });
  }

  completeAppointment(): void {
    if (!this.appointmentId) {
      return;
    }

    // Open custom dialog for completion note
    import('../notes-input-dialog/notes-input-dialog.component')
      .then(({ NotesInputDialogComponent }) => {
        const dialogRef = this.dialog.open(NotesInputDialogComponent, {
          width: '500px',
          data: {
            title: 'Complete Appointment',
            placeholder:
              'Enter completion notes (e.g., treatment provided, patient response, follow-up instructions, etc.)',
            confirmButtonText: 'Complete Appointment',
          },
        });

        dialogRef.afterClosed().subscribe((completionNote) => {
          if (completionNote) {
            this.processCompletion(completionNote);
          }
        });
      })
      .catch((error) => {
        console.error('Error loading notes dialog:', error);
        this.snackBar.open('Error opening notes dialog.', 'Close', {
          duration: 3000,
        });
      });
  }

  private processCompletion(completionNote: string): void {
    this.isUpdatingStatus = true;

    const request = new CompleteAppointmentRequest({
      appointmentId: this.appointmentId,
      completionNote: completionNote,
    });

    this.appointmentService.completeAppointment(request).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.isUpdatingStatus = false;
        this.showSnackbar('Appointment completed successfully!');
        if (this.appointment) {
          this.appointment.status = 'Completed';
          this.appointment.completionNote = completionNote;
        }
      },
      error: () => {
        this.isUpdatingStatus = false;
        this.showSnackbar('Error completing appointment. Please try again.', true);
      }
    });
  }

  // Cancel appointment methods
  openCancelDialog(): void {
    this.showCancelDialog = true;
    this.cancellationReason = '';
    this.showValidationError = false;
  }

  closeCancelDialog(): void {
    this.showCancelDialog = false;
    this.cancellationReason = '';
    this.showValidationError = false;
  }

  cancelAppointment(): void {
    // Validate cancellation reason
    if (!this.cancellationReason?.trim()) {
      this.showValidationError = true;
      return;
    }

    if (!this.appointmentId) {
      this.showSnackbar('Error: Appointment ID not found', true);
      return;
    }

    this.isCancelling = true;
    this.showValidationError = false;

    // Create cancel request - we'll update the status and store cancellation info in message
    const updatedAppointment = new AppointmentMessage();
    Object.assign(updatedAppointment, this.appointment, {
      status: 'Cancelled',
      message: `CANCELLED: ${this.cancellationReason.trim()}\n\nOriginal message: ${this.appointment?.message || 'No original message'}`
    });

    this.appointmentService.addOrUpdateAppointment(updatedAppointment)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.isCancelling = false;
          this.appointment = response;

          // Set display properties for the cancelled appointment
          this.cancellationDisplayNote = this.cancellationReason.trim();
          this.cancelledDisplayDate = this.formatDateTime(DateTime.now());

          this.showSnackbar('Appointment cancelled successfully. Patient has been notified.');
          this.closeCancelDialog();
        },
        error: () => {
          this.isCancelling = false;
          this.showSnackbar('Error cancelling appointment. Please try again.', true);
        }
      });
  }
}
