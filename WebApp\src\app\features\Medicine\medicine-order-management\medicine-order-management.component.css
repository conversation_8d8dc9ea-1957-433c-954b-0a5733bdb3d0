/* Medicine Order Management Component Styles */

.order-management-container {
  min-height: 100vh;
  background-color: #f9fafb;
}

.dark .order-management-container {
  background-color: #111827;
}

/* Statistics Cards */
.stats-card {
  background: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dark .stats-card {
  background: #1f2937;
}

.stats-icon {
  width: 1.5rem;
  height: 1.5rem;
  flex-shrink: 0;
}

.stats-value {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
}

.dark .stats-value {
  color: #ffffff;
}

.stats-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.dark .stats-label {
  color: #9ca3af;
}

/* Filter Section */
.filter-section {
  background: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.dark .filter-section {
  background: #1f2937;
}

/* Form Controls */
.form-input {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 0.875rem;
  transition: all 0.15s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .form-input {
  background-color: #374151;
  border-color: #4b5563;
  color: #ffffff;
}

.dark .form-input:focus {
  border-color: #3b82f6;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.dark .form-label {
  color: #d1d5db;
}

/* Table Styles - Updated for dropdown compatibility */
.orders-table {
  min-width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  position: relative;
  z-index: 1;
}

/* Table container to allow dropdowns to overflow */
.table-container {
  overflow: visible !important;
  position: relative;
  z-index: 1;
}

.table-container .overflow-x-auto {
  overflow-x: auto;
  overflow-y: visible !important;
}

.table-header {
  background-color: #f9fafb;
}

.dark .table-header {
  background-color: #374151;
}

.table-header-cell {
  padding: 0.75rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dark .table-header-cell {
  color: #d1d5db;
}

.table-row {
  background-color: #ffffff;
  transition: background-color 0.15s ease-in-out;
}

.table-row:hover {
  background-color: #f9fafb;
}

.dark .table-row {
  background-color: #1f2937;
}

.dark .table-row:hover {
  background-color: #374151;
}

.table-cell {
  padding: 1rem 1.5rem;
  white-space: nowrap;
  border-bottom: 1px solid #e5e7eb;
}

.dark .table-cell {
  border-bottom-color: #374151;
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 9999px;
  border: 1px solid;
}

/* Action Buttons */
.action-button {
  color: #3b82f6;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.15s ease-in-out;
}

.action-button:hover {
  color: #1d4ed8;
}

.dark .action-button {
  color: #60a5fa;
}

.dark .action-button:hover {
  color: #93c5fd;
}

.status-select {
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: #ffffff;
  color: #374151;
  transition: all 0.15s ease-in-out;
}

.status-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .status-select {
  background-color: #374151;
  border-color: #4b5563;
  color: #ffffff;
}

.dark .status-select:focus {
  border-color: #3b82f6;
}

/* Export Button */
.export-button {
  width: 100%;
  background-color: #059669;
  color: #ffffff;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.export-button:hover {
  background-color: #047857;
}

.export-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

/* Loading Spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state-icon {
  width: 3rem;
  height: 3rem;
  color: #9ca3af;
  margin: 0 auto 0.5rem;
}

.empty-state-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.dark .empty-state-title {
  color: #ffffff;
}

.empty-state-description {
  font-size: 0.875rem;
  color: #6b7280;
}

.dark .empty-state-description {
  color: #9ca3af;
}

/* Pagination */
.pagination-container {
  background-color: #ffffff;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #e5e7eb;
}

.dark .pagination-container {
  background-color: #1f2937;
  border-top-color: #374151;
}

.pagination-info {
  font-size: 0.875rem;
  color: #374151;
}

.dark .pagination-info {
  color: #d1d5db;
}

.pagination-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.pagination-button:hover:not(:disabled) {
  background-color: #f9fafb;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-button.active {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #1d4ed8;
}

.dark .pagination-button {
  background-color: #1f2937;
  border-color: #374151;
  color: #d1d5db;
}

.dark .pagination-button:hover:not(:disabled) {
  background-color: #374151;
}

.dark .pagination-button.active {
  background-color: #1e3a8a;
  border-color: #3b82f6;
  color: #93c5fd;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filter-section {
    padding: 1rem;
  }

  .table-cell {
    padding: 0.75rem 1rem;
  }

  .pagination-container {
    padding: 1rem;
  }

  .stats-card {
    margin-bottom: 1rem;
  }

  .status-dropdown {
    width: 9rem;
    margin-top: 0.25rem;
  }

  .status-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.6875rem;
  }
}

@media (max-width: 640px) {
  .table-header-cell,
  .table-cell {
    padding: 0.5rem 0.75rem;
  }

  .action-button {
    font-size: 0.75rem;
  }

  .status-select {
    font-size: 0.75rem;
    padding: 0.125rem 0.25rem;
  }

  .status-dropdown {
    width: 8rem;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 0.5rem;
  }

  .status-badge {
    padding: 0.2rem 0.4rem;
    font-size: 0.625rem;
  }

  .dropdown-chevron {
    width: 0.625rem;
    height: 0.625rem;
  }

  .status-dropdown-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }
}

/* Status Dropdown Styles - Enhanced Design with Fixed Z-Index */
.status-dropdown-container {
  position: relative;
  z-index: 10; /* Base z-index */
}

.status-dropdown-container.dropdown-open {
  z-index: 9999 !important; /* High z-index when open */
  position: relative;
}

/* Ensure table row doesn't interfere */
.table-row {
  position: relative;
  z-index: 1;
}

.status-dropdown-container.dropdown-open .table-row {
  z-index: 9998 !important;
}

.status-dropdown-container.dropdown-open .status-badge {
  transform: translateY(-1px);
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.15),
    0 4px 6px -2px rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
  z-index: 10000 !important;
  position: relative;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1.25;
  border-radius: 50px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  background-clip: padding-box;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: rgba(59, 130, 246, 0.4);
  filter: brightness(1.05);
}

.dark .status-badge:hover {
  border-color: rgba(59, 130, 246, 0.6);
  filter: brightness(1.1);
}

.status-badge:active {
  transform: translateY(0);
  transition: transform 0.1s ease-in-out;
}

.status-badge:focus {
  outline: none;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 3px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.dark .status-badge:focus {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.2),
    0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Status-specific styling with theme colors */
.status-badge.bg-yellow-100 {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
  border-color: #f59e0b;
  box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.2), 0 2px 4px -1px rgba(245, 158, 11, 0.1);
}

.status-badge.bg-blue-100 {
  background: linear-gradient(135deg, #dbeafe 0%, #93c5fd 100%);
  color: #1e40af;
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2), 0 2px 4px -1px rgba(59, 130, 246, 0.1);
}

.status-badge.bg-purple-100 {
  background: linear-gradient(135deg, #e9d5ff 0%, #c4b5fd 100%);
  color: #7c3aed;
  border-color: #8b5cf6;
  box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.2), 0 2px 4px -1px rgba(139, 92, 246, 0.1);
}

.status-badge.bg-green-100 {
  background: linear-gradient(135deg, #d1fae5 0%, #86efac 100%);
  color: #047857;
  border-color: #10b981;
  box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.2), 0 2px 4px -1px rgba(16, 185, 129, 0.1);
}

.status-badge.bg-red-100 {
  background: linear-gradient(135deg, #fee2e2 0%, #fca5a5 100%);
  color: #dc2626;
  border-color: #ef4444;
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2), 0 2px 4px -1px rgba(239, 68, 68, 0.1);
}

.status-badge.bg-gray-100 {
  background: linear-gradient(135deg, #f3f4f6 0%, #d1d5db 100%);
  color: #374151;
  border-color: #6b7280;
  box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.2), 0 2px 4px -1px rgba(107, 114, 128, 0.1);
}

/* Dark mode status badges with proper theme colors */
.dark .status-badge.bg-yellow-100 {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(245, 158, 11, 0.3) 100%);
  color: #fbbf24;
  border-color: #f59e0b;
  box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3), 0 2px 4px -1px rgba(245, 158, 11, 0.2);
}

.dark .status-badge.bg-blue-100 {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.3) 100%);
  color: #60a5fa;
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3), 0 2px 4px -1px rgba(59, 130, 246, 0.2);
}

.dark .status-badge.bg-purple-100 {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(139, 92, 246, 0.3) 100%);
  color: #a78bfa;
  border-color: #8b5cf6;
  box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.3), 0 2px 4px -1px rgba(139, 92, 246, 0.2);
}

.dark .status-badge.bg-green-100 {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(16, 185, 129, 0.3) 100%);
  color: #34d399;
  border-color: #10b981;
  box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3), 0 2px 4px -1px rgba(16, 185, 129, 0.2);
}

.dark .status-badge.bg-red-100 {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(239, 68, 68, 0.3) 100%);
  color: #f87171;
  border-color: #ef4444;
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.3), 0 2px 4px -1px rgba(239, 68, 68, 0.2);
}

.dark .status-badge.bg-gray-100 {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.2) 0%, rgba(107, 114, 128, 0.3) 100%);
  color: #d1d5db;
  border-color: #6b7280;
  box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3), 0 2px 4px -1px rgba(107, 114, 128, 0.2);
}

.status-dropdown {
  position: absolute !important;
  z-index: 99999 !important; /* Maximum z-index */
  margin-top: 0.5rem;
  width: 10rem;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  box-shadow:
    0 20px 25px -5px rgba(59, 130, 246, 0.1),
    0 10px 10px -5px rgba(59, 130, 246, 0.04),
    0 0 0 1px rgba(59, 130, 246, 0.05);
  overflow: hidden;
  animation: dropdownFadeIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  /* Force layer creation for better stacking */
  will-change: transform, opacity;
  transform: translateZ(0);
}

.dark .status-dropdown {
  background: rgba(31, 41, 55, 0.98);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.4),
    0 10px 10px -5px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.2);
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced dropdown animation for portal */
.dropdown-portal {
  animation: dropdownFadeIn 0.15s ease-out;
}

.status-dropdown-item {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  background: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.status-dropdown-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  transition: width 0.3s ease-in-out;
}

.status-dropdown-item:hover:not(:disabled)::before {
  width: 3px;
}

.status-dropdown-item:hover:not(:disabled) {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.08), rgba(59, 130, 246, 0.03));
  transform: translateX(4px);
  padding-left: 1.25rem;
  color: #1e40af;
}

.dark .status-dropdown-item:hover:not(:disabled) {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.08));
  color: #60a5fa;
}

.status-dropdown-item:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  color: #9ca3af;
}

.dark .status-dropdown-item:disabled {
  color: #6b7280;
}

.status-dropdown-item.active {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
  color: #1e40af;
  font-weight: 600;
  position: relative;
}

.status-dropdown-item.active::after {
  content: '✓';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #3b82f6;
  font-weight: bold;
  font-size: 0.875rem;
}

.dark .status-dropdown-item.active {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
  color: #60a5fa;
}

.dark .status-dropdown-item.active::after {
  color: #60a5fa;
}

.dropdown-chevron {
  margin-left: 0.375rem;
  width: 0.875rem;
  height: 0.875rem;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.7;
}

.dropdown-open .dropdown-chevron {
  transform: rotate(180deg);
  opacity: 1;
}

/* Loading state */
.status-dropdown-item.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.status-dropdown-item.loading::after {
  content: '';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.dark .status-dropdown-item.loading::after {
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-top: 2px solid #60a5fa;
}

@keyframes spin {
  from { transform: translateY(-50%) rotate(0deg); }
  to { transform: translateY(-50%) rotate(360deg); }
}
