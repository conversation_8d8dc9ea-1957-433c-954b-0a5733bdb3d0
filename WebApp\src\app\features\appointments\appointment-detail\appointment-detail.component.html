<div class="max-w-7xl mx-auto p-6 dark:bg-gray-900">
  <!-- Header with gradient background -->
  <div
    class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 overflow-hidden">
    <div class="px-6 py-4 border-b border-blue-100 dark:border-gray-700 flex items-center justify-between">
      <div class="flex items-center">
        <div class="bg-blue-500 p-2 rounded-lg mr-3">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        </div>
        <div>
          <h1 class="text-xl font-bold text-gray-800 dark:text-white">Appointment Details</h1>
          <p class="text-gray-600 dark:text-gray-300 text-sm">
            View complete information about this appointment
          </p>
        </div>
      </div>
      <div class="flex items-center gap-3">
        <button *ngIf="canEditAppointment()" (click)="editAppointment()"
          class="inline-flex items-center px-3 py-2 border border-blue-300 dark:border-blue-700 rounded-md shadow-sm text-sm font-medium text-blue-700 dark:text-blue-200 bg-blue-50 dark:bg-blue-900 hover:bg-blue-100 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          Edit
        </button>
        <button (click)="goBack()"
          class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to List
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="flex justify-center items-center p-12">
      <div class="flex flex-col items-center">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">Loading appointment details...</p>
      </div>
    </div>

    <!-- Appointment Details -->
    <div *ngIf="!isLoading && appointment" class="p-6">

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column - Main Information -->
        <div class="lg:col-span-2 space-y-6">
          <div>
            <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b pb-2 mb-4">Appointment Information</h2>

            <!-- Status -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Status</label>
              <div class="flex items-center gap-3">
                <div
                  [class]="'px-3 py-1 rounded-full text-sm font-medium border flex items-center gap-2 ' + getStatusColor(appointment.status) + ' dark:text-gray-100'">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      [attr.d]="getStatusIcon(appointment.status)"></path>
                  </svg>
                  {{appointment.status || 'Pending'}}
                </div>
              </div>
            </div>

            <!-- Status Update (Doctor only) -->
            <div *ngIf=" isDoctor" class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">Update Status</label>

              <!-- Both Admin and Doctor see the same clean interface -->
              <div class="flex flex-wrap gap-3 " >
                <button (click)="confirmAppointment()"
                  [disabled]="isUpdatingStatus || appointment.status === 'Confirmed'"
                  [class]="appointment.status === 'Confirmed' ?
                    'px-4 py-2 rounded-md text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-700 cursor-default' :
                    'px-4 py-2 rounded-md text-sm font-medium bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 disabled:opacity-50'">
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Confirm Appointment
                </button>

                <button (click)="completeAppointment()"
                  [disabled]="isUpdatingStatus || appointment.status === 'Completed'"
                  [class]="appointment.status === 'Completed' ?
                    'px-4 py-2 rounded-md text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-700 cursor-default' :
                    'px-4 py-2 rounded-md text-sm font-medium bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 disabled:opacity-50'">
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Complete Appointment
                </button>

                <button (click)="openCancelDialog()"
                  [disabled]="isUpdatingStatus || appointment.status === 'Cancelled'"
                  [class]="appointment.status === 'Cancelled' ?
                    'px-4 py-2 rounded-md text-sm font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-700 cursor-default' :
                    'px-4 py-2 rounded-md text-sm font-medium bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-100 border border-gray-300 dark:border-gray-700 hover:bg-red-50 dark:hover:bg-red-900 hover:text-red-700 dark:hover:text-red-200 transition-colors duration-200 disabled:opacity-50'">
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Cancel Appointment
                </button>
              </div>

              <!-- Both admin and doctor now use the same clean interface above -->

              <div *ngIf="isUpdatingStatus" class="flex items-center gap-2 mt-2 text-blue-600 dark:text-blue-400">
                <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                <span class="text-sm">Updating status...</span>
              </div>
            </div>

            <!-- Assign Doctor Section (Admin Only for Pending Appointments) -->
            <div *ngIf="isAdmin && appointment.status === 'Pending'" class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">Assign Doctor</label>
              <div class="flex items-center gap-3">
                <button (click)="openDoctorSelectionDialog()" [disabled]="isAssigningDoctor"
                  class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-800 rounded-lg shadow-sm hover:from-blue-700 hover:to-indigo-700 dark:hover:from-blue-800 dark:hover:to-indigo-900 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                    </path>
                  </svg>
                  <span *ngIf="isAssigningDoctor" class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                      </path>
                    </svg>
                    Assigning...
                  </span>
                  <span *ngIf="!isAssigningDoctor">Assign Doctor</span>
                </button>
              </div>
            </div>

            <!-- Assigned Doctor Info (Show when doctor is assigned) -->
            <div *ngIf="appointment.assignedDoctorEmail" class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Assigned Doctor</label>
              <div class="flex items-center gap-2 px-3 py-2 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md">
                <svg class="w-5 h-5 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span class="text-green-800 dark:text-green-200 font-medium">{{appointment.assignedDoctorEmail}}</span>
                <span *ngIf="appointment.assignedDate" class="text-green-600 dark:text-green-300 text-sm">
                  (Assigned: {{formatDateTime(appointment.assignedDate)}})
                </span>
              </div>
            </div>

            <!-- Confirmation Note (Show when appointment is confirmed) -->
            <div *ngIf="appointment.confirmationNote" class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Confirmation Note</label>
              <div class="px-3 py-2 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md">
                <p class="text-blue-800 dark:text-blue-200 text-sm">{{appointment.confirmationNote}}</p>
              </div>
            </div>

            <!-- Completion Note (Show when appointment is completed) -->
            <div *ngIf="appointment.completionNote" class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Completion Note</label>
              <div class="px-3 py-2 bg-purple-50 dark:bg-purple-900 border border-purple-200 dark:border-purple-700 rounded-md">
                <p class="text-purple-800 dark:text-purple-200 text-sm">{{appointment.completionNote}}</p>
              </div>
            </div>

            <!-- Cancellation Note (Show when appointment is cancelled) -->
            <div *ngIf="appointment.status === 'Cancelled' && cancellationDisplayNote" class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Cancellation Reason</label>
              <div class="px-3 py-2 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
                <p class="text-red-800 dark:text-red-200 text-sm">{{cancellationDisplayNote}}</p>
                <p *ngIf="cancelledDisplayDate" class="text-red-600 dark:text-red-300 text-xs mt-1">
                  Cancelled on: {{cancelledDisplayDate}}
                </p>
              </div>
            </div>

            <!-- Basic Details Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <!-- Patient Email -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Patient Email</label>
                <div class="flex items-center gap-2">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-white">{{appointment.patientEmail}}</span>
                </div>
              </div>

              <!-- Scheduled Time -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Scheduled Time</label>
                <div class="flex items-center gap-2">
                  <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <div>
                    <span class="text-sm text-gray-900 dark:text-white">{{formatDateTime(appointment.scheduledTime)}}</span>
                    <p class="text-xs text-blue-600 dark:text-blue-300">{{getRelativeTime(appointment.scheduledTime)}}</p>
                  </div>
                </div>
              </div>

              <!-- Created Date -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Created Date</label>
                <div class="flex items-center gap-2">
                  <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                      </path>
                    </svg>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-white">{{formatDateTimeShort(appointment.createDate)}}</span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b pb-2 mb-4">Appointment Message</h2>

            <!-- Message Content -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">

              @if(appointment.confirmationNote){
                <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                  {{appointment.confirmationNote}}</p>
              } @else if (appointment.completionNote) {
                <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                  {{appointment.completionNote}}</p>
              } @else {
                <p class="text-sm text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                  {{appointment.message}}</p>
              }
              <ng-template #noMessage>
                <div class="flex items-center justify-center py-6 text-gray-500 dark:text-gray-400">
                  <div class="text-center">
                    <svg class="w-8 h-8 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
                      </path>
                    </svg>
                    <p class="text-sm">No message provided</p>
                  </div>
                </div>
              </ng-template>
            </div>
          </div>
        </div>

        <!-- Right Column - Image and Actions -->
        <div>
          <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b pb-2 mb-4">Appointment Image</h2>

          <!-- Image Display Card -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-6">
            <!-- Image preview with loading indicator and error handling -->
            <div *ngIf="appointment.imageUrl; else noImageTemplate">
              <div class="border rounded-lg overflow-hidden w-full relative bg-white dark:bg-gray-900">
                <!-- Loading spinner -->
                <div *ngIf="isImageLoading"
                  class="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80">
                  <div class="w-8 h-8 border-4 border-t-blue-500 border-blue-200 rounded-full animate-spin"></div>
                </div>

                <!-- Image -->
                <img [src]="getImageUrl(appointment.imageUrl)" class="w-full h-auto max-h-64 object-contain"
                  alt="Appointment image" (load)="isImageLoading = false" (error)="onImageError($event)"
                  [class.hidden]="imageLoadError" />

                <!-- Error placeholder -->
                <div *ngIf="imageLoadError"
                  class="bg-gray-100 dark:bg-gray-800 w-full h-64 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2v12a2 2 0 002 2z" />
                  </svg>
                  <p>Image could not be loaded</p>
                </div>
              </div>
            </div>

            <!-- No Image Template -->
            <ng-template #noImageTemplate>
              <div class="bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 dark:text-gray-600 mb-2" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2v12a2 2 0 002 2z" />
                </svg>
                <p class="text-sm text-gray-500 dark:text-gray-400">No image available</p>
              </div>
            </ng-template>
          </div>

          <!-- Quick Actions -->
          <div class="space-y-3">
            <button *ngIf="canEditAppointment()" (click)="editAppointment()"
              class="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                </path>
              </svg>
              Edit Appointment
            </button>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Cancel Appointment Dialog -->
<div *ngIf="showCancelDialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
  <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-auto">
    <!-- Dialog Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Cancel Appointment</h3>
      <button (click)="closeCancelDialog()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Dialog Content -->
    <div class="p-6">
      <div class="mb-4">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Please provide a reason for cancelling this appointment. This message will be sent to the patient.
        </p>

        <label for="cancellationReason" class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
          Cancellation Reason <span class="text-red-500">*</span>
        </label>
        <textarea
          id="cancellationReason"
          [(ngModel)]="cancellationReason"
          rows="4"
          placeholder="Please explain why this appointment needs to be cancelled..."
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white resize-none"
          [class.border-red-500]="showValidationError && !cancellationReason.trim()">
        </textarea>

        <div *ngIf="showValidationError && !cancellationReason.trim()" class="mt-1 text-sm text-red-600 dark:text-red-400">
          Cancellation reason is required
        </div>
      </div>
    </div>

    <!-- Dialog Footer -->
    <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
      <button (click)="closeCancelDialog()"
              [disabled]="isCancelling"
              class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-100 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50">
        Cancel
      </button>
      <button (click)="cancelAppointment()"
              [disabled]="isCancelling || !cancellationReason.trim()"
              class="px-4 py-2 text-sm font-medium text-white bg-red-600 dark:bg-red-700 rounded-md hover:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 flex items-center gap-2">
        <svg *ngIf="isCancelling" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span *ngIf="isCancelling">Cancelling...</span>
        <span *ngIf="!isCancelling">Cancel Appointment</span>
      </button>
    </div>
  </div>
</div>
