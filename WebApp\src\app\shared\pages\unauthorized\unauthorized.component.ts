import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ButtonComponent } from '../../components/button/button.component';

@Component({
  selector: 'app-unauthorized',
  standalone: true,
  imports: [CommonModule, RouterModule, ButtonComponent],
  template: `
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto h-24 w-24 bg-red-100 rounded-full flex items-center justify-center">
          <svg class="h-12 w-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>

        <!-- Error Message -->
        <div>
          <h2 class="text-3xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p class="text-gray-600 mb-6">
            You don't have permission to access this page. Please contact your administrator if you believe this is an error.
          </p>
        </div>

        <!-- Actions -->
        <div class="space-y-4">
          <app-button
            variant="primary"
            [fullWidth]="true"
            routerLink="/dashboard">
            Go to Dashboard
          </app-button>
          
          <app-button
            variant="secondary"
            [fullWidth]="true"
            (clicked)="goBack()">
            Go Back
          </app-button>
        </div>

        <!-- Help Text -->
        <div class="mt-8 p-4 bg-blue-50 rounded-lg">
          <p class="text-sm text-blue-800">
            <strong>Need help?</strong> Contact your system administrator to request access to this feature.
          </p>
        </div>
      </div>
    </div>
  `,
  styles: []
})
export class UnauthorizedComponent {
  goBack(): void {
    window.history.back();
  }
}
