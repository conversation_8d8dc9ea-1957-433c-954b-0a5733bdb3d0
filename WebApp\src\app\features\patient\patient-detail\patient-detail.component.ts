
import { Compo<PERSON>, OnIni<PERSON>, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { PatientInfo, PatientInfoServiceProxy, WoundRequestServiceProxy, WoundRequestDto, RequestFilesServiceProxy, RequestFileDto } from '../../../../shared/service-proxies/service-proxies';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AuthService } from '../../../../shared/services/auth.service';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { FileInfoDialogComponent } from '../../user/user-request/file-info-dialog/file-info-dialog.component';
import { Subject, takeUntil } from 'rxjs';

interface ImageAttachment {
  id: number;
  name: string;
  url: string;
  uploadDate: Date;
  projectId: number;
  projectTitle: string;
  size?: string;
  aiAnalysis?: string;
  medicalFindings?: string;
  requestFileData?: RequestFileDto;
}

interface ProjectWithAttachments {
  id: number;
  subject: string;
  message: string;
  status: string;
  createdDate: Date;
  imageAttachments: ImageAttachment[];
}

@Component({
  selector: 'app-patient-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  templateUrl: './patient-detail.component.html',
  styleUrl: './patient-detail.component.css'
})
export class PatientDetailComponent implements OnInit, OnDestroy {
  baseUrl = getRemoteServiceBaseUrl();
  patient: PatientInfo | null = null;
  isLoading = false;
  patientId?: string;
  isAdmin = false;
  canManagePatients = false;
  patientProjects: ProjectWithAttachments[] = [];
  allImageAttachments: ImageAttachment[] = [];
  isLoadingImages = false;
  selectedImageIndex = 0;
  isLightboxOpen = false;
  imageGalleryFilter: 'all' | 'recent' | 'byProject' = 'all';
  isGridView = true;
  isComparisonMode = false;
  selectedImagesForComparison: ImageAttachment[] = [];
  isComparisonViewOpen = false;
  maxComparisonImages = 2;
  zoomLevel = 1;
  minZoom = 0.5;
  maxZoom = 3;
  zoomStep = 0.25;
  imageTransform = { x: 0, y: 0, scale: 1 };
  isDragging = false;
  lastMousePosition = { x: 0, y: 0 };
  imageLoadError = false;
  comparisonZoom = {
    image1: { zoomLevel: 1, transform: { x: 0, y: 0, scale: 1 }, isDragging: false, lastMousePosition: { x: 0, y: 0 } },
    image2: { zoomLevel: 1, transform: { x: 0, y: 0, scale: 1 }, isDragging: false, lastMousePosition: { x: 0, y: 0 } }
  };
  private destroy$ = new Subject<void>();

  constructor(
    private patientService: PatientInfoServiceProxy,
    private woundRequestService: WoundRequestServiceProxy,
    private requestFilesService: RequestFilesServiceProxy,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private dialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.isAdmin = this.authService.hasRole('Admin');
    this.canManagePatients = this.authService.hasRole('Admin') || this.authService.hasRole('Doctor');

    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['id']) {
        this.patientId = params['id'];
        this.loadPatientDetails(this.patientId!);
      } else {
        this.showSnackbar('Invalid patient ID');
        this.router.navigate(['/patients']);
      }
    });

    document.addEventListener('keydown', this.handleKeyPress);
    window.addEventListener('scroll', this.handleScroll, { passive: true });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    document.removeEventListener('keydown', this.handleKeyPress);
    window.removeEventListener('scroll', this.handleScroll);
  }

  private showSnackbar = (message: string): void => {
    this.snackBar.open(message, 'Close', { duration: 3000 });
  }

  loadPatientDetails = (id: string): void => {
    this.isLoading = true;
    this.patientService.getPatientInfoById(id).pipe(takeUntil(this.destroy$)).subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data) {
          this.patient = data;
          this.loadPatientProjects(this.patient.email || '');
        } else {
          this.showSnackbar('Patient not found');
          this.router.navigate(['/patients']);
        }
      },
      error: () => {
        this.isLoading = false;
        this.showSnackbar('Error loading patient details');
        this.router.navigate(['/patients']);
      }
    });
  }

  loadPatientProjects = (patientEmail: string): void => {
    if (!patientEmail) return;

    this.isLoadingImages = true;
    const apiCall = this.isAdmin
      ? this.woundRequestService.getAll()
      : this.woundRequestService.getAllForDoctor(this.authService.getUser()?.email);

    if (!apiCall) {
      this.isLoadingImages = false;
      return;
    }

    apiCall.pipe(takeUntil(this.destroy$)).subscribe({
      next: (woundRequests: WoundRequestDto[]) => {
        const patientProjects = woundRequests.filter(request =>
          request.userEmail?.toLowerCase() === patientEmail.toLowerCase()
        );
        this.processProjectsForImageGalleryWithAzure(patientProjects);
        this.isLoadingImages = false;
      },
      error: () => {
        this.isLoadingImages = false;
        this.showSnackbar('Error loading patient projects');
      }
    });
  }



  private processProjectsForImageGalleryWithAzure = (projects: any[]): void => {
    this.patientProjects = [];
    this.allImageAttachments = [];

    const projectPromises = projects.map(project => {
      return new Promise<void>((resolve) => {
        const projectWithAttachments: ProjectWithAttachments = {
          id: project.id,
          subject: project.subject || project.workspaceTitle || `Request #${project.id}`,
          message: project.message || '',
          status: project.status || 'Unknown',
          createdDate: project.createdDate ? new Date(project.createdDate) : new Date(),
          imageAttachments: []
        };

        this.requestFilesService.getByRequest(project.id).pipe(takeUntil(this.destroy$)).subscribe({
          next: (requestFiles: RequestFileDto[]) => {
            requestFiles.forEach((file: RequestFileDto) => {
              if (file.mimeType?.startsWith('image/')) {
                const imageUrl = file.blobUrl || file.thumbnailUrl ||
                  (file.id ? `${this.baseUrl}/api/RequestFiles/Download/${file.id}` : '');

                const imageAttachment: ImageAttachment = {
                  id: this.allImageAttachments.length + 1,
                  name: file.originalFileName || 'Untitled Image',
                  url: imageUrl,
                  uploadDate: file.createdDate ? new Date(file.createdDate.toString()) : new Date(),
                  projectId: project.id,
                  projectTitle: projectWithAttachments.subject,
                  size: this.formatFileSize(file.fileSize || 0),
                  aiAnalysis: file.aiAnalysis,
                  medicalFindings: file.medicalFindings,
                  requestFileData: file
                };

                projectWithAttachments.imageAttachments.push(imageAttachment);
                this.allImageAttachments.push(imageAttachment);
              }
            });

            if (projectWithAttachments.imageAttachments.length > 0) {
              this.patientProjects.push(projectWithAttachments);
            }
            resolve();
          },
          error: () => resolve()
        });
      });
    });

    Promise.all(projectPromises).then(() => {
      this.allImageAttachments.sort((a, b) =>
        new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime()
      );
    });
  }

  private formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  openImageLightbox = (imageIndex: number): void => {
    this.selectedImageIndex = imageIndex;
    this.isLightboxOpen = true;
    this.resetZoom();
    document.body.style.overflow = 'hidden';
  }

  closeLightbox = (): void => {
    this.isLightboxOpen = false;
    this.resetZoom();
    document.body.style.overflow = 'auto';
  }

  nextImage = (): void => {
    this.selectedImageIndex = (this.selectedImageIndex + 1) % this.allImageAttachments.length;
    this.resetZoom();
  }

  previousImage = (): void => {
    this.selectedImageIndex = this.selectedImageIndex === 0
      ? this.allImageAttachments.length - 1
      : this.selectedImageIndex - 1;
    this.resetZoom();
  }

  setImageGalleryFilter = (filter: 'all' | 'recent' | 'byProject'): void => {
    this.imageGalleryFilter = filter;
  }

  getFilteredImages = (): ImageAttachment[] => {
    switch (this.imageGalleryFilter) {
      case 'recent':
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return this.allImageAttachments.filter(img => new Date(img.uploadDate) >= thirtyDaysAgo);
      case 'byProject':
        return this.allImageAttachments;
      default:
        return this.allImageAttachments;
    }
  }

  downloadImage = (imageAttachment: ImageAttachment): void => {
    window.open(imageAttachment.url, '_blank');
  }

  zoomIn = (): void => {
    if (this.zoomLevel < this.maxZoom) {
      this.zoomLevel = Math.min(this.zoomLevel + this.zoomStep, this.maxZoom);
      this.updateImageTransform();
    }
  }

  zoomOut = (): void => {
    if (this.zoomLevel > this.minZoom) {
      this.zoomLevel = Math.max(this.zoomLevel - this.zoomStep, this.minZoom);
      this.updateImageTransform();
    }
  }

  resetZoom = (): void => {
    this.zoomLevel = 1;
    this.imageTransform = { x: 0, y: 0, scale: 1 };
  }

  updateImageTransform = (): void => { this.imageTransform.scale = this.zoomLevel; }

  getImageTransformStyle = (): string =>
    `transform: translate(${this.imageTransform.x}px, ${this.imageTransform.y}px) scale(${this.imageTransform.scale})`;

  onImageMouseDown = (event: MouseEvent): void => {
    if (this.zoomLevel > 1) {
      this.isDragging = true;
      this.lastMousePosition = { x: event.clientX, y: event.clientY };
      event.preventDefault();
    }
  }

  onImageMouseMove = (event: MouseEvent): void => {
    if (this.isDragging && this.zoomLevel > 1) {
      const deltaX = event.clientX - this.lastMousePosition.x;
      const deltaY = event.clientY - this.lastMousePosition.y;
      this.imageTransform.x += deltaX;
      this.imageTransform.y += deltaY;
      this.lastMousePosition = { x: event.clientX, y: event.clientY };
      event.preventDefault();
    }
  }

  onImageMouseUp = (_: MouseEvent): void => { this.isDragging = false; }

  // Wheel zoom functionality
  onImageWheel = (event: WheelEvent): void => {
    event.preventDefault();
    if (event.deltaY < 0) this.zoomIn();
    else this.zoomOut();
  }

  editPatient = (): void => {
    if (this.patient) this.router.navigate(['/patients/edit', this.patient.id]);
  }

  deletePatient = (): void => {
    if (!this.patient || !confirm(`Are you sure you want to delete patient "${this.patient.fullName}"?`)) return;

    this.patientService.deletePatientInfo(this.patient.id).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.showSnackbar('Patient deleted successfully');
        this.router.navigate(['/patients']);
      },
      error: () => this.showSnackbar('Error deleting patient')
    });
  }

  goBack = (): void => { this.router.navigate(['/patients']); }

  getPatientAge(): number {
    if (!this.patient?.dateOfBirth) return 0;
    const today = new Date();
    const birthDate = new Date(this.patient.dateOfBirth.toJSDate());
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  formatDate(date: any): string {
    if (!date) return '-';

    // Handle different date types
    if (date.toFormat) {
      return date.toFormat('MMMM dd, yyyy');
    } else if (date instanceof Date) {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } else {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  }

  formatDateShort(date: any): string {
    if (!date) return '-';

    // Handle different date types
    if (date.toFormat) {
      return date.toFormat('MMM dd, yyyy');
    } else if (date instanceof Date) {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } else {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  }

  getImageUrl(imageUrl: string | undefined): string {
    if (!imageUrl) return '';
    if (imageUrl.startsWith('http')) return imageUrl;
    return `${this.baseUrl}/api/File/Getfile/${imageUrl}`;
  }

  getPatientInitials(): string {
    if (!this.patient?.fullName) return 'P';
    const names = this.patient.fullName.split(' ');
    if (names.length >= 2) {
      return (names[0].charAt(0) + names[1].charAt(0)).toUpperCase();
    }
    return this.patient.fullName.charAt(0).toUpperCase();
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    const originalSrc = img.src;

    console.error('Failed to load image:', originalSrc);

    // Extract filename from the URL
    let fileName = '';
    const urlParts = originalSrc.split('/');
    if (urlParts.length > 0) {
      fileName = decodeURIComponent(urlParts[urlParts.length - 1]);
    }

    console.log('Extracted filename for retry:', fileName);

    // Try alternative URL formats with more comprehensive list
    const retryCount = parseInt(img.dataset['retryCount'] || '0');
    const alternatives = [
      `${this.baseUrl}/api/Files/Download/${encodeURIComponent(fileName)}`,
      `${this.baseUrl}/api/File/Download/${encodeURIComponent(fileName)}`,
      `${this.baseUrl}/api/files/${encodeURIComponent(fileName)}`,
      `${this.baseUrl}/files/${encodeURIComponent(fileName)}`,
      `${this.baseUrl}/uploads/${encodeURIComponent(fileName)}`,
      `${this.baseUrl}/static/uploads/${encodeURIComponent(fileName)}`,
      `${this.baseUrl}/wwwroot/uploads/${encodeURIComponent(fileName)}`,
      `${this.baseUrl}/content/uploads/${encodeURIComponent(fileName)}`,
      `${this.baseUrl}/assets/uploads/${encodeURIComponent(fileName)}`,
      `${this.baseUrl}/${encodeURIComponent(fileName)}` // Direct access
    ];

    if (retryCount < alternatives.length && fileName) {
      const nextUrl = alternatives[retryCount];
      console.log(`Trying alternative URL ${retryCount + 1}/${alternatives.length}:`, nextUrl);

      img.dataset['retryCount'] = (retryCount + 1).toString();
      img.src = nextUrl;

      // Add a small delay to prevent rapid retries
      setTimeout(() => {
        // Check if the image failed to load after a brief delay
        if (img.naturalWidth === 0 && img.naturalHeight === 0) {
          console.log(`Alternative URL ${retryCount + 1} also failed, will try next...`);
        }
      }, 500);

      return;
    }

    // If all attempts fail, show a more visible placeholder
    console.log('All URL attempts failed, showing placeholder. Attempted URLs:', alternatives);
    this.imageLoadError = true;

    // Create a more visible error placeholder with debugging info
    const errorPlaceholder = `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="300" height="200" viewBox="0 0 300 200">
      <rect width="300" height="200" fill="%23f3f4f6" stroke="%23d1d5db" stroke-width="2"/>
      <text x="150" y="100" text-anchor="middle" fill="%23374151" font-family="Arial, sans-serif" font-size="14">
        Image Not Found
      </text>
      <text x="150" y="120" text-anchor="middle" fill="%236b7280" font-family="Arial, sans-serif" font-size="10">
        ${fileName.length > 30 ? fileName.substring(0, 30) + '...' : fileName}
      </text>
      <circle cx="150" cy="80" r="20" fill="none" stroke="%239ca3af" stroke-width="2"/>
      <path d="M140 70 L160 90 M160 70 L140 90" stroke="%239ca3af" stroke-width="2"/>
    </svg>`;

    img.src = errorPlaceholder;
    img.onerror = null; // Prevent infinite loop
  }

  // Navigation methods
  viewAppointments(): void {
    // Navigate to appointments filtered by this patient
    this.router.navigate(['/appointments'], { queryParams: { patientEmail: this.patient?.email } });
  }

  viewMedicalNotes(): void {
    // Navigate to notes filtered by this patient
    this.router.navigate(['/notes'], { queryParams: { patientId: this.patient?.id } });
  }

  scheduleAppointment(): void {
    // Navigate to create appointment with patient email pre-filled
    this.router.navigate(['/appointments/add'], { queryParams: { patientEmail: this.patient?.email } });
  }

  handleKeyPress = (event: KeyboardEvent): void => {
    if (!this.isLightboxOpen) return;
    const keyActions: Record<string, () => void> = {
      'Escape': this.closeLightbox,
      'ArrowLeft': () => { event.preventDefault(); this.previousImage(); },
      'ArrowRight': () => { event.preventDefault(); this.nextImage(); }
    };
    keyActions[event.key]?.();
  }



  // View toggle methods
  setGridView(): void {
    this.isGridView = true;
    console.log('Switched to grid view');
  }

  setListView(): void {
    this.isGridView = false;
    console.log('Switched to list view');
  }

  // Image comparison methods
  toggleComparisonMode(): void {
    this.isComparisonMode = !this.isComparisonMode;
    if (!this.isComparisonMode) {
      this.selectedImagesForComparison = [];
      this.isComparisonViewOpen = false;
    }
    console.log('Comparison mode toggled:', this.isComparisonMode);
  }

  selectImageForComparison = (image: ImageAttachment, event: Event): void => {
    event.stopPropagation();
    const index = this.selectedImagesForComparison.findIndex(img => img.id === image.id);
    if (index > -1) {
      this.selectedImagesForComparison.splice(index, 1);
    } else if (this.selectedImagesForComparison.length < this.maxComparisonImages) {
      this.selectedImagesForComparison.push(image);
    }
  }

  isImageSelectedForComparison = (image: ImageAttachment): boolean =>
    this.selectedImagesForComparison.some(img => img.id === image.id);

  canSelectMoreImages = (): boolean => this.selectedImagesForComparison.length < this.maxComparisonImages;

  deselectImageForComparison = (image: ImageAttachment, event: Event): void => {
    event.stopPropagation();
    const index = this.selectedImagesForComparison.findIndex(img => img.id === image.id);
    if (index > -1) this.selectedImagesForComparison.splice(index, 1);
  }
  openComparisonView = (): void => {
    if (this.selectedImagesForComparison.length === 2) {
      this.isComparisonViewOpen = !this.isComparisonViewOpen;
      if (!this.isComparisonViewOpen) this.resetAllComparisonZoom();
    }
  }

  closeComparisonView = (): void => {
    this.isComparisonViewOpen = false;
    this.resetAllComparisonZoom();
  }

  clearComparisonSelection = (): void => { this.selectedImagesForComparison = []; }

  getComparisonSelectionIndex(image: ImageAttachment): number {
    return this.selectedImagesForComparison.findIndex(img => img.id === image.id) + 1;
  }

  quickCompareWithThis(image: ImageAttachment): void {
    // If not in comparison mode, enable it
    if (!this.isComparisonMode) {
      this.isComparisonMode = true;
    }

    // Clear any existing selections
    this.selectedImagesForComparison = [];

    // Add the current image as the first selection
    this.selectedImagesForComparison.push(image);

    console.log('Quick compare initiated with image:', image.name);
    console.log('Select another image to compare with this one');
  }

  getTimeDifference(date1: Date, date2: Date): string {
    const diff = Math.abs(new Date(date2).getTime() - new Date(date1).getTime());
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} apart`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} apart`;
    } else {
      return 'Less than an hour apart';
    }
  }

  downloadComparisonImages(): void {
    if (this.selectedImagesForComparison.length === 2) {
      this.selectedImagesForComparison.forEach((image, index) => {
        setTimeout(() => {
          this.downloadImage(image);
        }, index * 500); // Delay downloads to avoid browser blocking
      });
      console.log('Downloading both comparison images');
    }
  }

  // Enhanced image preview method
  openImagePreview(image: ImageAttachment): void {
    // Open image in a new tab for preview
    window.open(image.url, '_blank', 'noopener,noreferrer');
    console.log('Opening image preview for:', image.name, image.url);
  }

  // Comparison mode zoom methods
  zoomInComparison(imageIndex: 1 | 2): void {
    const imageKey = `image${imageIndex}` as 'image1' | 'image2';
    if (this.comparisonZoom[imageKey].zoomLevel < this.maxZoom) {
      this.comparisonZoom[imageKey].zoomLevel = Math.min(
        this.comparisonZoom[imageKey].zoomLevel + this.zoomStep,
        this.maxZoom
      );
      this.updateComparisonImageTransform(imageIndex);
    }
  }

  zoomOutComparison(imageIndex: 1 | 2): void {
    const imageKey = `image${imageIndex}` as 'image1' | 'image2';
    if (this.comparisonZoom[imageKey].zoomLevel > this.minZoom) {
      this.comparisonZoom[imageKey].zoomLevel = Math.max(
        this.comparisonZoom[imageKey].zoomLevel - this.zoomStep,
        this.minZoom
      );
      this.updateComparisonImageTransform(imageIndex);
    }
  }

  resetZoomComparison(imageIndex: 1 | 2): void {
    const imageKey = `image${imageIndex}` as 'image1' | 'image2';
    this.comparisonZoom[imageKey].zoomLevel = 1;
    this.comparisonZoom[imageKey].transform = { x: 0, y: 0, scale: 1 };
  }

  resetAllComparisonZoom(): void {
    this.resetZoomComparison(1);
    this.resetZoomComparison(2);
  }

  syncComparisonZoom(): void {
    // Sync zoom levels of both images
    const maxZoom = Math.max(
      this.comparisonZoom.image1.zoomLevel,
      this.comparisonZoom.image2.zoomLevel
    );
    this.comparisonZoom.image1.zoomLevel = maxZoom;
    this.comparisonZoom.image2.zoomLevel = maxZoom;
    this.updateComparisonImageTransform(1);
    this.updateComparisonImageTransform(2);
  }

  updateComparisonImageTransform(imageIndex: 1 | 2): void {
    const imageKey = `image${imageIndex}` as 'image1' | 'image2';
    this.comparisonZoom[imageKey].transform.scale = this.comparisonZoom[imageKey].zoomLevel;
  }

  getComparisonImageTransformStyle(imageIndex: 1 | 2): string {
    const imageKey = `image${imageIndex}` as 'image1' | 'image2';
    const transform = this.comparisonZoom[imageKey].transform;
    return `transform: translate(${transform.x}px, ${transform.y}px) scale(${transform.scale})`;
  }

  // Mouse events for comparison image pan functionality
  onComparisonImageMouseDown(event: MouseEvent, imageIndex: 1 | 2): void {
    const imageKey = `image${imageIndex}` as 'image1' | 'image2';
    if (this.comparisonZoom[imageKey].zoomLevel > 1) {
      this.comparisonZoom[imageKey].isDragging = true;
      this.comparisonZoom[imageKey].lastMousePosition = { x: event.clientX, y: event.clientY };
      event.preventDefault();
    }
  }

  onComparisonImageMouseMove(event: MouseEvent, imageIndex: 1 | 2): void {
    const imageKey = `image${imageIndex}` as 'image1' | 'image2';
    if (this.comparisonZoom[imageKey].isDragging && this.comparisonZoom[imageKey].zoomLevel > 1) {
      const deltaX = event.clientX - this.comparisonZoom[imageKey].lastMousePosition.x;
      const deltaY = event.clientY - this.comparisonZoom[imageKey].lastMousePosition.y;

      this.comparisonZoom[imageKey].transform.x += deltaX;
      this.comparisonZoom[imageKey].transform.y += deltaY;

      this.comparisonZoom[imageKey].lastMousePosition = { x: event.clientX, y: event.clientY };
      event.preventDefault();
    }
  }

  onComparisonImageMouseUp = (_: MouseEvent, imageIndex: 1 | 2): void => {
    const imageKey = `image${imageIndex}` as 'image1' | 'image2';
    this.comparisonZoom[imageKey].isDragging = false;
  }

  // Wheel zoom functionality for comparison images
  onComparisonImageWheel(event: WheelEvent, imageIndex: 1 | 2): void {
    event.preventDefault();
    if (event.deltaY < 0) {
      this.zoomInComparison(imageIndex);
    } else {
      this.zoomOutComparison(imageIndex);
    }
  }

  // Bulk actions for images
  selectAllImages(): void {
    // For now, just log the action - could be extended to support multi-select
    console.log('Select all images clicked - feature can be extended for batch operations');
    this.snackBar.open('Select all functionality can be implemented for batch operations', 'Close', { duration: 3000 });
  }

  downloadAllImages(): void {
    const images = this.getFilteredImages();
    if (images.length === 0) {
      this.snackBar.open('No images to download', 'Close', { duration: 3000 });
      return;
    }

    console.log(`Downloading ${images.length} images`);
    images.forEach((image, index) => {
      setTimeout(() => {
        this.downloadImage(image);
      }, index * 500); // Delay downloads to avoid browser blocking
    });

    this.snackBar.open(`Downloading ${images.length} images...`, 'Close', { duration: 3000 });
  }

  openFileInfoDialog = (attachment: ImageAttachment): void => {
    if (attachment.requestFileData) {
      this.dialog.open(FileInfoDialogComponent, {
        width: '600px',
        maxHeight: '80vh',
        data: { file: attachment.requestFileData }
      });
    } else {
      this.showSnackbar('File information not available');
    }
  }

  private handleScroll = (): void => {
    if (this.isComparisonMode && this.selectedImagesForComparison.length === 2) {
      const button = document.querySelector('.ready-to-compare-button') as HTMLElement;
      if (button) button.style.transform = 'translateZ(0)';
    }
  }

  startChatWithPatient = (): void => {
    if (this.patient?.id) {
      // Navigate to chat container without specific doctor/patient IDs
      // The chat container will handle starting a new chat with the patient
      this.router.navigate(['/chat'], {
        queryParams: {
          startNewChat: 'true',
          patientId: this.patient.id,
          patientName: this.patient.fullName || 'Patient'
        }
      });
    }
  }


}
