import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule, Router } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AppointmentMessage, AppointmentMessageServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { AuthService } from '../../../../shared/services/auth.service';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { DateTime } from 'luxon';
import { ConfirmationDialogComponent } from '../../user/user-request/confirmation-dialog/confirmation-dialog.component';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-appointment-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './appointment-list.component.html',
  styleUrl: './appointment-list.component.css',
})
export class AppointmentListComponent implements OnInit, OnDestroy {
  baseUrl = getRemoteServiceBaseUrl();
  appointments: AppointmentMessage[] = [];
  filteredAppointments: AppointmentMessage[] = [];
  isLoading = true;
  searchTerm = '';
  selectedStatus = 'All';
  isAdmin = false;
  isDoctor = false;
  isDeleting: string | null = null;
  currentUser: any;
  currentPage = 1;
  pageSize = 10;
  totalPages = 1;
  statusOptions = ['All', 'Pending', 'Doctor Assigned', 'Reviewing', 'Confirmed', 'Completed', 'Cancelled', 'Rescheduled'];
  private destroy$ = new Subject<void>();

  constructor(
    private appointmentService: AppointmentMessageServiceProxy,
    private router: Router,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getUser();
    this.isAdmin = this.authService.hasRole('Admin');
    this.isDoctor = this.authService.hasRole('Doctor');
    this.loadAppointments();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private showSnackbar = (message: string, isError = false): void => {
    this.snackBar.open(message, 'Close', { duration: 3000, panelClass: isError ? 'error-snackbar' : 'success-snackbar' });
  }

  loadAppointments = (): void => {
    this.isLoading = true;
    this.appointmentService.getAllAppointments().pipe(takeUntil(this.destroy$)).subscribe({
      next: (data) => {
        this.appointments = data || [];
        this.applyFilters();
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
        this.showSnackbar('Error loading appointments', true);
      }
    });
  }

  private applyFilters = (): void => {
    const term = this.searchTerm.toLowerCase();
    const userEmail = this.currentUser?.['email'] || this.currentUser?.['unique_name'];

    this.filteredAppointments = this.appointments.filter(appointment =>
      (this.selectedStatus === 'All' || appointment.status === this.selectedStatus) &&
      (!this.searchTerm.trim() ||
        appointment.patientEmail?.toLowerCase().includes(term) ||
        appointment.message?.toLowerCase().includes(term) ||
        appointment.status?.toLowerCase().includes(term)) &&
      (this.isAdmin || this.isDoctor || appointment.patientEmail === userEmail)
    );
    this.updatePagination();
  }

  private updatePagination = (): void => {
    this.totalPages = Math.ceil(this.filteredAppointments.length / this.pageSize);
    if (this.currentPage > this.totalPages) this.currentPage = 1;
  }

  get paginatedAppointments(): AppointmentMessage[] {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    return this.filteredAppointments.slice(startIndex, startIndex + this.pageSize);
  }

  onSearchChange = (): void => { this.currentPage = 1; this.applyFilters(); }
  onStatusChange = (): void => { this.currentPage = 1; this.applyFilters(); }
  previousPage = (): void => { if (this.currentPage > 1) this.currentPage--; }
  nextPage = (): void => { if (this.currentPage < this.totalPages) this.currentPage++; }
  goToPage = (page: number): void => { this.currentPage = page; }

  get pageNumbers(): number[] {
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);
    return Array.from({length: end - start + 1}, (_, i) => start + i);
  }

  viewAppointment = (id: string): void => { this.router.navigate(['/appointments/detail', id]); };
  editAppointment = (id: string): void => { this.router.navigate(['/appointments/edit', id]); };

  deleteAppointment = (appointment: AppointmentMessage): void => {
    this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Appointment', type: 'danger',
        message: 'Are you sure you want to delete this Appointment?',
        itemName: appointment.patientEmail, confirmText: 'Delete', cancelText: 'Cancel'
      }
    }).afterClosed().subscribe(result => {
      if (result) this.performDelete(appointment.id);
    });
  }

  private performDelete = (id: string): void => {
    this.isDeleting = id;
    this.appointmentService.deleteAppointment(id).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.isDeleting = null;
        this.showSnackbar('Appointment deleted successfully');
        this.loadAppointments();
      },
      error: () => {
        this.isDeleting = null;
        this.showSnackbar('Error deleting appointment', true);
      }
    });
  }

  // addNewAppointment(): void {
  //   this.router.navigate(['/appointments/add']);
  // }

  getStatusColor = (status: string | undefined): string => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-200 dark:border-yellow-800',
      'doctor assigned': 'bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900 dark:text-indigo-200 dark:border-indigo-800',
      reviewing: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900 dark:text-purple-200 dark:border-purple-800',
      confirmed: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-800',
      completed: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-800',
      cancelled: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-200 dark:border-red-800',
      rescheduled: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900 dark:text-orange-200 dark:border-orange-800'
    };
    return colors[status?.toLowerCase() as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900 dark:text-gray-200 dark:border-gray-800';
  }

  formatDateTime = (dateTime: DateTime | undefined): string =>
    dateTime ? dateTime.toFormat('MMM dd, yyyy • h:mm a') : 'Not scheduled';

  getImageUrl = (imageUrl: string | undefined): string =>
    !imageUrl ? '' : imageUrl.startsWith('http') ? imageUrl : `${this.baseUrl}/api/File/Getfile/${imageUrl}`;

  updateStatus = (appointment: AppointmentMessage, newStatus: string): void => {
    const updatedAppointment = new AppointmentMessage();
    Object.assign(updatedAppointment, appointment);
    updatedAppointment.status = newStatus;

    this.appointmentService.addOrUpdateAppointment(updatedAppointment)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showSnackbar('Status updated successfully');
          this.loadAppointments();
        },
        error: () => this.showSnackbar('Error updating status', true)
      });
  }

  onStatusUpdate = (appointment: AppointmentMessage, event: Event): void => {
    const target = event.target as HTMLSelectElement;
    if (target?.value) this.updateStatus(appointment, target.value);
  }

  getMaxItems = (): number => Math.min(this.currentPage * this.pageSize, this.filteredAppointments.length);
  getPendingCount = (): number => this.appointments.filter(a => a.status?.toLowerCase() === 'pending').length;
  getConfirmedCount = (): number => this.appointments.filter(a => a.status?.toLowerCase() === 'confirmed').length;

  getTodayCount = (): number => {
    const today = DateTime.now().startOf('day');
    const tomorrow = today.plus({ days: 1 });
    return this.appointments.filter(a => a.scheduledTime && a.scheduledTime >= today && a.scheduledTime < tomorrow).length;
  }

  getRelativeTime = (dateTime: DateTime | undefined): string => {
    if (!dateTime) return '';
    const diff = dateTime.diff(DateTime.now());
    return diff.as('milliseconds') < 0 ? dateTime.toRelative() || 'Past' : dateTime.toRelative() || 'Future';
  }

  onPageSizeChange = (): void => { this.currentPage = 1; this.updatePagination(); }

  get paginationRange(): number[] {
    const start = Math.max(1, this.currentPage - 2);
    const end = Math.min(this.totalPages, this.currentPage + 2);
    return Array.from({length: end - start + 1}, (_, i) => start + i);
  }

  getMessagePreview = (message: string | undefined): string =>
    !message ? 'No message provided' : message.length > 50 ? message.substring(0, 50) + '...' : message;
}
