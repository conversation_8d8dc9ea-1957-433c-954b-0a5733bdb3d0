import { Injectable, Inject } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import {
    DoctorPatientChatServiceProxy,
    StartVideoCallRequestDto,
    JoinVideoCallRequestDto,
    EndVideoCallRequestDto,
    VideoCallSessionDto,
    VideoCallTokenDto,
    GetVideoCallHistoryRequestDto,
    VideoCallHistoryResponseDto
} from '../../shared/service-proxies/service-proxies';

@Injectable({
    providedIn: 'root'
})
export class VideoCallService {
    // State management
    private currentCallSubject = new BehaviorSubject<VideoCallSessionDto | null>(null);
    public currentCall$ = this.currentCallSubject.asObservable();

    private callStateSubject = new BehaviorSubject<string>('idle'); // idle, connecting, active, ended
    public callState$ = this.callStateSubject.asObservable();

    constructor(@Inject(DoctorPatientChatServiceProxy) private doctorPatientChatService: DoctorPatientChatServiceProxy) { }

    /**
     * Start a new video call between doctor and patient
     */
    async startVideoCall(request: StartVideoCallRequestDto): Promise<VideoCallSessionDto> {
        try {
            this.callStateSubject.next('connecting');

            const response = await this.doctorPatientChatService.startVideoCall(request).toPromise();

            if (response) {
                this.currentCallSubject.next(response);
                this.callStateSubject.next('active');
                return response;
            }

            throw new Error('Failed to start video call');
        } catch (error) {
            this.callStateSubject.next('idle');
            console.error('Error starting video call:', error);
            throw error;
        }
    }

    /**
     * Join an existing video call
     */
    async joinVideoCall(request: JoinVideoCallRequestDto): Promise<VideoCallSessionDto> {
        try {
            this.callStateSubject.next('connecting');

            const response = await this.doctorPatientChatService.joinVideoCall(request).toPromise();

            if (response) {
                this.currentCallSubject.next(response);
                this.callStateSubject.next('active');
                return response;
            }

            throw new Error('Failed to join video call');
        } catch (error) {
            this.callStateSubject.next('idle');
            console.error('Error joining video call:', error);
            throw error;
        }
    }

    /**
     * End a video call
     */
    async endVideoCall(request: EndVideoCallRequestDto): Promise<boolean> {
        try {
            const response = await this.doctorPatientChatService.endVideoCall(request).toPromise();

            this.currentCallSubject.next(null);
            this.callStateSubject.next('ended');

            // Reset to idle after a short delay
            setTimeout(() => {
                this.callStateSubject.next('idle');
            }, 2000);

            return response || false;
        } catch (error) {
            console.error('Error ending video call:', error);
            throw error;
        }
    }

    /**
     * Get ACS token for video calling
     */
    async getVideoCallToken(callId: string, displayName: string = ''): Promise<VideoCallTokenDto> {
        try {
            const response = await this.doctorPatientChatService.getVideoCallToken(callId, displayName).toPromise();

            if (response) {
                return response;
            }

            throw new Error('Failed to get video call token');
        } catch (error) {
            console.error('Error getting video call token:', error);
            throw error;
        }
    }

    /**
     * Get video call session details
     */
    async getVideoCallSession(callId: string): Promise<VideoCallSessionDto | null> {
        try {
            const response = await this.doctorPatientChatService.getVideoCallSession(callId).toPromise();

            return response || null;
        } catch (error) {
            console.error('Error getting video call session:', error);
            return null;
        }
    }

    /**
     * Get video call history
     */
    async getVideoCallHistory(request: GetVideoCallHistoryRequestDto): Promise<VideoCallHistoryResponseDto> {
        try {
            const response = await this.doctorPatientChatService.getVideoCallHistory(request).toPromise();

            return response || new VideoCallHistoryResponseDto({ calls: [], totalCount: 0, pageNumber: 1, pageSize: 20 });
        } catch (error) {
            console.error('Error getting video call history:', error);
            throw error;
        }
    }

    /**
     * Get active video calls for current user
     */
    async getActiveVideoCalls(): Promise<VideoCallSessionDto[]> {
        try {
            const response = await this.doctorPatientChatService.getActiveVideoCalls().toPromise();

            return response || [];
        } catch (error) {
            console.error('Error getting active video calls:', error);
            return [];
        }
    }

    /**
     * Get current call state
     */
    getCurrentCall(): VideoCallSessionDto | null {
        return this.currentCallSubject.value;
    }

    /**
     * Get current call state string
     */
    getCurrentCallState(): string {
        return this.callStateSubject.value;
    }

    /**
     * Check if user is currently in a call
     */
    isInCall(): boolean {
        return this.currentCallSubject.value !== null && this.callStateSubject.value === 'active';
    }

    /**
     * Clear current call state (for cleanup)
     */
    clearCallState(): void {
        this.currentCallSubject.next(null);
        this.callStateSubject.next('idle');
    }

    /**
     * Observable for call state changes
     */
    getCallStateChanges(): Observable<string> {
        return this.callState$;
    }

    /**
     * Observable for current call changes
     */
    getCurrentCallChanges(): Observable<VideoCallSessionDto | null> {
        return this.currentCall$;
    }

    /**
     * Reset call state to idle (for cleanup between calls)
     */
    resetCallState(): void {
        console.log('🔄 Resetting video call service state');
        this.currentCallSubject.next(null);
        this.callStateSubject.next('idle');
    }
}