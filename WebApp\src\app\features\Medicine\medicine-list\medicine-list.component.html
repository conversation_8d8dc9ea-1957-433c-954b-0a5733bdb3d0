<div class=" p-5 bg-white dark:bg-gray-900 transition-colors duration-300">
  <!-- <PERSON> Header -->
  <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-5">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Medicine Inventory</h1>
    <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
      Manage your medicine inventory, stock levels, and track expiring items
    </p>
  </div>

  <!-- Stats Cards Section -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
    <!-- Total Medicines Card -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center w-14 h-14">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Total Medicines</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-blue-800 dark:text-blue-300">{{medicines.length || 0}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">items</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Low Stock Card -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center w-14 h-14">
            <svg class="w-6 h-6 text-amber-600 dark:text-amber-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Low Stock</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-amber-800 dark:text-amber-300">{{getLowStockCount()}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">items</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Expiring Soon Card -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center w-14 h-14">
            <svg class="w-6 h-6 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Expiring Soon</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-red-800 dark:text-red-300">{{getExpiringSoonCount()}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">items</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Expired Card -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-200 dark:bg-red-800 flex items-center justify-center w-14 h-14">
            <svg class="w-6 h-6 text-red-700 dark:text-red-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018a2 2 0 01.485.06l3.76.7m-7 10v5a2 2 0 002 2h.096c.5 0 .905-.405.905-.904 0-.715.211-1.413.608-2.008L17.25 15M10 14l-1.743-3.487c-.225-.45-.335-.933-.335-1.436 0-.745.349-1.448.944-1.861L10 6m0 8h5.172a2 2 0 001.414-.586l2.828-2.828A2 2 0 0020 9.172V7a2 2 0 00-2-2h-3" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Expired</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-red-900 dark:text-red-200">{{getExpiredCount()}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">items</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Categories Card -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center w-14 h-14">
            <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Categories</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-green-800 dark:text-green-300">{{getUniqueCategories()}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-300">total</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow transition-colors duration-300">
    <!-- Action Bar -->
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <div class="flex flex-col lg:flex-row justify-between gap-4">
        <!-- Search and Filters -->
        <div class="flex flex-col sm:flex-row gap-4 flex-1">
          <!-- Search Input -->
          <div class="flex-1">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                  fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <input type="text" id="search" (keyup)="applyFilter($event)" placeholder="Search medicines..." #searchInput
                class="block w-full outline-none pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100 sm:text-sm transition-shadow"
                aria-label="Search medicines">
              <button *ngIf="searchQuery" (click)="clearSearch(); searchInput.value = ''"
                class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                aria-label="Clear search">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
          <!-- Category Filter -->
          <div class="sm:w-48">
            <div class="relative">
              <select [(ngModel)]="selectedCategory" (change)="filterByCategory($event)"
                class="block w-full pl-3 pr-12 py-2 text-base border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100 sm:text-sm rounded-lg appearance-none bg-white"
                aria-label="Filter by category">
                <option value="">All Categories</option>
                <option *ngFor="let category of categories" [value]="category">{{category}}</option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>
          </div>
        </div> <!-- Action Buttons -->
        <div class="flex items-center gap-3">
          <button
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900"
            (click)="exportData()">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          </button>
          <button (click)="addNewMedicine()"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd" />
            </svg>
            Add Medicine
          </button>
        </div>
      </div>
    </div>
    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex justify-center items-center p-12">
      <div class="flex flex-col items-center">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 dark:border-blue-400 border-t-transparent"></div>
        <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">Loading medicines...</p>
      </div>
    </div>
    <!-- Error State -->
    <div *ngIf="!isLoading && errorMessage" class="p-12">
      <div class="text-center p-6 bg-red-50 dark:bg-red-900/20 rounded-lg max-w-lg mx-auto">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-red-400 dark:text-red-300" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-red-800 dark:text-red-200">Error Loading Data</h3>
        <p class="mt-1 text-sm text-red-600 dark:text-red-300">{{ errorMessage }}</p>
        <div class="mt-4">
          <button (click)="loadData()"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 dark:text-red-200 bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900">
            Try Again
          </button>
        </div>
      </div>
    </div>
    <!-- Table Content -->
    <div *ngIf="!isLoading && !errorMessage && displayData.length > 0" class="overflow-x-auto items-table">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-900">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Medicine
            </th>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              (click)="sortData('category')">
              Category
              <span *ngIf="sortColumn === 'category'" class="ml-1 inline-block">
                <span *ngIf="sortDirection">▲</span>
                <span *ngIf="!sortDirection">▼</span>
              </span>
            </th>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              (click)="sortData('expiryDate')">
              Expiry Date
              <span *ngIf="sortColumn === 'expiryDate'" class="ml-1 inline-block">
                <span *ngIf="sortDirection">▲</span>
                <span *ngIf="!sortDirection">▼</span>
              </span>
            </th>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              (click)="sortData('stockQuantity')">
              Stock
              <span *ngIf="sortColumn === 'stockQuantity'" class="ml-1 inline-block">
                <span *ngIf="sortDirection">▲</span>
                <span *ngIf="!sortDirection">▼</span>
              </span>
            </th>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
              (click)="sortData('price')">
              Price
              <span *ngIf="sortColumn === 'price'" class="ml-1 inline-block">
                <span *ngIf="sortDirection">▲</span>
                <span *ngIf="!sortDirection">▼</span>
              </span>
            </th>
            <th scope="col"
              class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-24">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <tr *ngFor="let medicine of displayData">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="h-10 w-10 flex-shrink-0 relative">
                  <!-- Loading indicator -->
                  <div *ngIf="medicine.imageUrl"
                    class="image-loading absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-900 rounded-lg">
                    <svg class="animate-spin h-4 w-4 text-blue-500 dark:text-blue-300" xmlns="http://www.w3.org/2000/svg" fill="none"
                      viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                      </path>
                    </svg>
                  </div>
                  <!-- Medicine image with error handling -->
                  <img *ngIf="medicine.imageUrl"
                       [src]="medicine.imageUrl"
                       class="h-10 w-10 rounded-lg object-cover"
                       [alt]="medicine.name || 'Medicine'"
                       (load)="onImageLoad($event)"
                       (error)="onImageError($event)"
                       loading="lazy">
                  <!-- Fallback for missing image -->
                  <div *ngIf="!medicine.imageUrl"
                    class="h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
                    <svg class="h-6 w-6 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{medicine.name}}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400" *ngIf="medicine.description">{{medicine.description.slice(0,20)}}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 rounded-full"
                [ngClass]="{
                  'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300': medicine.category === 'Pain Relief',
                  'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': medicine.category === 'Antibiotics',
                  'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300': medicine.category === 'Vitamins',
                  'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300': medicine.category === 'Cold & Flu',
                  'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300': medicine.category === 'Digestive Health',
                  'bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300': medicine.category === 'First Aid',
                  'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300': medicine.category === 'Allergy',
                  'bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-300': medicine.category === 'Skin Care',
                  'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300': medicine.category === 'Others',
                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300': medicine.category === 'Emergency',
                  'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300': medicine.category === 'Topical'
                }">
                {{medicine.category}}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div [ngClass]="getExpiryDateClasses(medicine.expiryDate!)">
                {{getFormattedDate(medicine.expiryDate) | date:'mediumDate'}}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <span [ngClass]="getStockLevelClasses(medicine.stockQuantity)">
                  {{medicine.stockQuantity}}
                </span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
              {{medicine.price | currency}}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <button (click)="editMedicine(medicine)"
                class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4 focus:outline-none focus:underline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                  </path>
                </svg>
              </button>
              <button (click)="deleteMedicine(medicine)"
                class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 focus:outline-none focus:underline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                  </path>
                </svg>
              </button>
            </td>
          </tr>
        </tbody>
      </table> <!-- No Results -->
      <div *ngIf="displayData.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
            d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No medicines found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Try adjusting your search criteria or add a new medicine.
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Pagination Controls -->
<div class="pagination-container bg-gray-50 dark:bg-gray-900 px-6 py-4 border-t border-gray-200 dark:border-gray-700 transition-colors duration-300">
  <div class="flex flex-col sm:flex-row items-center justify-between">
    <!-- Results Info -->
    <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
      Showing <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> to
      <span class="font-medium">{{ currentPage * pageSize > filteredData.length ? filteredData.length : currentPage * pageSize }}</span> of
      <span class="font-medium">{{ filteredData.length }}</span> medicines
    </div>
    <!-- Pagination Navigation -->
    <div class="flex items-center space-x-2">
      <!-- Page Size Selector -->
      <div class="flex items-center mr-3">
        <label for="page-size" class="text-sm text-gray-600 dark:text-gray-400 mr-2">Show:</label>
        <select id="page-size" [(ngModel)]="pageSize" (change)="onPageSizeChange($event)"
          class="page-size-select border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 dark:text-gray-100">
          <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }}</option>
        </select>
      </div>
      <!-- Previous Button -->
      <button (click)="previousPage()" [disabled]="currentPage === 1" class="pagination-button bg-white dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600"
        [ngClass]="{'pagination-button-disabled': currentPage === 1}">
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      <!-- First Page Button (if not in view) -->
      <button *ngIf="paginationRange[0] > 1" (click)="goToPage(1)" class="pagination-button-number bg-white dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600">
        1
      </button>
      <!-- Ellipsis (if needed) -->
      <span *ngIf="paginationRange[0] > 2" class="pagination-ellipsis text-gray-600 dark:text-gray-400">...</span>
      <!-- Page Numbers -->
      <button *ngFor="let page of paginationRange" (click)="goToPage(page)" class="pagination-button-number bg-white dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600" [ngClass]="{'pagination-button-active': page === currentPage,
                   'first-page': page === 1,
                   'last-page': page === totalPages}">
        {{ page }}
      </button>
      <!-- Ellipsis (if needed) -->
      <span *ngIf="paginationRange[paginationRange.length - 1] < totalPages - 1" class="pagination-ellipsis text-gray-600 dark:text-gray-400">...</span>
      <!-- Last Page Button (if not in view) -->
      <button *ngIf="paginationRange[paginationRange.length - 1] < totalPages" (click)="goToPage(totalPages)"
        class="pagination-button-number bg-white dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600">
        {{ totalPages }}
      </button>
      <!-- Next Button -->
      <button (click)="nextPage()" [disabled]="currentPage === totalPages" class="pagination-button bg-white dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600"
        [ngClass]="{'pagination-button-disabled': currentPage === totalPages}">
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>
  </div>
</div>
