import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule, Router } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { Educational, EducationalServiceProxy, MedicineServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { AuthService } from '../../../../shared/services/auth.service';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { EditorJsService } from '../../../shared/services/editor-js.service';
import { ConfirmationDialogComponent } from '../../user/user-request/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-educational-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  templateUrl: './educational-list.component.html',
  styleUrl: './educational-list.component.css'
})
export class EducationalListComponent implements OnInit {
  // Base URL for API calls
  baseUrl: string = getRemoteServiceBaseUrl();

  educationalItems: Educational[] = [];
  filteredItems: Educational[] = [];
  isLoading: boolean = true;
  searchTerm: string = '';
  selectedCategory: string = 'All';
  isAdmin: boolean = false;
  isDeleting: string | null = null;


  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 6;
  pageSizeOptions: number[] = [6, 12, 24, 48];
  totalItems: number = 0;
  totalPages: number = 1;
  paginationRange: number[] = [];
  displayedItems: Educational[] = [];
  categoryOptions: string[] | undefined;

  constructor(
    private educationalService: EducationalServiceProxy,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar,
    private editorJsService: EditorJsService,
    private dialog: MatDialog,
    private medicineService: MedicineServiceProxy
  ) { }

  ngOnInit(): void {
    this.isAdmin = this.authService.hasRole('Admin');
    this.loadEducationalItems();
    this.loadCategory();
  }

  loadCategory() {
    this.medicineService.categories().subscribe((res) => {
      if (res && res.length > 0) {
        this.categoryOptions = ['All', ...res];
      }
    })
  }

  loadEducationalItems(): void {
    this.isLoading = true;
    this.educationalService.getAllEducational().subscribe({
      next: (result) => {
        if (Array.isArray(result)) {
          this.educationalItems = result;
        } else {
          this.educationalItems = [];
        }
        this.filterItems();
        this.isLoading = false;
      },
      error: (error) => {
        this.handleError(error, 'Error loading educational content');
        this.isLoading = false;
      }
    });
  }

  filterItems(): void {
    this.filteredItems = this.educationalItems.filter(item => {
      // Category filter
      const categoryMatch = this.selectedCategory === 'All' ||
        item.category === this.selectedCategory;

      // Search filter
      const searchMatch = this.searchTerm.trim() === '' ||
        (item.content && item.content.toLowerCase().includes(this.searchTerm.toLowerCase())) ||
        (item.category && item.category.toLowerCase().includes(this.searchTerm.toLowerCase()));

      return categoryMatch && searchMatch;
    });

    this.updatePagination();
  }

  // Update pagination with filter results
  updatePagination() {
    this.totalItems = this.filteredItems.length;
    this.totalPages = Math.ceil(this.totalItems / this.pageSize) || 1; // Ensure at least 1 page

    // Make sure currentPage is within valid range
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }

    // Calculate pagination range
    this.calculatePaginationRange();

    // Update displayed data
    this.updateDisplayedItems();
  }

  // Calculate the range of page numbers to display
  calculatePaginationRange() {
    const maxPagesToShow = 5;
    const halfMax = Math.floor(maxPagesToShow / 2);

    let start = this.currentPage - halfMax;
    let end = this.currentPage + halfMax;

    if (start < 1) {
      start = 1;
      end = Math.min(start + maxPagesToShow - 1, this.totalPages);
    }

    if (end > this.totalPages) {
      end = this.totalPages;
      start = Math.max(end - maxPagesToShow + 1, 1);
    }

    this.paginationRange = [];
    for (let i = start; i <= end; i++) {
      this.paginationRange.push(i);
    }
  }

  // Update displayed items based on current page
  updateDisplayedItems() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.totalItems);

    this.displayedItems = this.filteredItems.slice(startIndex, endIndex);
  }

  onSearchChange(): void {
    this.currentPage = 1; // Reset to first page
    this.filterItems();
  }

  onCategoryChange(): void {
    this.currentPage = 1; // Reset to first page
    this.filterItems();
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.updateDisplayedItems();
    }
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.goToPage(this.currentPage - 1);
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.goToPage(this.currentPage + 1);
    }
  }

  onPageSizeChange(event: Event) {
    const newSize = parseInt((event.target as HTMLSelectElement).value, 10);
    this.pageSize = newSize;
    this.currentPage = 1; // Reset to first page when changing page size
    this.updatePagination();
  }

  viewEducationalItem(id: string): void {
    this.router.navigate(['/educational/detail', id]);
  }

  editEducationalItem(id: string): void {
    this.router.navigate(['/educational/edit', id]);
  }

  // Add a network error handler
  handleError(error: any, defaultMessage: string): void {
    console.error('Error:', error);
    let message = defaultMessage;

    if (error && error.message) {
      message = error.message;
    } else if (error && error.error && error.error.message) {
      message = error.error.message;
    } else if (typeof error === 'string') {
      message = error;
    }

    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'end',
      verticalPosition: 'bottom',
      panelClass: ['error-snackbar']
    });
  }

  /**
   * Handle image load errors by applying a CSS class and removing the image
   */
  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    // Hide the image and add a class to its parent for styling
    img.style.display = 'none';
    img.parentElement?.classList.add('image-error');

    // Create and append placeholder content
    const placeholder = document.createElement('div');
    placeholder.className = 'w-full h-full bg-gray-100 flex items-center justify-center';
    placeholder.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
    `;
    img.parentElement?.appendChild(placeholder);
  }

  deleteEducationalItem(educational: Educational): void {
    if (!educational || !educational.id) {
      console.error('Invalid educational item');
      return;
    }

    const title = this.getContentTitle(educational.content);
    const itemName = title.length > 50 ? title.substring(0, 50) + '...' : title;

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Educational Content',
        message: 'Are you sure you want to delete this educational content? This action cannot be undone.',
        itemName: itemName,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.isDeleting = educational.id;
        this.educationalService.deleteEducational(educational.id).subscribe({
          next: () => {
            this.educationalItems = this.educationalItems.filter(item => item.id !== educational.id);
            this.filterItems();
            this.isDeleting = null;
            this.snackBar.open('Educational content deleted successfully', 'Close', { duration: 3000 });
          },
          error: (error) => {
            this.isDeleting = null;
            this.handleError(error, 'Error deleting educational content');
          }
        });
      }
    });
  }

  addNewEducationalItem(): void {
    this.router.navigate(['/educational/add']);
  }

  /**
   * Strip HTML tags from text content
   */
  private stripHtmlTags(text: string): string {
    if (!text) return '';

    // Remove HTML tags using regex
    return text.replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .trim();
  }

  /**
   * Parse EditorJS content and extract plain text for preview
   */
  parseEditorJSContent(content: string | undefined): string {
    if (!content) return '';

    try {
      // Try to parse as EditorJS JSON
      const editorData = JSON.parse(content);

      if (editorData && editorData.blocks && Array.isArray(editorData.blocks)) {
        // Extract text from all blocks
        const textContent = editorData.blocks
          .map((block: any) => {
            switch (block.type) {
              case 'header':
                return this.stripHtmlTags(block.data.text || '');
              case 'paragraph':
                return this.stripHtmlTags(block.data.text || '');
              case 'list':
                return block.data.items ?
                  block.data.items.map((item: any) => {
                    const text = typeof item === 'string' ? item : (item.text || item.content || '');
                    return this.stripHtmlTags(text);
                  }).join(' ') : '';
              case 'checklist':
              case 'checkpoint':
                return block.data.items ?
                  block.data.items.map((item: any) => {
                    const text = typeof item === 'string' ? item : (item.text || item.content || '');
                    return this.stripHtmlTags(text);
                  }).join(' ') : '';
              default:
                return '';
            }
          })
          .filter((text: string) => text.length > 0)
          .join(' ');

        return textContent;
      }
    } catch (error) {
      // If not valid JSON, return as plain text (strip HTML just in case)
      return this.stripHtmlTags(content);
    }

    return this.stripHtmlTags(content);
  }

  /**
   * Get title from EditorJS content (first header or first paragraph)
   */
  getContentTitle(content: string | undefined): string {
    if (!content) return 'Untitled';

    try {
      const editorData = JSON.parse(content);

      if (editorData && editorData.blocks && Array.isArray(editorData.blocks)) {
        // Look for the first header block
        const headerBlock = editorData.blocks.find((block: any) => block.type === 'header');
        if (headerBlock && headerBlock.data.text) {
          const cleanTitle = this.stripHtmlTags(headerBlock.data.text);
          return cleanTitle.length > 60 ? cleanTitle.substring(0, 60) + '...' : cleanTitle;
        }

        // If no header, use the first paragraph
        const paragraphBlock = editorData.blocks.find((block: any) => block.type === 'paragraph');
        if (paragraphBlock && paragraphBlock.data.text) {
          const cleanText = this.stripHtmlTags(paragraphBlock.data.text);
          return cleanText.substring(0, 60) + (cleanText.length > 60 ? '...' : '');
        }
      }
    } catch (error) {
      // If not valid JSON, use the content as is (strip HTML)
      const cleanContent = this.stripHtmlTags(content);
      return cleanContent.substring(0, 60) + (cleanContent.length > 60 ? '...' : '');
    }

    return 'Untitled';
  }

  /**
   * Get preview text from EditorJS content (excluding the title)
   */
  getContentPreview(content: string | undefined): string {
    if (!content) return '';

    try {
      const editorData = JSON.parse(content);

      if (editorData && editorData.blocks && Array.isArray(editorData.blocks)) {
        // Skip the first header and get content from other blocks
        let foundFirstHeader = false;
        const previewText = editorData.blocks
          .filter((block: any) => {
            if (block.type === 'header' && !foundFirstHeader) {
              foundFirstHeader = true;
              return false; // Skip the first header
            }
            return block.type === 'paragraph' || block.type === 'list' || block.type === 'checklist' || block.type === 'checkpoint';
          })
          .map((block: any) => {
            switch (block.type) {
              case 'paragraph':
                return this.stripHtmlTags(block.data.text || '');
              case 'list':
                return block.data.items ?
                  block.data.items.map((item: any) => {
                    const text = typeof item === 'string' ? item : (item.text || item.content || '');
                    return this.stripHtmlTags(text);
                  }).join(' ') : '';
              case 'checklist':
              case 'checkpoint':
                return block.data.items ?
                  block.data.items.map((item: any) => {
                    const text = typeof item === 'string' ? item : (item.text || item.content || '');
                    return this.stripHtmlTags(text);
                  }).join(' ') : '';
              default:
                return '';
            }
          })
          .filter((text: string) => text.length > 0)
          .join(' ');

        return previewText;
      }
    } catch (error) {
      // If not valid JSON, return as plain text (strip HTML)
      return this.stripHtmlTags(content);
    }

    return this.stripHtmlTags(content);
  }

  truncateContent(content: string | undefined, maxLength: number = 150): string {
    if (!content) return '';

    // Parse EditorJS content first
    const plainText = this.parseEditorJSContent(content);

    return plainText.length > maxLength ? plainText.substring(0, maxLength) + '...' : plainText;
  }

  formatDate(date: any): string {
    if (!date) return 'N/A';
    try {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return 'Invalid Date';
    }
  }

  // Role-based access control methods
  canManageEducationalContent(): boolean {
    return this.authService.isAdmin() || this.authService.hasDoctorRole();
  }
}
