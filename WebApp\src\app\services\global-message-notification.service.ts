import { Injectable } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { BehaviorSubject, Subject, Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { ChatMessageResponseDto } from '../../shared/service-proxies/service-proxies';
import { MessageNotification } from './patient-chat.service';

export interface UnreadCounts {
  [threadId: string]: number; // threadId format: "doctorId-patientId"
}

export interface ToastNotificationData {
  senderName: string;
  messagePreview: string;
  threadId: string;
  doctorId: string;
  patientId: string;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class GlobalMessageNotificationService {
  // State management
  private unreadCountsSubject = new BehaviorSubject<UnreadCounts>({});
  private totalUnreadCountSubject = new BehaviorSubject<number>(0);
  private toastNotificationSubject = new Subject<ToastNotificationData>();

  // Current route tracking
  private currentRoute = '';
  private currentChatThreadId: string | null = null; // Format: "doctorId-patientId"

  // Public observables
  public unreadCounts$ = this.unreadCountsSubject.asObservable();
  public totalUnreadCount$ = this.totalUnreadCountSubject.asObservable();
  public toastNotification$ = this.toastNotificationSubject.asObservable();

  constructor(private router: Router) {
    this.initializeRouteTracking();
  }

  /**
   * Initialize route tracking to detect current chat viewing
   */
  private initializeRouteTracking(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        map(event => (event as NavigationEnd).url)
      )
      .subscribe(url => {
        this.currentRoute = url;
        this.updateCurrentChatThreadId(url);
        console.log('🔍 Route changed:', url, 'Current chat thread:', this.currentChatThreadId);
      });
  }

  /**
   * Extract current chat thread ID from route
   */
  private updateCurrentChatThreadId(url: string): void {
    // Match pattern: /chat/doctorId/patientId
    const chatRouteMatch = url.match(/^\/chat\/([^\/]+)\/([^\/]+)(?:\/.*)?$/);

    if (chatRouteMatch) {
      const [, doctorId, patientId] = chatRouteMatch;
      this.currentChatThreadId = `${doctorId}-${patientId}`;
      console.log('🎯 Route detection: Currently viewing chat thread:', this.currentChatThreadId);
    } else {
      this.currentChatThreadId = null;
      console.log('🎯 Route detection: Not viewing any chat thread, current route:', url);
    }
  }

  /**
   * Check if user is currently viewing a specific chat
   */
  public isCurrentlyViewingChat(doctorId: string, patientId: string): boolean {
    const threadId = `${doctorId}-${patientId}`;
    return this.currentChatThreadId === threadId;
  }

  /**
   * Handle new message received via SignalR
   */
  public handleNewMessage(message: ChatMessageResponseDto): void {
    const threadId = `${message.doctorId}-${message.patientId}`;
    const isCurrentChat = this.isCurrentlyViewingChat(message.doctorId, message.patientId);
    const isFromCurrentUser = this.isMessageFromCurrentUser(message);

    console.log('📨 Handling new message:', {
      threadId,
      isCurrentChat,
      isFromCurrentUser,
      senderType: message.senderType,
      currentRoute: this.currentRoute,
      currentChatThreadId: this.currentChatThreadId,
      messagePreview: this.getMessagePreview(message.message)
    });

    // Only increment unread count if:
    // 1. Message is not from current user
    // 2. User is not currently viewing this chat
    if (!isFromCurrentUser && !isCurrentChat) {
      console.log('📈 Incrementing unread count for thread:', threadId);
      this.incrementUnreadCount(threadId);

      // Show toast notification
      this.showToastNotification({
        senderName: this.getSenderName(message),
        messagePreview: this.getMessagePreview(message.message),
        threadId,
        doctorId: message.doctorId,
        patientId: message.patientId,
        timestamp: message.createdDate ? new Date(message.createdDate.toString()) : new Date()
      });
    } else {
      console.log('🚫 Not incrementing unread count:', {
        reason: isFromCurrentUser ? 'Message from current user' : 'User viewing this chat',
        isFromCurrentUser,
        isCurrentChat
      });
    }
  }

  /**
   * Handle message notification from SignalR
   * Note: This method is kept for compatibility but doesn't trigger toast notifications
   * to avoid duplicates with handleNewMessage
   */
  public handleMessageNotification(notification: MessageNotification): void {
    const isCurrentChat = this.isCurrentlyViewingChat(
      notification.message.doctorId,
      notification.message.patientId
    );
    const isFromCurrentUser = this.isMessageFromCurrentUser(notification.message);

    console.log('🔔 Handling message notification (no toast - avoiding duplicates):', {
      threadId: notification.threadId,
      isCurrentChat,
      isFromCurrentUser,
      senderName: notification.senderName
    });

    // Do not show toast notification here to avoid duplicates with handleNewMessage
    // The handleNewMessage method already handles the toast notifications
  }

  /**
   * Handle messages marked as read
   */
  public handleMessagesMarkedAsRead(doctorId: string, patientId: string): void {
    const threadId = `${doctorId}-${patientId}`;
    this.clearUnreadCount(threadId);
    console.log('✅ Cleared unread count for thread:', threadId);
  }

  /**
   * Initialize unread counts from chat threads data
   */
  public initializeUnreadCounts(chatThreads: any[]): void {
    const counts: UnreadCounts = {};

    chatThreads.forEach(thread => {
      const threadId = `${thread.doctorId}-${thread.patientId}`;
      counts[threadId] = thread.unreadCount || 0;
    });

    this.unreadCountsSubject.next(counts);
    this.updateTotalUnreadCount();
    console.log('📊 Initialized unread counts:', counts);
  }

  /**
   * Get unread count for specific thread
   */
  public getUnreadCount(doctorId: string, patientId: string): number {
    const threadId = `${doctorId}-${patientId}`;
    const counts = this.unreadCountsSubject.value;
    return counts[threadId] || 0;
  }

  /**
   * Get total unread count across all threads
   */
  public getTotalUnreadCount(): number {
    return this.totalUnreadCountSubject.value;
  }

  /**
   * Increment unread count for a thread
   */
  private incrementUnreadCount(threadId: string): void {
    const currentCounts = { ...this.unreadCountsSubject.value };
    const oldCount = currentCounts[threadId] || 0;
    currentCounts[threadId] = oldCount + 1;

    console.log('📈 Incrementing unread count for thread:', {
      threadId,
      oldCount,
      newCount: currentCounts[threadId],
      allCounts: currentCounts
    });

    this.unreadCountsSubject.next(currentCounts);
    this.updateTotalUnreadCount();
  }

  /**
   * Clear unread count for a thread
   */
  private clearUnreadCount(threadId: string): void {
    const currentCounts = { ...this.unreadCountsSubject.value };
    currentCounts[threadId] = 0;

    this.unreadCountsSubject.next(currentCounts);
    this.updateTotalUnreadCount();

    console.log('🧹 Cleared unread count for thread:', threadId);
  }

  /**
   * Update total unread count
   */
  private updateTotalUnreadCount(): void {
    const counts = this.unreadCountsSubject.value;
    const total = Object.values(counts).reduce((sum, count) => sum + count, 0);
    this.totalUnreadCountSubject.next(total);
    console.log('🔢 Updated total unread count:', total);
  }

  /**
   * Show toast notification
   */
  private showToastNotification(data: ToastNotificationData): void {
    this.toastNotificationSubject.next(data);
    console.log('🍞 Showing toast notification:', data);
  }

  /**
   * Check if message is from current user
   */
  private isMessageFromCurrentUser(message: ChatMessageResponseDto): boolean {
    try {
      // Get current user info from JWT token
      const token = this.getTokenFromCookie();
      if (!token) {
        console.log('🚫 No token found, assuming message is not from current user');
        return false;
      }

      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentUserId = payload.sub || payload.unique_name || payload.nameid;
      const currentUserRole = payload.role || payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'];

      console.log('🔍 Checking if message is from current user:', {
        currentUserId,
        currentUserRole,
        messageSenderId: message.senderId,
        messageSenderType: message.senderType,
        messageDoctorId: message.doctorId,
        messagePatientId: message.patientId
      });

      // Check if message is from current user based on sender ID and role
      if (message.senderId && currentUserId) {
        const isFromCurrentUser = message.senderId === currentUserId;
        console.log('✅ Sender ID check:', { isFromCurrentUser, senderId: message.senderId, currentUserId });
        return isFromCurrentUser;
      }

      // Fallback: check based on role and IDs
      if (currentUserRole === 'Doctor' && message.senderType === 'Doctor') {
        const isFromCurrentUser = message.doctorId === currentUserId;
        console.log('✅ Doctor role check:', { isFromCurrentUser, doctorId: message.doctorId, currentUserId });
        return isFromCurrentUser;
      } else if (currentUserRole === 'Patient' && message.senderType === 'Patient') {
        const isFromCurrentUser = message.patientId === currentUserId;
        console.log('✅ Patient role check:', { isFromCurrentUser, patientId: message.patientId, currentUserId });
        return isFromCurrentUser;
      }

      console.log('🚫 No matching role/sender criteria, assuming message is not from current user');
      return false;
    } catch (error) {
      console.error('❌ Error checking if message is from current user:', error);
      return false;
    }
  }

  /**
   * Get JWT token from cookie
   */
  private getTokenFromCookie(): string | null {
    try {
      const TOKEN_KEY = 'jwt_token';
      const cookies = document.cookie.split(';');
      for (let cookie of cookies) {
        const [name, ...valueParts] = cookie.trim().split('=');
        if (name === TOKEN_KEY) {
          const value = valueParts.join('=');
          if (value && value.length > 0) {
            return value;
          }
        }
      }
      return null;
    } catch (error) {
      console.error('GlobalMessageNotificationService: Error reading token from cookies:', error);
      return null;
    }
  }

  /**
   * Get sender name from message
   */
  private getSenderName(message: ChatMessageResponseDto): string {
    if (message.senderType === 'Doctor') {
      return message.doctorName || 'Doctor';
    } else if (message.senderType === 'Patient') {
      return message.patientName || 'Patient';
    }
    return 'Unknown';
  }

  /**
   * Get message preview (truncated)
   */
  private getMessagePreview(message: string | undefined): string {
    if (!message) return 'New message';
    return message.length > 50 ? message.substring(0, 50) + '...' : message;
  }

  /**
   * Navigate to specific chat
   */
  public navigateToChat(doctorId: string, patientId: string): void {
    this.router.navigate(['/chat', doctorId, patientId]);
  }

  /**
   * Get current route
   */
  public getCurrentRoute(): string {
    return this.currentRoute;
  }

  /**
   * Get current chat thread ID
   */
  public getCurrentChatThreadId(): string | null {
    return this.currentChatThreadId;
  }

  /**
   * Reset all unread counts (useful for testing or logout)
   */
  public resetAllUnreadCounts(): void {
    this.unreadCountsSubject.next({});
    this.totalUnreadCountSubject.next(0);
    console.log('🔄 Reset all unread counts');
  }

  /**
   * Show a general notification toast (public method for components to use)
   */
  public showGeneralNotification(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    const senderName = type === 'error' ? 'System Error' : type === 'success' ? 'Success' : 'System';
    
    this.showToastNotification({
      senderName: senderName,
      messagePreview: message,
      threadId: `system-${type}`,
      doctorId: 'system',
      patientId: type,
      timestamp: new Date()
    });
  }
}
