import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { PatientChatService } from './patient-chat.service';

@Injectable({
  providedIn: 'root'
})
export class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;

  // Subjects for WebRTC events
  private remoteStreamSubject = new Subject<MediaStream>();
  private connectionStateSubject = new Subject<RTCPeerConnectionState>();

  // Public observables
  public remoteStream$ = this.remoteStreamSubject.asObservable();
  public connectionState$ = this.connectionStateSubject.asObservable();

  private currentCallId: string = '';
  private doctorId: string = '';
  private patientId: string = '';

  constructor(private chatService: PatientChatService) {
    // Setup listeners when service is ready
    this.chatService.connectionState$.subscribe(state => {
      if (state === 'Connected') {
        this.setupSignalRListeners();
      }
    });
  }

  private setupSignalRListeners(): void {
    console.log('🔗🔗🔗 SETTING UP SIGNALR LISTENERS FOR WEBRTC');
    const hubConnection = this.chatService.hubConnectionForWebRTC;
    if (!hubConnection) {
      console.warn('⚠️ SignalR hub connection not available for WebRTC');
      return;
    }
    console.log('✅ SignalR hub connection found for WebRTC');

    // Listen for WebRTC offers
    hubConnection.on('ReceiveOffer', async (data: any) => {
      console.log('📡 Received WebRTC offer:', data);
      console.log('🔍 Current call ID:', this.currentCallId);
      console.log('🔍 Received call ID:', data.callId);

      if (data.callId === this.currentCallId) {
        console.log('✅ Call IDs match, processing offer...');
        try {
          await this.handleOffer(data.offer);
          console.log('✅ Offer processed successfully');
        } catch (error) {
          console.error('❌ Error processing offer:', error);
        }
      } else {
        console.warn('⚠️ Call ID mismatch, ignoring offer');
      }
    });

    // Listen for WebRTC answers
    hubConnection.on('ReceiveAnswer', async (data: any) => {
      console.log('📡 Received WebRTC answer:', data);
      if (data.callId === this.currentCallId) {
        await this.handleAnswer(data.answer);
      }
    });

    // Listen for ICE candidates
    hubConnection.on('ReceiveIceCandidate', async (data: any) => {
      console.log('📡 Received ICE candidate:', data);
      if (data.callId === this.currentCallId) {
        await this.handleIceCandidate(data.candidate);
      }
    });

    console.log('🔗 WebRTC SignalR listeners setup complete');
  }

  async initializeCall(callId: string, doctorId: string, patientId: string, localStream: MediaStream): Promise<void> {
    console.log('🚀🚀🚀 WEBRTC SERVICE INITIALIZE CALL CALLED!', { callId, doctorId, patientId });
    this.currentCallId = callId;
    this.doctorId = doctorId;
    this.patientId = patientId;
    this.localStream = localStream;

    // Create peer connection
    this.peerConnection = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    });

    // Add local stream tracks
    localStream.getTracks().forEach(track => {
      this.peerConnection?.addTrack(track, localStream);
    });

    // Handle remote stream
    this.peerConnection.ontrack = (event) => {
      console.log('🎯🎯🎯 ONTRACK EVENT FIRED!', {
        streams: event.streams.length,
        tracks: event.track ? [event.track.kind, event.track.enabled, event.track.readyState] : 'no track',
        callId: this.currentCallId
      });

      if (event.streams && event.streams[0]) {
        console.log('📹📹📹 Remote stream received:', {
          streamId: event.streams[0].id,
          tracks: event.streams[0].getTracks().map(t => ({ kind: t.kind, enabled: t.enabled, readyState: t.readyState }))
        });
        this.remoteStream = event.streams[0];
        console.log('🚀 Emitting remote stream to subscribers...');
        this.remoteStreamSubject.next(this.remoteStream);
        console.log('✅ Remote stream emitted successfully');
      } else {
        console.warn('⚠️ Ontrack event fired but no streams available');
      }
    };

    // Handle ICE candidates
    this.peerConnection.onicecandidate = async (event) => {
      if (event.candidate) {
        console.log('🧊 ICE candidate generated:', {
          type: event.candidate.type,
          protocol: event.candidate.protocol,
          address: event.candidate.address
        });
        console.log('📤 Sending ICE candidate via SignalR');
        await this.sendIceCandidate(JSON.stringify(event.candidate));
      } else {
        console.log('🧊 ICE gathering complete (null candidate)');
      }
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      const state = this.peerConnection?.connectionState;
      console.log('🔗 Connection state changed:', state);
      if (state) {
        this.connectionStateSubject.next(state);
      }
    };

    console.log('🚀 WebRTC initialized for call:', callId);
  }

  async createOffer(): Promise<void> {
    console.log('🚀🚀🚀 CREATE OFFER METHOD CALLED!');
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      console.log('🔄 Creating WebRTC offer...');
      const offer = await this.peerConnection.createOffer();
      console.log('✅ WebRTC offer created:', offer);

      await this.peerConnection.setLocalDescription(offer);
      console.log('✅ Local description set');

      console.log('📤 Sending offer via SignalR');
      await this.sendOffer(JSON.stringify(offer));
      console.log('✅ Offer sent successfully');
    } catch (error) {
      console.error('❌ Error creating offer:', error);
      throw error;
    }
  }

  private async handleOffer(offerString: string): Promise<void> {
    console.log('🔍 handleOffer called with offer string length:', offerString?.length);

    if (!this.peerConnection) {
      console.error('❌ Peer connection not initialized when handling offer!');
      throw new Error('Peer connection not initialized');
    }

    console.log('✅ Peer connection exists, state:', this.peerConnection.connectionState);

    try {
      console.log('📡 Received WebRTC offer, processing...');
      const offer = JSON.parse(offerString);
      console.log('✅ Offer parsed:', offer);

      await this.peerConnection.setRemoteDescription(offer);
      console.log('✅ Remote description set');

      console.log('🔄 Creating WebRTC answer...');
      const answer = await this.peerConnection.createAnswer();
      console.log('✅ WebRTC answer created:', answer);

      await this.peerConnection.setLocalDescription(answer);
      console.log('✅ Local description set');

      console.log('📤 Sending answer via SignalR');
      await this.sendAnswer(JSON.stringify(answer));
      console.log('✅ Answer sent successfully');
    } catch (error) {
      console.error('❌ Error handling offer:', error);
      throw error;
    }
  }

  private async handleAnswer(answerString: string): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      console.log('📡 Received WebRTC answer, processing...');
      const answer = JSON.parse(answerString);
      console.log('✅ Answer parsed:', answer);

      await this.peerConnection.setRemoteDescription(answer);
      console.log('✅ Remote description set - WebRTC handshake complete!');
    } catch (error) {
      console.error('❌ Error handling answer:', error);
      throw error;
    }
  }

  private async handleIceCandidate(candidateString: string): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      console.log('📡 Processing received ICE candidate...');
      const candidate = JSON.parse(candidateString);
      console.log('🧊 ICE candidate details:', {
        type: candidate.type,
        protocol: candidate.protocol,
        address: candidate.address
      });

      await this.peerConnection.addIceCandidate(candidate);
      console.log('✅ ICE candidate added successfully');
    } catch (error) {
      console.error('❌ Error handling ICE candidate:', error);
      throw error;
    }
  }

  private async sendOffer(offer: string): Promise<void> {
    console.log('📤📤📤 SEND OFFER METHOD CALLED!');
    const hubConnection = this.chatService.hubConnectionForWebRTC;
    if (hubConnection) {
      try {
        console.log('📡 Invoking SendOffer via SignalR:', { doctorId: this.doctorId, patientId: this.patientId, callId: this.currentCallId });
        await hubConnection.invoke('SendOffer', this.doctorId, this.patientId, this.currentCallId, offer);
        console.log('✅ WebRTC offer sent successfully via SignalR');
      } catch (error) {
        console.error('❌ Error sending WebRTC offer:', error);
        throw error;
      }
    } else {
      throw new Error('SignalR connection not available');
    }
  }

  private async sendAnswer(answer: string): Promise<void> {
    const hubConnection = this.chatService.hubConnectionForWebRTC;
    if (hubConnection) {
      try {
        await hubConnection.invoke('SendAnswer', this.doctorId, this.patientId, this.currentCallId, answer);
        console.log('📤 WebRTC answer sent successfully');
      } catch (error) {
        console.error('❌ Error sending WebRTC answer:', error);
        throw error;
      }
    } else {
      throw new Error('SignalR connection not available');
    }
  }

  private async sendIceCandidate(candidate: string): Promise<void> {
    const hubConnection = this.chatService.hubConnectionForWebRTC;
    if (hubConnection) {
      try {
        await hubConnection.invoke('SendIceCandidate', this.doctorId, this.patientId, this.currentCallId, candidate);
        console.log('🧊 ICE candidate sent successfully');
      } catch (error) {
        console.error('❌ Error sending ICE candidate:', error);
        // Don't throw for ICE candidates as they're not critical
      }
    }
  }

  endCall(): void {
    console.log('🔚 WebRTC endCall() - Cleaning up all resources');

    if (this.peerConnection) {
      console.log('🔌 Closing peer connection');
      this.peerConnection.close();
      this.peerConnection = null;
    }

    if (this.localStream) {
      console.log('📹 Stopping local media stream');
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    // Reset connection state
    this.connectionStateSubject.next('disconnected');
    console.log('✅ WebRTC cleanup completed');

    this.remoteStream = null;
    this.currentCallId = '';
    this.doctorId = '';
    this.patientId = '';

    console.log('📞 WebRTC call ended');
  }

  getRemoteStream(): MediaStream | null {
    return this.remoteStream;
  }

  isConnected(): boolean {
    return this.peerConnection?.connectionState === 'connected';
  }

  updateAudioTrackState(enabled: boolean): void {
    if (this.peerConnection) {
      const senders = this.peerConnection.getSenders();
      const audioSender = senders.find(sender =>
        sender.track && sender.track.kind === 'audio'
      );

      if (audioSender && audioSender.track) {
        audioSender.track.enabled = enabled;
        console.log(`🔄 WebRTC audio sender updated: ${audioSender.track.enabled}`);
      }
    }
  }

  updateVideoTrackState(enabled: boolean): void {
    if (this.peerConnection) {
      const senders = this.peerConnection.getSenders();
      const videoSender = senders.find(sender =>
        sender.track && sender.track.kind === 'video'
      );

      if (videoSender && videoSender.track) {
        videoSender.track.enabled = enabled;
        console.log(`🔄 WebRTC video sender updated: ${videoSender.track.enabled}`);
      }
    }
  }
}
