import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';

export interface NotesDialogData {
  title: string;
  placeholder: string;
  confirmButtonText: string;
  initialValue?: string;
}

@Component({
  selector: 'app-notes-input-dialog',
  standalone: true,
  imports: [CommonModule, FormsModule, MatDialogModule],
  templateUrl: './notes-input-dialog.component.html',
  styleUrls: ['./notes-input-dialog.component.css']
})
export class NotesInputDialogComponent {
  notes: string = '';

  constructor(
    public dialogRef: MatDialogRef<NotesInputDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: NotesDialogData
  ) {
    this.notes = data.initialValue || '';
  }

  onCancel(): void {
    this.dialogRef.close(null);
  }

  onConfirm(): void {
    if (this.notes.trim()) {
      this.dialogRef.close(this.notes.trim());
    }
  }

  isValid(): boolean {
    return this.notes.trim().length > 0;
  }
}
