/* Patient Detail Component Styles */
.patient-profile {
  transition: all 0.2s ease-in-out;
}

/* Ensure floating elements stay on top and prevent flickering */
.fixed {
  z-index: 99999999 !important;
  position: fixed !important;
  transform: translateZ(0);
  will-change: auto;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  isolation: isolate;
  contain: layout style paint;
}

/* Prevent button flickering and ensure it's always visible */
.ready-to-compare-button {
  position: relative !important;
  z-index: ********** !important; /* Maximum z-index value */
  transform: translateZ(0);
  will-change: auto;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  isolation: isolate;
  contain: layout style paint;
  pointer-events: auto !important;

  /* Disable transitions that might cause flickering */
  transition: none !important;
  animation: none !important;

  /* Ensure it's always visible */
  visibility: visible !important;
  opacity: 1 !important;
  display: flex !important;
}

.ready-to-compare-button:hover {
  transform: translateZ(0) scale(1.05);
  transition: transform 0.2s ease-out !important;
}

/* Force the button to stay in viewport during scroll */
.comparison-floating-button {
  position: fixed !important;
  bottom: 1.5rem !important;
  right: 1.5rem !important;
  z-index: ********** !important; /* Maximum z-index value */
  transform: translateZ(0);
  will-change: auto;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  isolation: isolate;
  contain: layout style paint;
  pointer-events: auto !important;

  /* Ensure it's always visible */
  visibility: visible !important;
  opacity: 1 !important;
  display: block !important;

  /* Prevent any parent from hiding it */
  clip: auto !important;
  clip-path: none !important;
  overflow: visible !important;

  /* Force it to be on top of everything */
  mix-blend-mode: normal !important;
}

.patient-avatar {
  transition: all 0.2s ease-in-out;
}

.patient-avatar:hover {
  transform: scale(1.05);
}

.info-card {
  transition: all 0.2s ease-in-out;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.action-button {
  transition: all 0.2s ease-in-out;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.back-button {
  transition: all 0.2s ease-in-out;
}

.back-button:hover {
  background-color: #f3f4f6;
}

.stat-item {
  transition: all 0.2s ease-in-out;
}

/* .stat-item:hover {
  background-color: #f8fafc;
} */

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-image-container {
  position: relative;
  overflow: hidden;
}

.profile-image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.profile-image-container:hover::before {
  opacity: 1;
}

.quick-actions {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.detail-section {
  border-left: 4px solid #e5e7eb;
  transition: border-color 0.2s ease-in-out;
}

.detail-section:hover {
  border-color: #3b82f6;
}

.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-primary {
  background-color: #dbeafe;
  color: #1e40af;
}

.badge-success {
  background-color: #d1fae5;
  color: #065f46;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

/* .contact-item:hover {
  background-color: #f3f4f6;
} */

.contact-icon {
  flex-shrink: 0;
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.icon-email {
  background-color: #dbeafe;
  color: #1e40af;
}

.icon-phone {
  background-color: #d1fae5;
  color: #065f46;
}

.icon-address {
  background-color: #fef3c7;
  color: #92400e;
}

.icon-birthday {
  background-color: #fce7f3;
  color: #be185d;
}

.icon-updated {
  background-color: #e0e7ff;
  color: #3730a3;
}

/* Image Gallery Styles */
.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  background-color: #f3f4f6;
  aspect-ratio: 1;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.gallery-item:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease-in-out;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  display: flex;
  align-items: flex-end;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-info {
  padding: 0.75rem;
  color: white;
  width: 100%;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
}

.gallery-item:hover .gallery-info {
  transform: translateY(0);
}

.project-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: rgba(59, 130, 246, 0.9);
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.gallery-item:hover .project-indicator {
  opacity: 1;
}

.download-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: rgba(255, 255, 255, 0.9);
  color: #374151;
  padding: 0.375rem;
  border-radius: 0.25rem;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  border: none;
  cursor: pointer;
}

.download-btn:hover {
  background-color: white;
  transform: scale(1.1);
}

.gallery-item:hover .download-btn {
  opacity: 1;
}

/* Lightbox Styles */
.lightbox {
  position: fixed;
  inset: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-in-out;
}

.lightbox-image {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.lightbox-btn {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  z-index: 10;
}

.lightbox-btn:hover {
  background-color: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.lightbox-close {
  top: 1rem;
  right: 1rem;
}

.lightbox-prev {
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

.lightbox-next {
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

.lightbox-info {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 1rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

/* Filter Buttons */
.filter-btn {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.filter-btn.active {
  background-color: #dbeafe;
  color: #1e40af;
}

.filter-btn:not(.active) {
  background-color: #f3f4f6;
  color: #6b7280;
}

.filter-btn:not(.active):hover {
  background-color: #e5e7eb;
}

/* Project Group Styles */
.project-group {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.project-group:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.project-header {
  background-color: #f9fafb;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.project-status {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 9999px;
  background-color: #dbeafe;
  color: #1e40af;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gallery-item {
    aspect-ratio: 1;
  }

  .lightbox-image {
    max-width: 95vw;
    max-height: 80vh;
  }

  .lightbox-info {
    bottom: 0.5rem;
    left: 0.5rem;
    right: 0.5rem;
    padding: 0.75rem;
  }

  /* Mobile zoom controls positioning */
  .zoom-control {
    padding: 0.5rem;
  }

  .zoom-indicator {
    font-size: 0.625rem;
    min-width: 45px;
    padding: 0.375rem 0.5rem;
  }

  /* Mobile navigation buttons */
  .lightbox-nav-btn {
    padding: 0.75rem;
  }

  .lightbox-nav-btn svg {
    width: 1.25rem;
    height: 1.25rem;
  }
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced Lightbox Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Apply animations */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-zoomIn {
  animation: zoomIn 0.4s ease-out;
}

.animate-slideInFromTop {
  animation: slideInFromTop 0.3s ease-out;
}

.animate-slideInFromBottom {
  animation: slideInFromBottom 0.3s ease-out;
}

.animate-pulse-custom {
  animation: pulse 2s infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Scrollbar hiding for thumbnails */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Enhanced glassmorphism effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Button hover effects */
.btn-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  transform: translateY(-2px);
}

.btn-scale:hover {
  transform: scale(1.05);
}

/* Image loading placeholder */
.image-placeholder {
  background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
  background-size: 400% 400%;
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced focus states */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Smooth transitions */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Image gallery enhancements */
.image-grid-item {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.image-grid-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.image-grid-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.image-grid-item:hover::before {
  opacity: 1;
}

/* List view specific styles */
.image-list-item {
  transition: all 0.3s ease;
  /* background: white; */
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.image-list-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #d1d5db;
}

.image-list-item .thumbnail-container {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
}

.image-list-item .thumbnail-container img {
  transition: transform 0.3s ease;
}

.image-list-item:hover .thumbnail-container img {
  transform: scale(1.05);
}

.image-list-item .content-section {
  padding: 16px;
  min-width: 0;
}

.image-list-item .action-buttons {
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
}

.image-list-item:hover .action-buttons {
  opacity: 1;
  transform: translateX(0);
}

.image-list-item .action-button {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.image-list-item .action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.image-list-item .action-button:hover::before {
  left: 100%;
}

.image-list-item .action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* View toggle button styles */
.view-toggle-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.view-toggle-button.active {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1d4ed8;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.view-toggle-button.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), transparent);
  pointer-events: none;
}

.view-toggle-button:hover:not(.active) {
  background-color: #f9fafb;
  transform: translateY(-1px);
}

.view-toggle-button svg {
  transition: transform 0.3s ease;
}

.view-toggle-button:hover svg {
  transform: scale(1.1);
}

/* Image comparison mode styles */
.comparison-mode-active {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  border: 2px solid #3b82f6;
}

.comparison-selection-indicator {
  animation: pulse 2s infinite;
}

.comparison-selected {
  transform: scale(0.95);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
}

.comparison-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.comparison-view-modal {
  backdrop-filter: blur(20px);
  animation: fadeIn 0.3s ease-out;
}

.comparison-image-container {
  transition: all 0.3s ease;
}

.comparison-image-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.comparison-analysis-card {
  transition: all 0.3s ease;
}

.comparison-analysis-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Enhanced Modal Animations */
.comparison-view-modal {
  animation: modalFadeIn 0.4s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(20px);
  }
}

/* Enhanced Card Hover Effects */
.enhanced-card {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.enhanced-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.enhanced-card:hover::before {
  left: 100%;
}

.enhanced-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

/* Button Enhancement Animations */
.enhanced-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.enhanced-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transition: width 0.6s, height 0.6s;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.enhanced-button:hover::before {
  width: 300px;
  height: 300px;
}

.enhanced-button > * {
  position: relative;
  z-index: 1;
}

/* Floating Action Buttons */
.floating-action-btn {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.floating-action-btn:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.floating-action-btn:active {
  transform: translateY(0) scale(0.95);
}

/* Gradient Text Animation */
.gradient-text {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Loading Animation Enhancement */
.enhanced-loading {
  position: relative;
}

.enhanced-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  border: 3px solid transparent;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translate(-50%, -50%);
}

/* Image Quality Indicator */
.quality-indicator {
  animation: qualityPulse 2s ease-in-out infinite;
}

@keyframes qualityPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Enhanced Grid Layout */
.enhanced-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (min-width: 1200px) {
  .enhanced-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Backdrop Blur Enhancement */
.enhanced-backdrop {
  backdrop-filter: blur(20px) saturate(180%);
  background: rgba(255, 255, 255, 0.1);
}

/* Smooth Scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Enhanced Border Animations */
.animated-border {
  position: relative;
  background: linear-gradient(45deg, transparent, transparent);
  background-clip: padding-box;
  border: 2px solid transparent;
}

.animated-border::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  margin: -2px;
  border-radius: inherit;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 400% 400%;
  animation: gradientShift 3s ease infinite;
}

/* Text Glow Effect */
.text-glow {
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* 3D Button Effect */
.button-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.button-3d:hover {
  transform: translateZ(10px) rotateX(5deg);
}

/* Particle Effect for Background */
.particle-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0;
  }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

/* Enhanced Focus States */
.enhanced-focus:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Staggered Animation */
.stagger-animation > * {
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Zoom Controls for Comparison Mode */
.zoom-control {
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}

.zoom-control:hover:not(:disabled) {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.zoom-control:active:not(:disabled) {
  transform: scale(0.95);
}

.zoom-control:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Comparison Image Zoom Animations */
.comparison-image {
  transition: transform 0.3s ease-out;
  cursor: grab;
}

.comparison-image:active {
  cursor: grabbing;
}

.comparison-image.zoomed {
  cursor: move;
}

/* Zoom Level Indicator */
.zoom-indicator {
  backdrop-filter: blur(10px);
  transition: all 0.3s ease-in-out;
}

.zoom-indicator:hover {
  transform: scale(1.05);
}

/* Pan Instructions */
.pan-instruction {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enhanced Comparison Modal Animations */
.comparison-modal-enter {
  animation: comparisonModalSlideIn 0.5s ease-out;
}

@keyframes comparisonModalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive Zoom Controls */
@media (max-width: 768px) {
  .zoom-control {
    padding: 0.5rem;
  }

  .zoom-control svg {
    width: 1rem;
    height: 1rem;
  }

  .zoom-indicator {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Enhanced Image Transform Styles */
.image-transform-container {
  overflow: hidden;
  position: relative;
}

.image-transform-container img {
  transition: transform 0.2s ease-out;
  max-width: none;
  height: auto;
}

/* Sync Button Styles */
.sync-zoom-button {
  background: linear-gradient(45deg, #f59e0b, #ea580c);
  animation: syncPulse 2s ease-in-out infinite;
}

@keyframes syncPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
}

/* Enhanced Floating Action Buttons */
.floating-action-btn {
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-action-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.floating-action-btn:active {
  transform: translateY(0) scale(0.95);
}

/* Zoom Level Badges */
.zoom-level-badge {
  animation: zoomBadgeAppear 0.3s ease-out;
}

@keyframes zoomBadgeAppear {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Enhanced Image Interaction */
.comparison-image-container {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.comparison-image-container img {
  pointer-events: auto;
  user-select: none;
  -webkit-user-drag: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Loading States for Zoomed Images */
.image-loader-overlay {
  backdrop-filter: blur(5px);
  transition: opacity 0.3s ease-in-out;
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .zoom-control svg {
    transform: scale(0.9);
  }
}

/* Custom Scrollbar Styles for Modal */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #4B5563 #1F2937;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #1F2937;
  border-radius: 8px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #4B5563;
  border-radius: 8px;
  border: 2px solid #1F2937;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

/* Modal Scrolling Improvements */
.modal-scroll-container {
  max-height: calc(95vh - 120px); /* Account for header */
  overflow-y: auto;
  overflow-x: hidden;
}

/* Smooth scrolling behavior */
.modal-scroll-container {
  scroll-behavior: smooth;
}

/* Hide scrollbar on mobile but keep functionality */
@media (max-width: 768px) {
  .modal-scroll-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .modal-scroll-container::-webkit-scrollbar {
    display: none;
  }
}


/* Enhanced Modal Layout */
.comparison-modal-flex {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 95vh;
}

.comparison-modal-header {
  flex-shrink: 0;
}

.comparison-modal-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.comparison-modal-scrollable {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Mobile Touch Scrolling Improvements */
@media (max-width: 768px) {
  .comparison-modal-scrollable {
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: y proximity;
  }

  /* Smooth momentum scrolling on iOS */
  .scrollbar-thin {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent rubber band scrolling on iOS */
  .modal-container {
    overscroll-behavior: contain;
  }
}

/* Scroll position indicators */
.scroll-position-indicator {
  position: fixed;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  width: 4px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  z-index: 1000;
}

.scroll-position-thumb {
  width: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* Enhanced fade effects for scrolling */
.scroll-fade-gradient {
  pointer-events: none;
  position: sticky;
  z-index: 10;
}

.scroll-fade-top {
  top: 0;
  background: linear-gradient(180deg, rgba(30, 41, 59, 0.9) 0%, rgba(30, 41, 59, 0.7) 50%, transparent 100%);
  height: 30px;
  margin-bottom: -30px;
}

.scroll-fade-bottom {
  bottom: 0;
  background: linear-gradient(0deg, rgba(30, 41, 59, 0.9) 0%, rgba(30, 41, 59, 0.7) 50%, transparent 100%);
  height: 30px;
  margin-top: -30px;
}

/* Improved scrollbar for better visibility */
.modal-scrollbar::-webkit-scrollbar {
  width: 12px;
}

.modal-scrollbar::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.8);
  border-radius: 6px;
  margin: 10px 0;
}

.modal-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #6366f1, #8b5cf6);
  border-radius: 6px;
  border: 2px solid rgba(15, 23, 42, 0.8);
}

.modal-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #7c3aed, #a855f7);
}

/* Content spacing for better mobile experience */
@media (max-width: 640px) {
  .comparison-grid-mobile {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .comparison-card-mobile {
    min-height: 300px;
  }

  .zoom-controls-mobile {
    scale: 0.9;
  }
}
