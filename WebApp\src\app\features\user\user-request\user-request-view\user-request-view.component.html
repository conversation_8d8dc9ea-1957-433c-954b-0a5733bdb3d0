<div class=" p-5 bg-white dark:bg-gray-900 min-h-screen transition-colors duration-200">
  <ng-container *ngIf="patientRequest">
    <!-- Back Button -->
    <div class="flex items-center mb-3">
      <a routerLink="/request"
        class="flex items-center px-3 py-1.5 text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="text-sm font-medium">Back to Requests</span>
      </a>
    </div>

    <!-- Project Information Card -->
    <div
      class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-6 border border-gray-200 dark:border-gray-700 relative transition-colors duration-200">
      <!-- Card Header with modern gradient -->
      <div
        class="px-6 py-4 bg-gradient-to-r from-blue-700 via-blue-600 to-indigo-600 dark:from-blue-950 dark:via-blue-950 dark:to-indigo-950 flex justify-between items-center">
        <div class="flex items-center">
          <div class="h-10 w-10 bg-white/20 dark:bg-gray-900/20 rounded-full flex items-center justify-center mr-3">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
              </path>
            </svg>
          </div>
          <h2 class="text-xl font-semibold text-white">Patient Information</h2>
        </div>
        <div class="flex items-center space-x-3">


          <!-- Status Update Dropdown (for Admin/Doctor) -->
          <select *ngIf="isAdmin || isDoctor" [value]="patientRequest.status" (change)="updateRequestStatus($event)"
            class="text-sm rounded-lg px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 shadow-sm "
            [ngClass]="{
              'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/50 dark:text-green-300 dark:border-green-700': patientRequest.status === 'Completed',
              'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900/50 dark:text-amber-300 dark:border-amber-700': patientRequest.status === 'Open',
              'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/50 dark:text-red-300 dark:border-red-700': patientRequest.status === 'Critical',
              'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-800 dark:text-blue-300 dark:border-blue-700': patientRequest.status === 'In Progress',
              'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600': !patientRequest.status || (patientRequest.status !== 'Completed' && patientRequest.status !== 'Open' && patientRequest.status !== 'Critical' && patientRequest.status !== 'In Progress')
            }">
            <option value="Open">Open</option>
            <option value="In Progress">In Progress</option>
            <option value="Completed">Completed</option>
          </select>

        </div>
      </div>

      <div class="p-6">
        <!-- Project Title with Priority Badge -->
        <div class="flex items-center mb-5">
          <h3 class="text-2xl font-bold text-gray-800 dark:text-gray-100">{{ patientRequest.subject || 'Project #' +
            patientRequest.id }}</h3>
          <span class="ml-3 px-3 py-1 rounded-full text-xs font-medium" [ngClass]="{
            'bg-red-100 text-red-800 border border-red-200 dark:bg-red-900/50 dark:text-red-300 dark:border-red-700': patientRequest.priority?.toLowerCase() === 'high',
            'bg-amber-100 text-amber-800 border border-amber-200 dark:bg-amber-900/50 dark:text-amber-300 dark:border-amber-700': patientRequest.priority?.toLowerCase() === 'medium',
            'bg-green-100 text-green-800 border border-green-200 dark:bg-green-900/50 dark:text-green-300 dark:border-green-700': patientRequest.priority?.toLowerCase() === 'low',
            'bg-gray-100 text-gray-800 border border-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600': !patientRequest.priority
          }">
            Priority: {{ patientRequest.priority || 'Normal' }}
          </span>
        </div>

        <!-- Project Details in a modern card-based layout -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">


          <!-- Email Card -->
          <div
            class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 hover:shadow-md transition-all">
            <div class="flex items-start">
              <div class="bg-blue-100 dark:bg-blue-900/50 rounded-full p-2 mr-3">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                  </path>
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Email</p>
                <p class="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">{{ patientRequest.userEmail
                  }}</p>
              </div>
            </div>
          </div>

          <!-- Dates Info Card -->
          <div
            class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 hover:shadow-md transition-all">
            <div class="flex items-start">
              <div class="bg-green-100 dark:bg-green-900/50 rounded-full p-2 mr-3">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
              <div>
                <p class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Timeline</p>
                <div class="flex space-x-4">
                  <div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Created</p>
                    <p class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ patientRequest.createdDate |
                      date:'mediumDate' }}</p>
                  </div>

                </div>
              </div>
            </div>
          </div>

          <!-- Problem Description Card - Full Width -->
          <div
            class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 md:col-span-3 hover:shadow-md transition-all">
            <div class="flex items-start">
              <div class="bg-teal-100 dark:bg-teal-900/50 rounded-full p-2 mr-3">
                <svg class="w-5 h-5 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                  </path>
                </svg>
              </div>
              <div>
                <p class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Problem Message</p>
                <p class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ patientRequest.message }}</p>
              </div>
            </div>
          </div>

          <!-- Assigned Doctor Card - Full Width -->
          <div
            class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 md:col-span-3 hover:shadow-md transition-all">
            <div class="flex items-start justify-between">
              <div class="flex items-start">
                <div class="bg-amber-100 dark:bg-amber-900/50 rounded-full p-2 mr-3">
                  <svg class="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Assigned Doctor</p>
                  <p class="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">{{
                    patientRequest.assignedEmail || 'Unassigned' }}</p>
                </div>
              </div>
              <button (click)="openDoctorSelectionDialog()"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-700 rounded-lg shadow-sm hover:from-blue-700 hover:to-indigo-700 dark:hover:from-blue-800 dark:hover:to-indigo-800 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all">
                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                  </path>
                </svg>
                Assign Doctor
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabbed Navigation Section -->
    <div
      class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-8 border border-gray-100 dark:border-gray-700 transform transition-all hover:shadow-xl">
      <div
        class="border-b border-gray-200 dark:border-gray-600 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900">
        <nav class="flex px-2 pt-2">
          <button (click)="setActiveTab('routine')"
            [class]="activeTab === 'routine' ?
              'border-b-2 border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400 whitespace-nowrap px-8 py-4 font-medium text-sm rounded-t-lg bg-white dark:bg-gray-800 shadow-[0_-2px_8px_rgba(0,0,0,0.05)]' :
              'border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 whitespace-nowrap px-8 py-4 font-medium text-sm rounded-t-lg transition-all'">
            <div class="flex items-center">
              <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              Daily Routine
            </div>
          </button>

          <button (click)="setActiveTab('medication')"
            [class]="activeTab === 'medication' ?
              'border-b-2 border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400 whitespace-nowrap px-8 py-4 font-medium text-sm rounded-t-lg bg-white dark:bg-gray-800 shadow-[0_-2px_8px_rgba(0,0,0,0.05)]' :
              'border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 whitespace-nowrap px-8 py-4 font-medium text-sm rounded-t-lg transition-all'">
            <div class="flex items-center">
              <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z">
                </path>
              </svg>
              Medicine Description
            </div>
          </button>

          <button (click)="setActiveTab('attachments')"
            [class]="activeTab === 'attachments' ?
              'border-b-2 border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400 whitespace-nowrap px-8 py-4 font-medium text-sm rounded-t-lg bg-white dark:bg-gray-800 shadow-[0_-2px_8px_rgba(0,0,0,0.05)]' :
              'border-b-2 border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 whitespace-nowrap px-8 py-4 font-medium text-sm rounded-t-lg transition-all'">
            <div class="flex items-center">
              <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13">
                </path>
              </svg>
              Attachments
            </div>
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="p-8">
        <!-- Daily Routine Tab -->
        <div *ngIf="activeTab === 'routine'" class="animate-fadeIn">
          <div class="mb-6 flex justify-between items-center">
            <div>
              <h3
                class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-700 to-blue-500 dark:from-blue-500 dark:to-blue-300">
                Daily Routine Schedule</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Personalized activity plan for optimal recovery
              </p>
            </div>
            <div class="flex space-x-3">
              <button (click)="openAddRoutineDialog()"
                class="inline-flex items-center px-4 py-2 text-xs font-medium border border-transparent rounded-lg text-white bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-700 shadow-md hover:from-blue-700 hover:to-indigo-700 dark:hover:from-blue-800 dark:hover:to-indigo-800 transform hover:scale-105 transition-all duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Routine
              </button>
            </div>
          </div>

          <!-- Day Tabs -->
          <div class="flex overflow-x-auto space-x-3 mb-6 pb-3 day-tabs">
            <button *ngFor="let day of uniqueDays" (click)="filterRoutineByDay(day)"
              [class]="selectedDay === day ?
                      'px-5 py-2.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-700 text-white text-sm font-medium flex-shrink-0 shadow-md transform scale-105 transition-all' :
                      'px-5 py-2.5 rounded-full bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 text-sm font-medium flex-shrink-0 shadow-sm border border-gray-200 dark:border-gray-600 hover:border-blue-200 dark:hover:border-blue-500 transition-all duration-200'">
              {{ day }}
            </button>
            <button (click)="filterRoutineByDay('All')"
              [class]="selectedDay === 'All' ?
                      'px-5 py-2.5 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-700 text-white text-sm font-medium flex-shrink-0 shadow-md transform scale-105 transition-all' :
                      'px-5 py-2.5 rounded-full bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 text-sm font-medium flex-shrink-0 shadow-sm border border-gray-200 dark:border-gray-600 hover:border-blue-200 dark:hover:border-blue-500 transition-all duration-200'">
              All Days
            </button>
          </div>

          <!-- Loading Spinner -->
          <div *ngIf="loading" class="flex justify-center items-center py-10">
            <div
              class="animate-spin rounded-full h-10 w-10 border-4 border-blue-500 dark:border-blue-400 border-t-transparent">
            </div>
            <span class="ml-3 text-gray-600 dark:text-gray-400">Loading routines...</span>
          </div>

          <!-- Empty state - when no routines are available -->
          <div *ngIf="!loading && filteredRoutine.length === 0" class="text-center py-10">
            <div class="mx-auto h-24 w-24 text-gray-400 dark:text-gray-500 mb-5">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-700 dark:text-gray-200 mb-2">No routines found</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-6">There are no daily routines available for this day.</p>
            <button (click)="openAddRoutineDialog()"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-700 dark:to-indigo-700 rounded-lg shadow-md hover:from-blue-700 hover:to-indigo-700 dark:hover:from-blue-800 dark:hover:to-indigo-800">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Add First Routine
            </button>
          </div>

          <!-- Routine Timeline -->
          <div *ngIf="!loading && filteredRoutine.length > 0" class="space-y-5">
            <div *ngFor="let item of filteredRoutine; let i = index"
              class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-5 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:border-blue-200 dark:hover:border-blue-500 routine-card overflow-hidden relative"
              [ngClass]="{'from-left': i % 2 === 0, 'from-right': i % 2 === 1}">
              <!-- Background subtle pattern with very low opacity -->
              <div class="absolute inset-0 z-0 bg-pattern opacity-5 dark:opacity-10"></div>

              <!-- Border decoration line on left side -->
              <div
                class="absolute left-0 top-0 h-full w-1.5 bg-gradient-to-b from-blue-600 via-indigo-500 to-blue-400 dark:from-blue-700 dark:via-indigo-600 dark:to-blue-500">
              </div>

              <div class="flex justify-between items-start relative z-10">
                <div class="flex items-start">
                  <div
                    class="h-14 w-14 rounded-full bg-gradient-to-br from-blue-500 to-indigo-500 dark:from-blue-600 dark:to-indigo-600 flex items-center justify-center text-white mr-4 shadow-md pulse-animation border-2 border-white dark:border-gray-800">
                    <svg class="h-7 w-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <div>
                    <div class="flex items-center flex-wrap gap-2">
                      <h4 class="font-bold text-gray-900 dark:text-gray-100 text-lg">{{ item.activity }}</h4>
                      <span
                        class="px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/50 dark:to-indigo-900/50 text-blue-800 dark:text-blue-300 shadow-sm">
                        {{ item.day }}
                      </span>
                    </div>
                    <p class="text-sm text-indigo-600 dark:text-indigo-400 font-medium mt-1 flex items-center">
                      <svg class="h-4 w-4 mr-1 text-indigo-500 dark:text-indigo-400" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      {{ item.time }}
                    </p>
                    <p
                      class="text-sm text-gray-700 dark:text-gray-300 mt-3 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 p-3 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm">
                      {{ item.description }}
                    </p>
                  </div>
                </div>
                <div class="flex space-x-1">
                  <button (click)="openEditRoutineDialog(item)"
                    class="text-gray-400 dark:text-gray-500 hover:text-blue-500 dark:hover:text-blue-400 p-2 hover:bg-blue-50 dark:hover:bg-blue-900/50 rounded-full transition-all transform hover:scale-110">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                      </path>
                    </svg>
                  </button>
                  <button (click)="deleteRoutine(item)"
                    class="text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 p-2 hover:bg-red-50 dark:hover:bg-red-900/50 rounded-full transition-all transform hover:scale-110">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                      </path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Medicine Description Tab -->
        <div *ngIf="activeTab === 'medication'" class="animate-fadeIn">
          <div class="mb-6 flex justify-between items-center">
            <div>
              <h3
                class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-teal-700 to-indigo-500 dark:from-teal-500 dark:to-indigo-300">
                Prescribed Medications</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Patient's current medication regimen</p>
            </div>
            <div class="flex space-x-3">
              <button (click)="openAddMedicationDialog()"
                class="inline-flex items-center px-4 py-2 text-xs font-medium border border-transparent rounded-lg text-white bg-gradient-to-r from-teal-600 to-indigo-600 dark:from-teal-700 dark:to-indigo-700 shadow-md hover:from-teal-700 hover:to-indigo-700 dark:hover:from-teal-800 dark:hover:to-indigo-800 transform hover:scale-105 transition-all duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Medication
              </button>
            </div>
          </div>

          <!-- Loading Spinner -->
          <div *ngIf="loading" class="flex justify-center items-center py-10">
            <div
              class="animate-spin rounded-full h-10 w-10 border-4 border-teal-500 dark:border-teal-400 border-t-transparent">
            </div>
            <span class="ml-3 text-gray-600 dark:text-gray-400">Loading medications...</span>
          </div>

          <!-- No Medications Message -->
          <div *ngIf="!loading && (!medicineData || medicineData.length === 0)" class="text-center py-10">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-teal-50 dark:bg-teal-900/50 rounded-full flex items-center justify-center">
              <svg class="w-10 h-10 text-teal-400 dark:text-teal-300" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                </path>
              </svg>
            </div>
            <h4 class="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">No Medications Found</h4>
            <p class="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
              No medications have been prescribed yet. Click "Add Medication" to create a new prescription.
            </p>
          </div>

          <!-- Medications Grid -->
          <div *ngIf="!loading && medicineData && medicineData.length > 0"
            class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div *ngFor="let med of medicineData; let i = index"
              class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-5 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:border-teal-200 dark:hover:border-teal-500 relative overflow-hidden"
              [ngClass]="{'from-left': i % 2 === 0, 'from-right': i % 2 === 1}">
              <!-- Decorative pill capsule shapes in background -->
              <div
                class="absolute -right-8 -top-8 w-24 h-24 rounded-full bg-teal-50 dark:bg-teal-900/30 opacity-30 z-0">
              </div>
              <div
                class="absolute -right-6 -bottom-6 w-16 h-16 rounded-full bg-indigo-50 dark:bg-indigo-900/30 opacity-30 z-0">
              </div>

              <!-- Border decoration line on left side -->
              <div
                class="absolute left-0 top-0 h-full w-1.5 bg-gradient-to-b from-teal-600 via-indigo-500 to-teal-400 dark:from-teal-700 dark:via-indigo-600 dark:to-teal-500">
              </div>

              <div class="flex justify-between items-center relative z-10">
                <div class="flex items-center">
                  <div
                    class="h-12 w-12 rounded-full bg-gradient-to-br from-teal-500 to-indigo-600 dark:from-teal-600 dark:to-indigo-700 flex items-center justify-center text-white mr-3 shadow-md border-2 border-white dark:border-gray-800 pulse-animation">
                    <svg class="h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z">
                      </path>
                    </svg>
                  </div>
                  <h4 class="font-bold text-gray-900 dark:text-gray-100 text-lg">{{ med.name }}</h4>
                </div>
                <span
                  class="px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-teal-100 to-indigo-100 dark:from-teal-900/50 dark:to-indigo-900/50 text-teal-800 dark:text-teal-300 shadow-sm">
                  {{ med.dosage }}
                </span>
              </div>

              <div
                class="mt-4 p-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm relative z-10">
                <div class="space-y-3">
                  <div class="flex items-center">
                    <span class="text-xs font-semibold text-teal-600 dark:text-teal-400 w-24 flex items-center">
                      <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      Frequency:
                    </span>
                    <span class="text-sm text-gray-700 dark:text-gray-300 font-medium">{{ getMedicineFrequency(med)
                      }}</span>
                  </div>
                  <div class="flex items-center">
                    <span class="text-xs font-semibold text-teal-600 dark:text-teal-400 w-24 flex items-center">
                      <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      Timing:
                    </span>
                    <span class="text-sm text-gray-700 dark:text-gray-300 font-medium">{{ getMedicineTiming(med)
                      }}</span>
                  </div>
                  <div class="flex items-start">
                    <span class="text-xs font-semibold text-teal-600 dark:text-teal-400 w-24 flex items-center mt-1">
                      <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      Details:
                    </span>
                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ med.description }}</span>
                  </div>
                </div>
              </div>
              <div class="mt-4 pt-4 flex justify-end space-x-3 border-t border-gray-100 dark:border-gray-700">
                <button (click)="openEditMedicationDialog(med)"
                  class="text-xs px-3 py-1.5 rounded-lg text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/50 hover:text-indigo-700 dark:hover:text-indigo-300 font-medium transition-all duration-200 flex items-center transform hover:scale-105 btn-shine">
                  <svg class="h-3.5 w-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                    </path>
                  </svg>
                  Edit
                </button>
                <button (click)="deleteMedication(med)"
                  class="text-xs px-3 py-1.5 rounded-lg text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/50 hover:text-red-700 dark:hover:text-red-300 font-medium transition-all duration-200 flex items-center transform hover:scale-105 btn-shine">
                  <svg class="h-3.5 w-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                    </path>
                  </svg>
                  Remove
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Attachments Tab -->
        <div *ngIf="activeTab === 'attachments'" class="animate-fadeIn">
          <!-- Loading Overlay -->
          <div *ngIf="loading"
            class="fixed inset-0 bg-gray-900/50 dark:bg-gray-900/70 z-50 flex items-center justify-center">
            <div class="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-lg flex items-center">
              <div
                class="animate-spin rounded-full h-8 w-8 border-4 border-blue-500 dark:border-blue-400 border-t-transparent mr-3">
              </div>
              <span class="text-gray-700 dark:text-gray-200 font-medium">Processing document...</span>
            </div>
          </div>

          <div class="mb-6 flex justify-between items-center">
            <div>
              <h3
                class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-700 to-cyan-500 dark:from-blue-500 dark:to-cyan-300">
                Medical Documents & Images</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Patient's medical records and reports</p>
            </div>
            <div class="flex space-x-3">
              <button (click)="openAttachmentUploadDialog()" [disabled]="loading"
                class="inline-flex items-center px-4 py-2 text-xs font-medium border border-transparent rounded-lg text-white bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-700 dark:to-cyan-700 shadow-md hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-800 dark:hover:to-cyan-800 transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                Upload Document
              </button>
            </div>
          </div>

          <!-- Attachments Grid -->
          <div class="space-y-8">
            <!-- No attachments message -->
            <div *ngIf="!hasAnyAttachments()"
              class="flex flex-col items-center justify-center py-12 bg-gray-50 dark:bg-gray-800 rounded-xl border border-dashed border-gray-300 dark:border-gray-600">
              <svg class="w-16 h-16 text-gray-300 dark:text-gray-500 mb-4" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                </path>
              </svg>
              <h3 class="text-gray-500 dark:text-gray-300 text-lg font-medium">No attachments found</h3>
              <p class="text-gray-400 dark:text-gray-400 text-sm mt-2">This request doesn't have any attached files.</p>
            </div>

            <!-- Documents grouped by date -->
            <div *ngFor="let dateKey of attachmentDateKeys" class="mb-8">
              <!-- Date header with subtle animation -->
              <div class="mb-6 border-b border-gray-200 dark:border-gray-600 pb-3 flex justify-between items-center">
                <h3 class="text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <div
                    class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 rounded-full flex items-center justify-center mr-3 shadow-lg">
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-1 12a2 2 0 002 2h6a2 2 0 002-2L16 7m-6 0V3a2 2 0 012-2h4a2 2 0 012 2v4">
                      </path>
                    </svg>
                  </div>
                  {{ formatDateForDisplay(dateKey) }}
                </h3>
                <div class="flex items-center space-x-2">
                  <span
                    class="px-3 py-1.5 rounded-full text-sm font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/50 dark:to-indigo-900/50 text-blue-800 dark:text-blue-300 shadow-md border border-blue-200 dark:border-blue-700">
                    {{ attachmentsByDate[dateKey].length }} {{ attachmentsByDate[dateKey].length === 1 ? 'file' :
                    'files' }}
                  </span>
                </div>
              </div>

              <!-- Files for this date -->
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div *ngFor="let file of attachmentsByDate[dateKey]; let i = index"
                  class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 hover:border-blue-200 dark:hover:border-blue-500 attachment-card relative group"
                  [ngClass]="{'from-left': i % 3 === 0, 'from-center': i % 3 === 1, 'from-right': i % 3 === 2}">
                  <div
                    class="h-48 bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center overflow-hidden relative">
                    <!-- Image wrapper with loading state -->
                    <div *ngIf="file.type === 'Image'" class="relative w-full h-full">
                      <!-- Loading indicator -->
                      <div
                        class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/50 dark:to-indigo-900/50 z-10 image-loading">
                        <div class="flex flex-col items-center">
                          <div
                            class="animate-spin rounded-full h-10 w-10 border-4 border-blue-500 dark:border-blue-400 border-t-transparent mb-2">
                          </div>
                          <span class="text-xs text-blue-600 dark:text-blue-300 font-medium">Loading image...</span>
                        </div>
                      </div>

                      <!-- Actual image with enhanced styling -->
                      <img [src]="file.url" [alt]="'Medical image: ' + file.name"
                        class="w-full h-full object-cover hover:scale-105 transition-all duration-300 z-20 cursor-pointer"
                        (load)="onImageLoad($event)" (error)="onImageError($event, file)"
                        (click)="openImagePreview(file)">
                    </div>

                    <!-- Document preview with enhanced styling -->
                    <div *ngIf="file.type === 'Document'"
                      class="h-full w-full flex flex-col items-center justify-center bg-gradient-to-br from-amber-50 to-orange-100 dark:from-amber-900/50 dark:to-orange-900/50 p-4 cursor-pointer hover:bg-gradient-to-br hover:from-amber-100 hover:to-orange-200 dark:hover:from-amber-800/50 dark:hover:to-orange-800/50 transition-all duration-300"
                      (click)="downloadAttachment(file.name)">
                      <div
                        class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 transform hover:scale-105 transition-transform duration-300 border border-amber-200 dark:border-amber-700 mb-3 w-20 h-20 flex items-center justify-center">
                        <svg class="h-12 w-12 text-amber-500 dark:text-amber-400" fill="none" stroke="currentColor"
                          viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                          </path>
                        </svg>
                      </div>
                      <span class="text-xs text-amber-700 dark:text-amber-300 font-medium text-center">
                        {{ file.name.split('.').pop()?.toUpperCase() }} Document
                      </span>
                      <span class="text-xs text-amber-600 dark:text-amber-400 mt-1 opacity-75">Click to download</span>
                    </div>
                  </div>

                  <div
                    class="p-4 bg-gradient-to-b from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border-t border-gray-100 dark:border-gray-700">
                    <div class="flex flex-col">
                      <h4 class="font-semibold text-gray-900 dark:text-gray-100 truncate mb-2 text-sm">{{ file.name }}
                      </h4>
                      <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-3 space-x-2">
                        <span
                          class="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 px-2 py-1 rounded-full font-medium">{{
                          file.size }}</span>
                        <span class="text-gray-300 dark:text-gray-500">•</span>
                        <span class="font-medium">{{ file.uploadDate | date:'mediumDate' }}</span>
                      </div>
                      <!-- Action buttons with improved styling -->
                      <div class="flex justify-between items-center mt-2 space-x-2">
                        <button (click)="openFileInfoDialog(file)"
                          class="flex-1 inline-flex items-center justify-center text-xs px-3 py-2 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/50 dark:to-blue-900/50 text-blue-700 dark:text-blue-300 hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800/50 dark:hover:to-blue-800/50 transition-all duration-200 transform hover:scale-105 font-medium shadow-sm">
                          <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          Details
                        </button>
                        <button (click)="downloadAttachment(file.name)"
                          class="flex-1 inline-flex items-center justify-center text-xs px-3 py-2 rounded-lg bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/50 dark:to-blue-900/50 text-blue-700 dark:text-blue-300 hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800/50 dark:hover:to-blue-800/50 transition-all duration-200 transform hover:scale-105 font-medium shadow-sm">
                          <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                          </svg>
                          Download
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <!-- Loading State -->
  <div *ngIf="!patientRequest" class="flex flex-col justify-center items-center h-64">
    <div
      class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-600 dark:border-blue-400 mb-4 shadow-md">
    </div>
    <p class="text-gray-500 dark:text-gray-400 animate-pulse mt-4 font-medium">Loading patient details...</p>
  </div>

  <!-- Styled Toast Component for Notifications -->
  <app-styled-toast></app-styled-toast>

</div>
