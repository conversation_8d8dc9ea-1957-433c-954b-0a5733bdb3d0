{"version": 3, "sources": ["../../../../../../node_modules/@editorjs/paragraph/dist/paragraph.mjs"], "sourcesContent": ["(function () {\n  \"use strict\";\n\n  try {\n    if (typeof document < \"u\") {\n      var e = document.createElement(\"style\");\n      e.appendChild(document.createTextNode(\".ce-paragraph{line-height:1.6em;outline:none}.ce-block:only-of-type .ce-paragraph[data-placeholder-active]:empty:before,.ce-block:only-of-type .ce-paragraph[data-placeholder-active][data-empty=true]:before{content:attr(data-placeholder-active)}.ce-paragraph p:first-of-type{margin-top:0}.ce-paragraph p:last-of-type{margin-bottom:0}\")), document.head.appendChild(e);\n    }\n  } catch (a) {\n    console.error(\"vite-plugin-css-injected-by-js\", a);\n  }\n})();\nconst a = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8 9V7.2C8 7.08954 8.08954 7 8.2 7L12 7M16 9V7.2C16 7.08954 15.9105 7 15.8 7L12 7M12 7L12 17M12 17H10M12 17H14\"/></svg>';\nfunction l(r) {\n  const t = document.createElement(\"div\");\n  t.innerHTML = r.trim();\n  const e = document.createDocumentFragment();\n  return e.append(...Array.from(t.childNodes)), e;\n}\n/**\n * Base Paragraph Block for the Editor.js.\n * Represents a regular text block\n *\n * <AUTHOR> (<EMAIL>)\n * @copyright CodeX 2018\n * @license The MIT License (MIT)\n */\nclass n {\n  /**\n   * Default placeholder for Paragraph Tool\n   *\n   * @returns {string}\n   * @class\n   */\n  static get DEFAULT_PLACEHOLDER() {\n    return \"\";\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {object} params - constructor params\n   * @param {ParagraphData} params.data - previously saved data\n   * @param {ParagraphConfig} params.config - user config for Tool\n   * @param {object} params.api - editor.js api\n   * @param {boolean} readOnly - read only mode flag\n   */\n  constructor({\n    data: t,\n    config: e,\n    api: i,\n    readOnly: s\n  }) {\n    this.api = i, this.readOnly = s, this._CSS = {\n      block: this.api.styles.block,\n      wrapper: \"ce-paragraph\"\n    }, this.readOnly || (this.onKeyUp = this.onKeyUp.bind(this)), this._placeholder = e.placeholder ? e.placeholder : n.DEFAULT_PLACEHOLDER, this._data = t ?? {}, this._element = null, this._preserveBlank = e.preserveBlank ?? !1;\n  }\n  /**\n   * Check if text content is empty and set empty string to inner html.\n   * We need this because some browsers (e.g. Safari) insert <br> into empty contenteditanle elements\n   *\n   * @param {KeyboardEvent} e - key up event\n   */\n  onKeyUp(t) {\n    if (t.code !== \"Backspace\" && t.code !== \"Delete\" || !this._element) return;\n    const {\n      textContent: e\n    } = this._element;\n    e === \"\" && (this._element.innerHTML = \"\");\n  }\n  /**\n   * Create Tool's view\n   *\n   * @returns {HTMLDivElement}\n   * @private\n   */\n  drawView() {\n    const t = document.createElement(\"DIV\");\n    return t.classList.add(this._CSS.wrapper, this._CSS.block), t.contentEditable = \"false\", t.dataset.placeholderActive = this.api.i18n.t(this._placeholder), this._data.text && (t.innerHTML = this._data.text), this.readOnly || (t.contentEditable = \"true\", t.addEventListener(\"keyup\", this.onKeyUp)), t;\n  }\n  /**\n   * Return Tool's view\n   *\n   * @returns {HTMLDivElement}\n   */\n  render() {\n    return this._element = this.drawView(), this._element;\n  }\n  /**\n   * Method that specified how to merge two Text blocks.\n   * Called by Editor.js by backspace at the beginning of the Block\n   *\n   * @param {ParagraphData} data\n   * @public\n   */\n  merge(t) {\n    if (!this._element) return;\n    this._data.text += t.text;\n    const e = l(t.text);\n    this._element.appendChild(e), this._element.normalize();\n  }\n  /**\n   * Validate Paragraph block data:\n   * - check for emptiness\n   *\n   * @param {ParagraphData} savedData — data received after saving\n   * @returns {boolean} false if saved data is not correct, otherwise true\n   * @public\n   */\n  validate(t) {\n    return !(t.text.trim() === \"\" && !this._preserveBlank);\n  }\n  /**\n   * Extract Tool's data from the view\n   *\n   * @param {HTMLDivElement} toolsContent - Paragraph tools rendered view\n   * @returns {ParagraphData} - saved data\n   * @public\n   */\n  save(t) {\n    return {\n      text: t.innerHTML\n    };\n  }\n  /**\n   * On paste callback fired from Editor.\n   *\n   * @param {HTMLPasteEvent} event - event with pasted data\n   */\n  onPaste(t) {\n    const e = {\n      text: t.detail.data.innerHTML\n    };\n    this._data = e, window.requestAnimationFrame(() => {\n      this._element && (this._element.innerHTML = this._data.text || \"\");\n    });\n  }\n  /**\n   * Enable Conversion Toolbar. Paragraph can be converted to/from other tools\n   * @returns {ConversionConfig}\n   */\n  static get conversionConfig() {\n    return {\n      export: \"text\",\n      // to convert Paragraph to other block, use 'text' property of saved data\n      import: \"text\"\n      // to covert other block's exported string to Paragraph, fill 'text' property of tool data\n    };\n  }\n  /**\n   * Sanitizer rules\n   * @returns {SanitizerConfig} - Edtior.js sanitizer config\n   */\n  static get sanitize() {\n    return {\n      text: {\n        br: !0\n      }\n    };\n  }\n  /**\n   * Returns true to notify the core that read-only mode is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Used by Editor paste handling API.\n   * Provides configuration to handle P tags.\n   *\n   * @returns {PasteConfig} - Paragraph Paste Setting\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"P\"]\n    };\n  }\n  /**\n   * Icon and title for displaying at the Toolbox\n   *\n   * @returns {ToolboxConfig} - Paragraph Toolbox Setting\n   */\n  static get toolbox() {\n    return {\n      icon: a,\n      title: \"Text\"\n    };\n  }\n}\nexport { n as default };"], "mappings": ";;;CAAC,WAAY;AACX;AAEA,MAAI;AACF,QAAI,OAAO,WAAW,KAAK;AACzB,UAAI,IAAI,SAAS,cAAc,OAAO;AACtC,QAAE,YAAY,SAAS,eAAe,8UAA8U,CAAC,GAAG,SAAS,KAAK,YAAY,CAAC;AAAA,IACrZ;AAAA,EACF,SAASA,IAAG;AACV,YAAQ,MAAM,kCAAkCA,EAAC;AAAA,EACnD;AACF,GAAG;AACH,IAAM,IAAI;AACV,SAAS,EAAE,GAAG;AACZ,QAAM,IAAI,SAAS,cAAc,KAAK;AACtC,IAAE,YAAY,EAAE,KAAK;AACrB,QAAM,IAAI,SAAS,uBAAuB;AAC1C,SAAO,EAAE,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,CAAC,GAAG;AAChD;AASA,IAAM,IAAN,MAAM,GAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAON,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,UAAU;AAAA,EACZ,GAAG;AACD,SAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,OAAO;AAAA,MAC3C,OAAO,KAAK,IAAI,OAAO;AAAA,MACvB,SAAS;AAAA,IACX,GAAG,KAAK,aAAa,KAAK,UAAU,KAAK,QAAQ,KAAK,IAAI,IAAI,KAAK,eAAe,EAAE,cAAc,EAAE,cAAc,GAAE,qBAAqB,KAAK,QAAQ,KAAK,CAAC,GAAG,KAAK,WAAW,MAAM,KAAK,iBAAiB,EAAE,iBAAiB;AAAA,EAChO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,GAAG;AACT,QAAI,EAAE,SAAS,eAAe,EAAE,SAAS,YAAY,CAAC,KAAK,SAAU;AACrE,UAAM;AAAA,MACJ,aAAa;AAAA,IACf,IAAI,KAAK;AACT,UAAM,OAAO,KAAK,SAAS,YAAY;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW;AACT,UAAM,IAAI,SAAS,cAAc,KAAK;AACtC,WAAO,EAAE,UAAU,IAAI,KAAK,KAAK,SAAS,KAAK,KAAK,KAAK,GAAG,EAAE,kBAAkB,SAAS,EAAE,QAAQ,oBAAoB,KAAK,IAAI,KAAK,EAAE,KAAK,YAAY,GAAG,KAAK,MAAM,SAAS,EAAE,YAAY,KAAK,MAAM,OAAO,KAAK,aAAa,EAAE,kBAAkB,QAAQ,EAAE,iBAAiB,SAAS,KAAK,OAAO,IAAI;AAAA,EAC3S;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACP,WAAO,KAAK,WAAW,KAAK,SAAS,GAAG,KAAK;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,GAAG;AACP,QAAI,CAAC,KAAK,SAAU;AACpB,SAAK,MAAM,QAAQ,EAAE;AACrB,UAAM,IAAI,EAAE,EAAE,IAAI;AAClB,SAAK,SAAS,YAAY,CAAC,GAAG,KAAK,SAAS,UAAU;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,GAAG;AACV,WAAO,EAAE,EAAE,KAAK,KAAK,MAAM,MAAM,CAAC,KAAK;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,GAAG;AACN,WAAO;AAAA,MACL,MAAM,EAAE;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG;AACT,UAAM,IAAI;AAAA,MACR,MAAM,EAAE,OAAO,KAAK;AAAA,IACtB;AACA,SAAK,QAAQ,GAAG,OAAO,sBAAsB,MAAM;AACjD,WAAK,aAAa,KAAK,SAAS,YAAY,KAAK,MAAM,QAAQ;AAAA,IACjE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,mBAAmB;AAC5B,WAAO;AAAA,MACL,QAAQ;AAAA;AAAA,MAER,QAAQ;AAAA;AAAA,IAEV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,IAAI;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,cAAc;AACvB,WAAO;AAAA,MACL,MAAM,CAAC,GAAG;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": ["a"]}