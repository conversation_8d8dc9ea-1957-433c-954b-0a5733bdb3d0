import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'localTime',
  standalone: true
})
export class LocalTimePipe implements PipeTransform {

  transform(value: any): Date | null {
    if (!value) {
      return null;
    }

    let utcDate: Date;

    // Handle different input types
    if (value instanceof Date) {
      utcDate = value;
    } else if (typeof value === 'string') {
      utcDate = new Date(value);
    } else if (value && typeof value === 'object' && value.toJSDate) {
      // Luxon DateTime object - convert to UTC first
      utcDate = value.toUTC().toJSDate();
    } else if (value && typeof value === 'object' && value.c) {
      // Luxon DateTime object (alternative format)
      const luxonObj = value as any;

      // Check if this Luxon object is already in local time (if hour is around 14 when UTC should be 18)
      // If so, don't subtract 4 hours again
      const luxonHour = luxonObj.c.hour;
      console.log('🔧 PIPE - Luxon hour:', luxonHour, 'Full Luxon object:', luxonObj);

      // Create date from Luxon components (this will be in the timezone the Luxon object represents)
      utcDate = new Date(
        luxonObj.c.year,
        luxonObj.c.month - 1, // Month is 0-based in Date constructor
        luxonObj.c.day,
        luxonObj.c.hour,
        luxonObj.c.minute,
        luxonObj.c.second,
        luxonObj.c.millisecond || 0
      );
    } else {
      return null;
    }

    // Ensure we have a valid date
    if (isNaN(utcDate.getTime())) {
      return null;
    }

    // Convert UTC to local time by subtracting 4 hours
    const localTime = new Date(utcDate.getTime() - (4 * 60 * 60 * 1000));

    return localTime;
  }
}
