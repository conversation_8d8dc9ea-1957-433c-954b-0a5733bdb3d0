import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideHttpClient, withFetch, withInterceptors, HTTP_INTERCEPTORS } from '@angular/common/http';
import { AuthInterceptor } from './interceptors/auth.interceptor';

import { routes } from './app.routes';
import { API_BASE_URL } from '../shared/service-proxies/service-proxies';
import { provideClientHydration } from '@angular/platform-browser';
import { HttpInterceptorService } from '../shared/services/http-interceptor.service';
import { importProvidersFrom } from '@angular/core';
import { ServiceProxyModule } from '../shared/service-proxies/service-proxy.module';

export function getRemoteServiceBaseUrl(): string {
  let url = (window as any).location.host;
  if (url.indexOf("localhost") >= 0)
    return 'https://localhost:44314';
  else
    return `https://patient-api-dev.3dbotics.com`;
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideClientHydration(),
    provideAnimationsAsync(),
    provideHttpClient(
      withFetch(),
      withInterceptors([HttpInterceptorService])
    ),
    { provide: API_BASE_URL, useFactory: getRemoteServiceBaseUrl },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    importProvidersFrom(ServiceProxyModule),
  ]
};
