.chat-list-container {
  height: 100%;
  /* background: white; */
  position: relative;
  display: flex;
  flex-direction: column;
}

.chat-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.chat-list-header h2 {
  margin: 0;
  color: #2c5aa0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.chat-list-header h2 i {
  color: #007bff;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.refresh-btn,
.new-chat-btn {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.refresh-btn:hover:not(:disabled),
.new-chat-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.new-chat-btn {
  background: #28a745;
}

.new-chat-btn:hover {
  background: #218838;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Ensure modal appears above everything */
.fixed.z-50 {
  z-index: 50 !important;
  position: fixed !important;
}

/* Mobile-first modal positioning */
@media (max-width: 640px) {
  .animate-slide-up {
    border-radius: 1.5rem 1.5rem 0 0 !important;
  }
}

/* Enhanced Loading States */
.chat-thread-item.loading {
  opacity: 0.8;
  pointer-events: none;
  position: relative;
  background: linear-gradient(90deg, #ffffff, #f8f9fa, #ffffff);
  background-size: 200% 100%;
  animation: loadingShimmer 1.5s ease-in-out infinite;
}

/* Enhanced Hover Effects */
.chat-thread-item:hover {
  transform: translateX(2px);
}

.chat-thread-item:hover .avatar-circle {
  transform: scale(1.05);
}

/* Modern Animations */
@keyframes modernSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmerLoading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes loadingShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Apply enhanced animations to elements */
.chat-thread-item {
  animation: slideInUp 0.3s ease-out;
}

.unread-badge {
  animation: bounceIn 0.5s ease-out;
}

/* Mobile Optimizations */
@media (max-width: 480px) {
  .chat-thread-item {
    padding: 12px !important;
  }
  .chat-list-container{
    margin-top: 0px;
  }

  .thread-avatar {
    margin-right: 12px !important;
  }
}

/* Touch targets for mobile */
@media (max-width: 768px) {
  .chat-thread-item {
    min-height: 60px;
    touch-action: manipulation;
  }

  button {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Dark mode specific styles */
.dark .chat-thread-item:hover {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* Responsive text scaling */
@media (max-width: 640px) {
  .text-responsive-sm {
    font-size: 0.875rem; /* 14px */
  }

  .text-responsive-base {
    font-size: 1rem; /* 16px */
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .text-responsive-sm {
    font-size: 0.875rem; /* 14px */
  }

  .text-responsive-base {
    font-size: 1rem; /* 16px */
  }
}

@media (min-width: 1025px) {
  .text-responsive-sm {
    font-size: 0.875rem; /* 14px */
  }

  .text-responsive-base {
    font-size: 1.125rem; /* 18px */
  }
}

/* Avatar scaling */
@media (max-width: 640px) {
  .avatar-sm {
    width: 2.5rem !important; /* 40px */
    height: 2.5rem !important; /* 40px */
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .avatar-md {
    width: 3rem !important; /* 48px */
    height: 3rem !important; /* 48px */
  }
}

@media (min-width: 1025px) {
  .avatar-lg {
    width: 3.5rem !important; /* 56px */
    height: 3.5rem !important; /* 56px */
  }
}

/* Improved focus states for accessibility */
.chat-thread-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chat-thread-item {
    border: 1px solid currentColor;
  }

  .avatar-circle {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Custom scrollbar for webkit browsers */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Floating Action Button - Only in Chat List Container */
.floating-action-btn {
  position: absolute;
  bottom: 2px;
  right: 2px;
  z-index: 1000;
}

.floating-action-btn .fab-btn {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  isolation: isolate;
}

.floating-action-btn .fab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
  z-index: -1;
}

.floating-action-btn .fab-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
}

.floating-action-btn .fab-btn:hover::before {
  opacity: 1;
}

.floating-action-btn .fab-btn:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.floating-action-btn .fab-btn:focus {
  outline: none;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4), 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Dark mode styling for floating action button */
.dark .floating-action-btn .fab-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4);
}

.dark .floating-action-btn .fab-btn::before {
  background: linear-gradient(135deg, #6366f1 0%, #4338ca 100%);
}

.dark .floating-action-btn .fab-btn:hover {
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.5);
}

.dark .floating-action-btn .fab-btn:active {
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.dark .floating-action-btn .fab-btn:focus {
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4), 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Icon styling for floating action button */
.floating-action-btn .fab-btn i {
  font-size: 20px;
  color: white;
  transition: transform 0.3s ease;
}

.floating-action-btn .fab-btn:hover i {
  transform: rotate(90deg);
}

/* Responsive styles for floating action button */
/* Tablet optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .floating-action-btn {
    bottom: 24px;
    right: 24px;
  }

  .floating-action-btn .fab-btn {
    width: 60px;
    height: 60px;
    font-size: 26px;
  }

  .floating-action-btn .fab-btn i {
    font-size: 22px;
  }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
  .floating-action-btn {
    bottom: 32px;
    right: 32px;
  }

  .floating-action-btn .fab-btn {
    width: 64px;
    height: 64px;
    font-size: 28px;
  }

  .floating-action-btn .fab-btn i {
    font-size: 24px;
  }
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .floating-action-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
  }

  .floating-action-btn .fab-btn {
    width: 52px;
    height: 52px;
    font-size: 22px;
    box-shadow: 0 3px 12px rgba(59, 130, 246, 0.4);
  }

  .floating-action-btn .fab-btn i {
    font-size: 18px;
  }

  .floating-action-btn .fab-btn:hover {
    transform: translateY(-1px) scale(1.03);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.5);
  }

  .dark .floating-action-btn .fab-btn {
    box-shadow: 0 3px 12px rgba(79, 70, 229, 0.4);
  }

  .dark .floating-action-btn .fab-btn:hover {
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.5);
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .floating-action-btn {
    position: fixed;
    bottom: 16px;
    right: 16px;
    z-index: 1000;
  }

  .floating-action-btn .fab-btn {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .floating-action-btn .fab-btn i {
    font-size: 16px;
  }
}

/* Safe area adjustments for mobile devices */
@supports (bottom: env(safe-area-inset-bottom)) {
  @media (max-width: 640px) {
    .floating-action-btn {
      position: fixed;
      bottom: calc(20px + env(safe-area-inset-bottom));
      right: calc(20px + env(safe-area-inset-right));
      z-index: 1000;
    }
  }
}

/* Pulse animation for attention */
.floating-action-btn.pulse .fab-btn {
  animation: fabPulse 2s infinite;
}

@keyframes fabPulse {
  0% {
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4), 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4), 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4), 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.dark .floating-action-btn.pulse .fab-btn {
  animation: fabPulseDark 2s infinite;
}

@keyframes fabPulseDark {
  0% {
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4), 0 0 0 0 rgba(79, 70, 229, 0.7);
  }
  70% {
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4), 0 0 0 10px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4), 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

/* Loading state for floating action button */
.floating-action-btn.loading .fab-btn {
  pointer-events: none;
}

.floating-action-btn.loading .fab-btn i {
  animation: fabSpin 1s linear infinite;
}

@keyframes fabSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .floating-action-btn .fab-btn {
    border: 2px solid currentColor;
    background: #0066cc;
  }

  .dark .floating-action-btn .fab-btn {
    background: #0080ff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .floating-action-btn .fab-btn {
    transition: none;
  }

  .floating-action-btn .fab-btn:hover {
    transform: none;
  }

  .floating-action-btn .fab-btn i {
    transition: none;
  }

  .floating-action-btn .fab-btn:hover i {
    transform: none;
  }

  .floating-action-btn.pulse .fab-btn {
    animation: none;
  }

  .floating-action-btn.loading .fab-btn i {
    animation: none;
  }
}
