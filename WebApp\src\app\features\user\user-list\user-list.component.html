<div class="p-5">
  <!-- Header -->
  <div
    class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-4 sm:p-3 xs:p-2 relative">
    <div class="px-6 py-4 sm:px-3 sm:py-2 xs:px-1 xs:py-1">
      <div class="header-container flex flex-col sm:flex-row gap-4 sm:gap-0">
        <!-- Left Side: User Management Heading -->
        <div class="header-left flex-1">
          <h1 class="text-2xl sm:text-xl xs:text-lg font-bold dark:text-white text-gray-900">User Management</h1>
          <p class="text-gray-600 dark:text-gray-200 mt-1 text-base sm:text-sm xs:text-xs">Manage users, roles, and
            permissions</p>
        </div>
        <!-- Right Side: Search, Filters and Add User Button -->
        <div
          class="header-right flex flex-col sm:flex-row gap-2 sm:gap-4 items-stretch sm:items-center w-full sm:w-auto">
          <!-- Search Box -->
          <div class="flex items-center px-4 py-2  gap-4 w-full sm:w-auto transition-colors duration-300">
            <label for="search" class="sr-only">Search users</label>
            <div class="relative w-full">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
              <input id="search" type="text" [(ngModel)]="searchQuery" (input)="onSearch()"
                placeholder="Search users..."
                class="search-input w-full sm:w-64 xs:w-full text-sm sm:text-base pl-10 pr-3 py-2 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 border outline-none border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 shadow-sm placeholder-gray-500 dark:placeholder-gray-400" />
            </div>
          </div>

          <!-- Role Filter -->
          <div class="px-4 py-2 pr-6 w-full sm:w-auto" (clickOutside)="showDropdown = false">
            <div class="relative z-50">
              <button type="button" (click)="showDropdown = !showDropdown"
                class="flex items-center justify-between min-w-[140px] w-full sm:w-auto text-sm sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 rounded-md px-4 py-2 transition-colors duration-200 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 relative z-50">
                <span class="truncate">
                  {{ roleFilter || 'All Roles' }}
                </span>
                <svg class="h-4 w-4 text-gray-500 dark:text-gray-400 ml-2 transition-transform duration-200"
                  [class.rotate-180]="showDropdown" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>

              <!-- Dropdown Menu -->
              <div *ngIf="showDropdown"
                class="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-md shadow-2xl border border-gray-200 dark:border-gray-700 w-fit  max-h-60 overflow-y-auto z-[9999]">
                <div class="py-1 relative z-[9999]">
                  <!-- All Roles Option -->
                  <button type="button" (click)="selectRole('')"
                    class="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 relative z-[9999]"
                    [class]="!roleFilter ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-700 dark:text-gray-300'">
                    <div class="flex items-center">
                      <svg *ngIf="!roleFilter" class="h-4 w-4 text-blue-600 dark:text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span class="ml-6" *ngIf="roleFilter"></span>
                      All Roles
                    </div>
                  </button>

                  <!-- Individual Role Options -->
                  <ng-container *ngFor="let role of availableRoles">
                    <button type="button" (click)="selectRole(role)"
                      class="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 relative z-[9999]"
                      [class]="roleFilter === role ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-700 dark:text-gray-300'">
                      <div class="flex items-center">
                        <svg *ngIf="roleFilter === role" class="h-4 w-4 text-blue-600 dark:text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="ml-6" *ngIf="roleFilter !== role"></span>
                        {{ role }}
                      </div>
                    </button>
                  </ng-container>
                </div>
              </div>
            </div>
          </div>

          <!-- Add User Button -->
          <div class="flex items-center w-full sm:w-auto">
            <button  routerLink="/users/add"
              class="add-btn">
              <svg slot="icon" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              <span>Add User</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Users Table -->
  <div
    class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-x-auto transition-colors duration-300">
    <div *ngIf="isLoading" class="p-8 xs:p-4">
      <app-loading-spinner text="Loading users..." [size]="40"></app-loading-spinner>
    </div>

    <div *ngIf="!isLoading && filteredUsers.length === 0" class="p-8 xs:p-4 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor"
        viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
        </path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No users found</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Try adjusting your search or filter criteria.</p>
    </div>

    <div *ngIf="!isLoading && filteredUsers.length > 0">
      <div class="w-full overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-sm xs:text-xs">
          <thead class="bg-gray-50 dark:bg-gray-900">
            <tr>
              <th scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                User</th>
              <th scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                Email</th>
              <th scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                Roles</th>
              <th scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                Skills</th>
              <th scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr *ngFor="let user of displayedUsers"
              class="hover:bg-gray-50 dark:hover:!bg-gray-700 transition-colors duration-200">
              <!-- User Name & Avatar -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div
                    class="flex-shrink-0 h-10 w-10 bg-gray-200 dark:!bg-gray-600 rounded-full flex items-center justify-center overflow-hidden">
                    <span class="text-gray-600 dark:text-gray-300 font-medium text-sm">{{ user.name ?
                      user.name.charAt(0).toUpperCase() : '?' }}</span>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ user.name }}</div>
                  </div>
                </div>
              </td>
              <!-- Email -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-gray-100">{{ user.email }}</div>
              </td>
              <!-- Roles -->
              <td class="px-6 py-4 whitespace-nowrap relative">
                <div class="flex flex-wrap gap-1 items-center">
                  <span *ngFor="let role of user.roles"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium role-badge"
                    [ngClass]="getRoleColorClass(role)">
                    {{ role }} <button *ngIf="user.roles && user.roles.length > 1"
                      (click)="removeRoleFromUser(user, role)"
                      class="ml-1 opacity-0 group-hover:opacity-100 role-remove-btn" [disabled]="isRemovingRole">
                      <svg class="h-3 w-3 text-gray-600 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400"
                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                      </svg>
                    </button>
                  </span>
                  <!-- Add Role Button -->
                  <button (click)="openRoleSelector(user, $event)"
                    class="ml-1 inline-flex items-center justify-center h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 transition-colors role-add-btn">
                    <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                  </button>
                </div>
              </td>
              <!-- Skills -->
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900 dark:text-gray-100">{{ getSkillsString(user.skills) }}</div>
              </td>
              <!-- Actions -->
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-center ">
                  <button [disabled]="!hasRole(user, 'User')" (click)="openDeleteConfirm(user, $event)"
                    class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-red-500 dark:focus:ring-red-400 rounded-full p-1 transition-colors duration-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                      </path>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- Pagination Controls -->
      <div class="pagination-container bg-gray-50 dark:bg-gray-900 px-6 py-4 border-t border-gray-200 dark:border-gray-700 transition-colors duration-300">
        <div class="flex flex-col sm:flex-row items-center justify-between">
          <!-- Results Info -->
          <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
            Showing <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> to
            <span class="font-medium">{{ Math.min(currentPage * pageSize, totalUsers) }}</span> of
            <span class="font-medium">{{ totalUsers }}</span> users
          </div>
          <!-- Pagination Navigation -->
          <div class="flex items-center space-x-2">
            <!-- Page Size Selector -->
            <div class="flex items-center mr-3">
              <label for="page-size" class="text-sm text-gray-600 dark:text-gray-400 mr-2">Show:</label>
              <select id="page-size" [(ngModel)]="pageSize" (change)="onPageSizeChange($event)"
                class="min-w-[56px] py-1 px-2 rounded-md border border-gray-300 dark:border-gray-600 text-sm bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition">
                <option *ngFor="let size of getPageSizeOptions()" [value]="size">{{ size }}</option>
              </select>
            </div>
            <!-- Previous Button -->
            <button (click)="previousPage()" [disabled]="currentPage === 1"
              class="w-8 h-8 !flex !items-center !justify-center !rounded-md !bg-gray-100 dark:!bg-gray-800 !text-gray-700 dark:!text-gray-200 hover:!bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            <!-- First Page Button (if not in view) -->
            <button *ngIf="paginationRange[0] > 1" (click)="goToPage(1)"
              class="w-8 h-8 flex items-center justify-center rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 transition-colors">
              1
            </button>
            <!-- Ellipsis (if needed) -->
            <span *ngIf="paginationRange[0] > 2" class="px-1 text-gray-400 dark:text-gray-500">…</span>
            <!-- Page Numbers -->
            <button *ngFor="let page of paginationRange" (click)="goToPage(page)"
              class="w-8 h-8 flex items-center justify-center rounded-md mx-0.5 border transition-colors"
              [ngClass]="page === currentPage
                ? 'bg-blue-600 text-white border-blue-600 font-medium'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border-gray-300 dark:border-gray-600'">
              {{ page }}
            </button>
            <!-- Ellipsis (if needed) -->
            <span *ngIf="paginationRange[paginationRange.length - 1] < totalPages - 1" class="px-1 text-gray-400 dark:text-gray-500">…</span>
            <!-- Last Page Button (if not in view) -->
            <button *ngIf="paginationRange[paginationRange.length - 1] < totalPages" (click)="goToPage(totalPages)"
              class="w-8 h-8 flex items-center justify-center rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 transition-colors">
              {{ totalPages }}
            </button>
            <!-- Next Button -->
            <button (click)="nextPage()" [disabled]="currentPage === totalPages"
              class="w-8 h-8 flex items-center justify-center rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Role Selector Popover -->
<div *ngIf="showRoleSelector"
  class="fixed inset-0 z-[9999] flex items-center justify-center p-4 bg-black bg-opacity-50 backdrop-blur-sm transition-all duration-300"
  (click)="closeRoleSelector()">
  <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-md transform transition-all duration-300 scale-100"
    (click)="$event.stopPropagation()"
    [style.max-height.vh]="80">

    <!-- Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center">
        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3">
          <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Add Roles</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">Assign roles to {{ selectedUser?.name }}</p>
        </div>
      </div>
      <button (click)="closeRoleSelector()"
        class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Status Messages -->
      <div *ngIf="operationSuccess"
        class="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 text-sm rounded-lg flex items-center transition-all duration-300">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        {{ operationMessage }}
      </div>

      <div *ngIf="operationError"
        class="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 text-sm rounded-lg flex items-center transition-all duration-300">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        {{ operationMessage }}
      </div>

      <!-- Available Roles -->
      <div class="space-y-3">
        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Available Roles</h4>

        <div *ngIf="getAvailableRolesToAdd().length === 0"
          class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <p class="text-sm text-gray-500 dark:text-gray-400 font-medium">All roles assigned</p>
          <p class="text-xs text-gray-400 dark:text-gray-500">User already has all available roles</p>
        </div>

        <div class="max-h-64 overflow-y-auto custom-scrollbar space-y-2">
          <div *ngFor="let role of getAvailableRolesToAdd()" class="group relative">
            <div
              class="role-item mt-2 flex items-center p-3 rounded-lg border transition-all duration-200 cursor-pointer"
              [ngClass]="{
                'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/10': !selectedRolesToAdd.includes(role),
                'border-blue-500 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/20': selectedRolesToAdd.includes(role)
              }"
              (click)="toggleRoleSelection(role)">

              <div class="flex items-center flex-1">
                <input
                  type="checkbox"
                  [id]="'role-select-' + role"
                  [checked]="selectedRolesToAdd.includes(role)"
                  (click)="$event.stopPropagation()"
                  class="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded transition-colors duration-200">

                <div class="ml-3 flex-1">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ role }}</span>
                    <span
                      *ngIf="selectedRolesToAdd.includes(role)"
                      class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                      Selected
                    </span>
                  </div>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ getRoleDescription(role) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50 rounded-b-xl">
      <div class="text-sm text-gray-500 dark:text-gray-400">
        {{ selectedRolesToAdd.length }} role{{ selectedRolesToAdd.length !== 1 ? 's' : '' }} selected
      </div>

      <div class="flex items-center space-x-3">
        <button (click)="closeRoleSelector()"
          class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400">
          Cancel
        </button>

        <button [disabled]="selectedRolesToAdd.length === 0 || isAddingRole"
          (click)="addRolesToUser()"
          class="px-6 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center min-w-[120px] justify-center">
          <svg *ngIf="isAddingRole" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span *ngIf="!isAddingRole">Add {{ selectedRolesToAdd.length > 0 ? '(' + selectedRolesToAdd.length + ')' : '' }}</span>
          <span *ngIf="isAddingRole">Adding...</span>
        </button>
      </div>
    </div>
  </div>
</div>
