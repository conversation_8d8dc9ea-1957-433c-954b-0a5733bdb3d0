import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { RoutineFormComponent } from '../routine-form/routine-form.component';
import { MedicationFormComponent } from '../medication-form/medication-form.component';
import { AttachmentUploadComponent } from '../attachment-upload/attachment-upload.component';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { FileInfoDialogComponent } from '../file-info-dialog/file-info-dialog.component';
import { DailyRoutine, DailyRoutineServiceProxy, FileServiceProxy, Medicine, WoundRequestServiceProxy, WoundRequestDto, UserAccountServiceProxy, RequestFilesServiceProxy, RequestFileDto, MedicineRoutineContollerServiceProxy, MedicineRoutine } from '../../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../../shared/service-proxies/service-proxy.module';
import { getRemoteServiceBaseUrl } from '../../../../app.config';
import { DateTime } from 'luxon';
import { AuthService } from '../../../../../shared/services/auth.service';
import { GlobalMessageNotificationService } from '../../../../services/global-message-notification.service';
import { StyledToastComponent } from '../../../../components/styled-toast/styled-toast.component';

interface ProjectViewDto {
  id: number;
  message: string;
  filesName: string[];
  userEmail: string;
  status: string;
  createdDate?: string;
  completionDate?: string;
  priority?: string;
  subject?: string;
  assignedEmail?: string;
  workspaceTitle?: string;
  workspaceId?: number;
  summary?: string;
  projectCategoryId?: number;
  projectCategory?: string;
  matchedDocuments?: any[];
  requestFiles?: RequestFileDto[]; // New property from backend

  // Additional fields for UI compatibility
  patientName?: string;
  patientEmail?: string;
  problems?: string;
  requestDate?: Date;
  lastUpdated?: Date;
  age?: number;
  category?: string;
  dailyRoutine?: RoutineItem[];
  medications?: Medication[];
  attachments?: Attachment[];
}

interface RoutineItem {
  day: string;
  time: string;
  activity: string;
  description: string;
}

interface Medication {
  id?: string;
  name: string;
  dosage: string;
  frequency: string;
  timing: string;
  description: string;
}

interface Attachment {
  id: number;
  name: string;
  type: string;
  size: string;
  uploadDate: Date;
  url: string;
  description?: string;
  aiAnalysis?: string;
  medicalFindings?: string;
  requestFileData?: RequestFileDto; // Store the full RequestFileDto for the dialog
}

interface ChatMessage {
  id: number;
  sender: 'patient' | 'doctor' | 'system';
  message: string;
  timestamp: Date;
  read: boolean;
}

@Component({
  selector: 'app-user-request-view',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, MatDialogModule, ServiceProxyModule, StyledToastComponent],
  templateUrl: './user-request-view.component.html',
  styleUrl: './user-request-view.component.css'
})
export class UserRequestViewComponent implements OnInit {
  requestId: number | null = null;
  patientRequest: ProjectViewDto | null = null;
  activeTab: 'routine' | 'medication' | 'attachments' = 'routine';
  uniqueDays: string[] = [];
  selectedDay: string = 'All';
  filteredRoutine: RoutineItem[] = [];
  loading: boolean = false;
  baseUrl: string = getRemoteServiceBaseUrl();
  dailyRoutineData: DailyRoutine[] = [];
  medicineData: Medicine[] = [];



  // Chat related properties
  isChatOpen: boolean = false;
  chatMessages: ChatMessage[] = [];
  newMessage: string = '';
  chatMessageForm = new FormGroup({
    message: new FormControl('', Validators.required)
  });

  // Object to store attachments grouped by date
  attachmentsByDate: { [key: string]: Attachment[] } = {};
  // Array of unique dates for display order
  attachmentDateKeys: string[] = [];

  // Request files data
  requestFiles: RequestFileDto[] = [];
  requestImages: RequestFileDto[] = [];
  requestDocuments: RequestFileDto[] = [];
  isLoadingFiles = false;

  // Role properties
  isAdmin = false;
  isDoctor = false;

  constructor(
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private _woundRequestService: WoundRequestServiceProxy,
    private _fileService: FileServiceProxy,
    private _dailyRoutieService: DailyRoutineServiceProxy,
    // private _medicineService: MedicineServiceProxy,
    private _medicineRoutineService: MedicineRoutineContollerServiceProxy,
    private _userService: UserAccountServiceProxy,
    private _requestFilesService: RequestFilesServiceProxy,
    private authService: AuthService,
    private globalMessageService: GlobalMessageNotificationService
  ) { }
  ngOnInit(): void {
    // Initialize roles
    this.isAdmin = this.authService.hasRole('Admin');
    this.isDoctor = this.authService.hasRole('Doctor');

    this.route.params.subscribe(params => {
      this.requestId = +params['id'];
      console.log('Loading project with ID:', this.requestId);

      // Load project data
      this.LoadAllData(this.requestId);
      this.loadDailyRoutineData(this.requestId)
      this.loadMedicineData(this.requestId)
      this.loadRequestFiles(this.requestId);

      // Load chat messages (independent of project data)
    });
  }
  doctors: any[] = [];
  private doctorsLoading = false;
  private doctorsLoaded = false;

  loadDocotrDat() {
    // Prevent multiple simultaneous calls
    if (this.doctorsLoading || this.doctorsLoaded) {
      return;
    }

    this.doctorsLoading = true;
    this.loading = true;

    this._userService.getAllDoctors().subscribe({
      next: (res) => {
        console.log('Doctors data:', res);
        this.doctors = res || [];
        this.doctorsLoaded = true;
        this.doctorsLoading = false;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading doctors:', error);
        this.showErrorNotification('Failed to load doctors list.');
        this.doctorsLoading = false;
        this.loading = false;
      }
    });
  }

  openDoctorSelectionDialog(): void {
    // Load doctors data only when needed
    if (!this.doctorsLoaded && !this.doctorsLoading) {
      this.loadDocotrDat();
    }

    // Check if doctors are already loaded, if not wait for them to load
    const checkDoctorsAndOpenDialog = () => {
      if (this.doctorsLoaded || (!this.doctorsLoading && this.doctors.length > 0)) {
        import('./../../user-request/doctor-selection-dialog/doctor-selection-dialog.component')
          .then(({ DoctorSelectionDialogComponent }) => {
            const dialogRef = this.dialog.open(DoctorSelectionDialogComponent, {
              width: '500px',
              data: { doctors: this.doctors }
            });

            dialogRef.afterClosed().subscribe(result => {
              if (result && this.requestId) {
                this.assignDoctorToPatient(result.email);
              }
            });
          })
          .catch(error => {
            console.error('Error loading doctor selection dialog:', error);
            this.showErrorNotification('Error opening doctor selection dialog.');
          });
      } else if (this.doctorsLoading) {
        // If still loading, wait a bit and try again
        setTimeout(checkDoctorsAndOpenDialog, 500);
      } else {
        // No doctors available and not loading
        this.showErrorNotification('No doctors available. Please try again later.');
      }
    };

    checkDoctorsAndOpenDialog();
  }

  assignDoctorToPatient(doctorEmail: string): void {
    if (!doctorEmail || !this.requestId) {
      this.showErrorNotification('Could not assign doctor. Missing doctor email or request ID.');
      return;
    }

    this.loading = true;
    // Use wound request service to assign doctor
    this._woundRequestService.changeDoctorByAssignedEmail(this.requestId, doctorEmail).subscribe({
      next: (res) => {
        console.log('Doctor assigned successfully:', res);

        // Update the patient request with the new doctor
        if (this.patientRequest) {
          this.patientRequest.assignedEmail = doctorEmail;
        }

        this.loading = false;
        this.showNotification('Doctor assigned successfully to patient request.');

        // Refresh data to get updated assigned doctor info
        this.LoadAllData(this.requestId as number);
      },
      error: (error) => {
        console.error('Error assigning doctor:', error);
        this.showErrorNotification('Failed to assign doctor. Please try again.');
        this.loading = false;
      }
    });
  }

  loadDailyRoutineData(patientNumber: number) {
    this.loading = true;

    this._dailyRoutieService.getByPatientId(patientNumber).subscribe({
      next: (res) => {
        console.log('Daily routine data:', res);
        this.dailyRoutineData = res || [];

        // Map daily routines to RoutineItem format
        if (this.dailyRoutineData && this.dailyRoutineData.length > 0) {
          // Update the patient request object with the dailyRoutine data
          if (this.patientRequest) {
            this.patientRequest.dailyRoutine = this.mapDailyRoutineToRoutineItem(this.dailyRoutineData);
          }

          // Extract unique days and set up filters
          this.extractUniqueDaysFromRoutineData();
          this.filterRoutineByDayFromData(this.selectedDay);
        } else {
          // No routines found, set empty arrays
          if (this.patientRequest) {
            this.patientRequest.dailyRoutine = [];
          }
          this.filteredRoutine = [];
          this.uniqueDays = [];
        }

        // Force change detection to update the UI
        this.filteredRoutine = [...this.filteredRoutine];
        this.uniqueDays = [...this.uniqueDays];

        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading daily routine data:', error);
        this.showErrorNotification('Failed to load daily routine data. Please refresh the page to try again.');

        // Reset arrays on error
        this.dailyRoutineData = [];
        if (this.patientRequest) {
          this.patientRequest.dailyRoutine = [];
        }
        this.filteredRoutine = [];
        this.uniqueDays = [];

        this.loading = false;
      }
    });
  }
  loadMedicineData(patientId: number) {
    this.loading = true;
    // Use getByPatientId from MedicineRoutineContollerServiceProxy to get patient-specific medicines
    this._medicineRoutineService.getByPatientId(patientId).subscribe({
      next: (res: MedicineRoutine[]) => {
        console.log('Medicine routine data:', res);

        // Convert MedicineRoutine[] to Medicine[] for compatibility
        this.medicineData = (res || []).map(routine => {
          const medicine = new Medicine();
          medicine.id = routine.id;
          medicine.name = routine.name || '';
          medicine.dosage = routine.dosage || '';
          medicine.description = routine.description || '';
          (medicine as any).frequency = routine.frequency || '';
          (medicine as any).timing = routine.timing || '';
          (medicine as any).patientId = routine.patientId;
          medicine.createdAt = routine.createdDate;
          return medicine;
        });

        // Map medicine data to the patient request object
        if (this.patientRequest) {
          this.patientRequest.medications = this.medicineData.map(med => ({
            name: med.name || '',
            dosage: med.dosage || '',
            frequency: (med as any).frequency || '',
            timing: (med as any).timing || '',
            description: med.description || '',
            id: med.id // Add id for CRUD operations
          }));
        }
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Error loading medicine data:', error);
        this.showErrorNotification('Failed to load medication data. Please refresh the page to try again.');
        this.medicineData = [];
        if (this.patientRequest) {
          this.patientRequest.medications = [];
        }
        this.loading = false;
      }
    });
  }

  loadRequestFiles(requestId: number): void {
    this.isLoadingFiles = true;

    // First check if we already have requestFiles from the backend response
    if (this.patientRequest?.requestFiles && this.patientRequest.requestFiles.length > 0) {
      this.requestFiles = this.patientRequest.requestFiles;
      console.log('Using request files from backend response:', this.requestFiles);
      this.processRequestFilesData();
      return;
    }

    // Fallback to separate API call if not included in response
    this._requestFilesService.getByRequest(requestId).subscribe({
      next: (files: RequestFileDto[]) => {
        console.log('Request files loaded via separate API:', files);
        this.requestFiles = files || [];
        this.processRequestFilesData();

        this.isLoadingFiles = false;
      },
      error: (error) => {
        console.error('Error loading request files:', error);
        this.requestFiles = [];
        this.requestImages = [];
        this.isLoadingFiles = false;

        // Show a subtle error message
        this.showErrorNotification('Could not load uploaded files. Some features may be limited.');
      }
    });
  }

  // Process request files data (separate images, create attachments, etc.)
  private processRequestFilesData(): void {
    // Separate images from other files
    this.requestImages = this.requestFiles.filter(file =>
      file.mimeType && file.mimeType.startsWith('image/')
    );

    // Separate non-image files
    this.requestDocuments = this.requestFiles.filter(file =>
      !file.mimeType || !file.mimeType.startsWith('image/')
    );

    // Update the legacy attachments array for compatibility
    if (this.patientRequest) {
      this.patientRequest.attachments = this.requestFiles.map((file, index) => {
        // Determine the best URL to use for the image
        let imageUrl = '';

        if (file.blobUrl) {
          imageUrl = file.blobUrl;
        } else if (file.thumbnailUrl) {
          imageUrl = file.thumbnailUrl;
        } else if (file.id) {
          // Fallback to download endpoint
          imageUrl = `${this.baseUrl}/api/RequestFiles/Download/${file.id}`;
        }

        console.log('Processing file for display:', {
          fileName: file.originalFileName,
          blobUrl: file.blobUrl,
          thumbnailUrl: file.thumbnailUrl,
          selectedUrl: imageUrl,
          fileId: file.id
        });

        return {
          id: index + 1,
          name: file.originalFileName || 'Untitled File',
          type: file.mimeType?.startsWith('image/') ? 'Image' : 'Document',
          size: this.formatFileSize(file.fileSize || 0),
          uploadDate: file.createdDate ? new Date(file.createdDate.toString()) : new Date(),
          url: imageUrl,
          description: file.description,
          aiAnalysis: file.aiAnalysis,
          medicalFindings: file.medicalFindings,
          requestFileData: file // Store the full RequestFileDto for the dialog
        };
      });

      // Categorize attachments by date
      this.categorizeAttachmentsByDate();
    }

    console.log('Request images:', this.requestImages);
    console.log('Request documents:', this.requestDocuments);
    console.log('Processed attachments:', this.patientRequest?.attachments);

    this.isLoadingFiles = false;
  }

  LoadAllData(requestId: number) {
    this._woundRequestService.getById(requestId).subscribe({
      next: (res: WoundRequestDto) => {
        console.log('Wound request data:', res);
        // Map the WoundRequestDto response to our component's data structure
        this.patientRequest = {
          id: res.id,
          message: res.message || 'No description provided',
          filesName: [],
          userEmail: res.userEmail || 'No email provided',
          status: res.status || 'Pending',
          createdDate: res.createdDate ? res.createdDate.toString() : new Date().toISOString(),
          completionDate: res.completionDate ? res.completionDate.toString() : undefined,
          priority: res.priority || 'Medium',
          subject: res.title || `Wound Request #${res.id}`,
          assignedEmail: res.assignedEmail,
          // Initialize empty arrays - these will be populated by separate service calls
          dailyRoutine: [],
          medications: [],
          attachments: []
        };
        this.loadRequestFiles(requestId);
      },
      error: (error) => {
        console.error('Error loading wound request data:', error);
        this.loading = false;
        this.showErrorNotification('Failed to load request data. Please try refreshing the page.');
      }
    });
    // Categorize attachments by date
    this.categorizeAttachmentsByDate();

    console.log('Wound request view component needs refactoring to work with WoundRequest model');
  }
  getFileType(fileName: string): string {
    if (!fileName) return 'Unknown';

    const extension = fileName.split('.').pop()?.toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension || '')) {
      return 'Image';
    } else if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(extension || '')) {
      return 'Document';
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension || '')) {
      return 'Video';
    } else if (['mp3', 'wav', 'ogg', 'aac', 'flac'].includes(extension || '')) {
      return 'Audio';
    }

    // Default to Document for consistency with upload component
    return 'Document';
  }
  // Download file attachment
  downloadAttachment(file: string) {
    // Ensure the file name is properly encoded for URLs
    const encodedFileName = encodeURIComponent(file);
    const url = `${this.baseUrl}/api/File/Getfile/${encodedFileName}`;
    console.log('Opening file URL:', url);
    window.open(url, '_blank');
  }

  // Open file info dialog with detailed information
  openFileInfoDialog(attachment: Attachment): void {
    if (attachment.requestFileData) {
      const dialogRef = this.dialog.open(FileInfoDialogComponent, {
        width: '800px',
        maxWidth: '90vw',
        maxHeight: '90vh',
        data: { file: attachment.requestFileData }
      });
    }
  }
  loadPatientRequest(id: number): void {
    // This method is now replaced by LoadAllData, but we keep it for backward compatibility
    // It will now just call LoadAllData
    this.LoadAllData(id);
  }

  // Fallback to demo data if API fails
  loadFallbackData(id: number): void {
    console.log('Loading fallback demo data');

    // Create demo data for display
    this.patientRequest = {
      id: id,
      message: 'Fever, Headache',
      filesName: ['xray_result.jpg', 'blood_test.pdf', 'medical_history.pdf'],
      userEmail: '<EMAIL>',
      status: 'Open',
      patientName: 'John Smith',
      patientEmail: '<EMAIL>',
      problems: 'Fever, Headache',
      requestDate: new Date(2025, 5, 10),
      lastUpdated: new Date(2025, 5, 12),
      age: 45,
      category: 'Outpatient',
      projectCategory: 'Outpatient',
      dailyRoutine: [
        { day: 'Monday', time: '08:00 AM', activity: 'Exercise', description: 'Light stretching and walking for 30 minutes' },
        { day: 'Monday', time: '12:00 PM', activity: 'Rest', description: 'Midday rest to reduce fatigue' },
        { day: 'Tuesday', time: '09:00 AM', activity: 'Physical Therapy', description: 'Attend physical therapy session' },
        { day: 'Wednesday', time: '08:00 AM', activity: 'Exercise', description: 'Light stretching and walking for 30 minutes' },
        { day: 'Thursday', time: '10:00 AM', activity: 'Doctor Appointment', description: 'Follow-up with Dr. Miller' },
        { day: 'Friday', time: '08:00 AM', activity: 'Exercise', description: 'Light stretching and walking for 30 minutes' },
        { day: 'Weekend', time: 'Any time', activity: 'Rest', description: 'Focus on rest and recovery' }
      ],
      medications: [
        { name: 'Paracetamol', dosage: '500mg', frequency: 'Every 6 hours', timing: 'After meals', description: 'For pain relief' },
        { name: 'Amoxicillin', dosage: '250mg', frequency: 'Every 8 hours', timing: 'With food', description: 'Antibiotic treatment' },
        { name: 'Vitamin D', dosage: '1000 IU', frequency: 'Once daily', timing: 'With breakfast', description: 'Supplement for bone health' },
        { name: 'Ibuprofen', dosage: '200mg', frequency: 'As needed', timing: 'With food', description: 'For inflammation and pain' }
      ],
      attachments: [
        { id: 1, name: 'xray_result.jpg', type: 'Image', size: '2.4 MB', uploadDate: new Date(2025, 5, 9), url: '/assets/images/xray_placeholder.jpg' },
        { id: 2, name: 'blood_test.pdf', type: 'Document', size: '1.2 MB', uploadDate: new Date(2025, 5, 10), url: '/assets/images/document_placeholder.jpg' },
        { id: 3, name: 'medical_history.pdf', type: 'Document', size: '3.8 MB', uploadDate: new Date(2025, 5, 8), url: '/assets/images/document_placeholder.jpg' }
      ]
    };

    // Extract unique days from routine and set initially filtered routine
    this.extractUniqueDays();
    this.filterRoutineByDay('All');

    // Categorize attachments by date
    this.categorizeAttachmentsByDate();
  }




  setActiveTab(tab: 'routine' | 'medication' | 'attachments'): void {
    this.activeTab = tab;
  }

  extractUniqueDays(): void {
    if (this.patientRequest && this.patientRequest.dailyRoutine) {
      // Create a Set to store unique days, then convert back to array
      const daysSet = new Set(this.patientRequest.dailyRoutine.map(item => item.day));
      this.uniqueDays = Array.from(daysSet);
    }
  }

  // Method to handle day filter button click in the UI
  filterRoutineByDay(day: string): void {
    this.selectedDay = day;
    this.filterRoutineByDayFromData(day);
  }

  // Helper method to filter routine data by selected day
  filterRoutineByDayFromData(day: string): void {
    this.selectedDay = day;

    if (!this.dailyRoutineData || this.dailyRoutineData.length === 0) {
      this.filteredRoutine = [];
      return;
    }

    // Filter routines based on selected day
    if (day === 'All') {
      this.filteredRoutine = this.mapDailyRoutineToRoutineItem([...this.dailyRoutineData]);
    } else {
      this.filteredRoutine = this.mapDailyRoutineToRoutineItem(
        this.dailyRoutineData.filter(item => item.day === day)
      );
    }

    // Sort the filtered routines by time
    this.sortRoutinesByTime();

    // Force change detection to update the UI
    this.filteredRoutine = [...this.filteredRoutine];
  }

  // Helper method to sort routines by time
  sortRoutinesByTime(): void {
    this.filteredRoutine.sort((a, b) => {
      // Convert time strings to comparable values (assuming format like "08:00 AM")
      const timeA = this.timeToMinutes(a.time || '');
      const timeB = this.timeToMinutes(b.time || '');
      return timeA - timeB;
    });
  }

  // Helper method to convert time string to minutes for sorting
  timeToMinutes(timeStr: string): number {
    if (!timeStr) return 0;

    try {
      // Extract hours, minutes, and period (AM/PM)
      const match = timeStr.match(/(\d+):(\d+)\s*([AP]M)?/i);
      if (!match) return 0;

      let hours = parseInt(match[1]);
      const minutes = parseInt(match[2]);
      const period = match[3] ? match[3].toUpperCase() : null;

      // Convert to 24-hour format
      if (period === 'PM' && hours < 12) {
        hours += 12;
      } else if (period === 'AM' && hours === 12) {
        hours = 0;
      }

      return hours * 60 + minutes;
    } catch (e) {
      console.error('Error parsing time:', e);
      return 0;
    }
  }

  // Helper method to map DailyRoutine to RoutineItem
  mapDailyRoutineToRoutineItem(routines: DailyRoutine[]): RoutineItem[] {
    return routines.map(routine => ({
      day: routine.day || '',
      time: routine.time || '',
      activity: routine.activity || '',
      description: routine.description || ''
    }));
  }

  extractUniqueDaysFromRoutineData(): void {
    if (this.dailyRoutineData && this.dailyRoutineData.length > 0) {
      // Create a Set to store unique days, then convert back to array
      const daysSet = new Set(this.dailyRoutineData.map(item => item.day || '').filter(day => day !== ''));
      const uniqueDaysArray = Array.from(daysSet);

      // Define the order of days
      const dayOrder = {
        'Monday': 1,
        'Tuesday': 2,
        'Wednesday': 3,
        'Thursday': 4,
        'Friday': 5,
        'Saturday': 6,
        'Sunday': 7,
        'Weekend': 8,
        'Daily': 9
      };

      // Sort days according to the defined order
      this.uniqueDays = uniqueDaysArray.sort((a, b) => {
        const orderA = dayOrder[a as keyof typeof dayOrder] || 100;
        const orderB = dayOrder[b as keyof typeof dayOrder] || 100;
        return orderA - orderB;
      });
    } else {
      this.uniqueDays = [];
    }
  }






  getUnreadCount(): number {
    return this.chatMessages.filter(msg => !msg.read && msg.sender !== 'doctor').length;
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Pending':
        return 'bg-amber-100 text-amber-800';
      case 'Recovered':
        return 'bg-green-100 text-green-800';
      case 'Critical':
        return 'bg-red-100 text-red-800';
      case 'In Treatment':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  openAddRoutineDialog(): void {
    const dialogRef = this.dialog.open(RoutineFormComponent, {
      width: '500px',
      data: {

      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Show loading indicator
        this.loading = true;

        const data: DailyRoutine = {
          id: "00000000-0000-0000-0000-000000000000", // or assign a proper id if available
          day: result.day || '',
          time: result.time || '',
          activity: result.activity || '',
          description: result.description || '',
          patientId: this.requestId,
          createdDate: DateTime.now(), // Assuming you are using luxon for DateTime
        } as DailyRoutine;

        this._dailyRoutieService.createOrUpdatePatient(data).subscribe({
          next: (res) => {
            console.log('Routine added successfully:', res);

            // Add the new routine to our local array
            this.dailyRoutineData.push(res);

            // Update the UI
            if (this.patientRequest) {
              // Update the daily routine array in patientRequest
              this.patientRequest.dailyRoutine = this.mapDailyRoutineToRoutineItem(this.dailyRoutineData);

              // Update day filters and filtered list
              this.extractUniqueDaysFromRoutineData();
              this.filterRoutineByDayFromData(this.selectedDay);
            }

            this.loading = false;
            this.showNotification('Routine added successfully');
          },
          error: (error) => {
            console.error('Error adding routine:', error);
            this.loading = false;
            this.showErrorNotification('Failed to add routine. Please try again.');
          }
        });
      }
    });
  }


  openEditRoutineDialog(routine: RoutineItem): void {
    // Find the corresponding DailyRoutine object in our data
    const dailyRoutineToEdit = this.dailyRoutineData.find(
      dr => dr.day === routine.day && dr.time === routine.time && dr.activity === routine.activity
    );

    if (!dailyRoutineToEdit) {
      console.error('Could not find matching daily routine in data');
      return;
    }

    const dialogRef = this.dialog.open(RoutineFormComponent, {
      width: '500px',
      data: { routine }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Show loading indicator
        this.loading = true;

        // Update the existing DailyRoutine object with new values
        const updatedRoutine = {
          ...dailyRoutineToEdit,
          day: result.day,
          time: result.time,
          activity: result.activity,
          description: result.description
        } as DailyRoutine;

        // Call the API to update the routine
        this._dailyRoutieService.createOrUpdatePatient(updatedRoutine).subscribe({
          next: (res) => {
            console.log('Routine updated successfully:', res);

            // Update the record in our local array
            const index = this.dailyRoutineData.findIndex(dr => dr.id === dailyRoutineToEdit.id);
            if (index !== -1) {
              this.dailyRoutineData[index] = res;
            }

            // Update the UI
            if (this.patientRequest) {
              // Update the daily routine array in patientRequest
              this.patientRequest.dailyRoutine = this.mapDailyRoutineToRoutineItem(this.dailyRoutineData);

              // Update day filters and filtered list
              this.extractUniqueDaysFromRoutineData();
              this.filterRoutineByDayFromData(this.selectedDay);
            }

            this.loading = false;
            this.showNotification('Routine updated successfully');
          },
          error: (error) => {
            console.error('Error updating routine:', error);
            this.loading = false;
            this.showErrorNotification('Failed to update routine. Please try again.');
          }
        });
      }
    });
  }
  deleteRoutine(routine: RoutineItem): void {
    // Find the corresponding DailyRoutine object in our data
    const dailyRoutineToDelete = this.dailyRoutineData.find(
      dr => dr.day === routine.day && dr.time === routine.time && dr.activity === routine.activity
    );

    if (!dailyRoutineToDelete) {
      console.error('Could not find matching daily routine in data');
      return;
    }

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Routine',
        message: 'Are you sure you want to delete this routine item?',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'warning',
        itemName: `${routine.activity} (${routine.day}, ${routine.time})`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Show loading indicator
        this.loading = true;

        // Call the API to delete the routine
        this._dailyRoutieService.delete(dailyRoutineToDelete.id).subscribe({
          next: (res) => {
            console.log('Routine deleted successfully:', res);

            // Remove the item from our local array
            const index = this.dailyRoutineData.findIndex(dr => dr.id === dailyRoutineToDelete.id);
            if (index !== -1) {
              this.dailyRoutineData.splice(index, 1);
            }

            // Update the UI
            if (this.patientRequest) {
              // Update the daily routine array in patientRequest
              this.patientRequest.dailyRoutine = this.mapDailyRoutineToRoutineItem(this.dailyRoutineData);

              // Update day filters and filtered list
              this.extractUniqueDaysFromRoutineData();
              this.filterRoutineByDayFromData(this.selectedDay);
            }

            this.loading = false;
            this.showNotification('Routine deleted successfully');
          },
          error: (error) => {
            console.error('Error deleting routine:', error);
            this.loading = false;
            this.showErrorNotification('Failed to delete routine. Please try again.');
          }
        });
      }
    });
  }

  openAddMedicationDialog(): void {
    const patientEmail = this.patientRequest?.patientEmail || this.patientRequest?.userEmail || '';
    console.log('Email to pass to dialog:', patientEmail);

    const dialogRef = this.dialog.open(MedicationFormComponent, {
      width: '500px',
      data: {
        patientEmail: patientEmail
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.requestId) {        // Create a new MedicineRoutine object to save to the API

        const newMedicineRoutine = new MedicineRoutine();
        newMedicineRoutine.id = "00000000-0000-0000-0000-000000000000"; // Empty GUID for creating new medicine routine
        newMedicineRoutine.name = result.name;
        newMedicineRoutine.dosage = result.dosage;
        newMedicineRoutine.frequency = result.frequency;
        newMedicineRoutine.timing = result.timing;
        newMedicineRoutine.description = result.description;
        newMedicineRoutine.patientId = this.requestId || 0;
        newMedicineRoutine.createdDate = DateTime.now(); // Add the current date and time

        this.loading = true;
        // Use createOrUpdateMedicine from MedicineRoutineContollerServiceProxy
        this._medicineRoutineService.createOrUpdateMedicine(newMedicineRoutine).subscribe({
          next: (response: any) => {
            console.log('Medication created:', response);

            // Since the API doesn't return the created object, create a Medicine object to add to the array
            const createdMedicine = new Medicine();
            // Generate a new GUID for the created medicine (this will be replaced by the actual GUID from the server)
            createdMedicine.id = this.generateGuid();
            createdMedicine.name = newMedicineRoutine.name || '';
            createdMedicine.dosage = newMedicineRoutine.dosage || '';
            createdMedicine.description = newMedicineRoutine.description || '';
            (createdMedicine as any).frequency = newMedicineRoutine.frequency;
            (createdMedicine as any).timing = newMedicineRoutine.timing;
            (createdMedicine as any).patientId = newMedicineRoutine.patientId;
            createdMedicine.createdAt = newMedicineRoutine.createdDate;

            // Add the new medication to the medicineData array
            this.medicineData.push(createdMedicine);


            // Update the patient request medications display
            if (this.patientRequest) {
              this.patientRequest.medications = this.medicineData.map(med => ({
                name: med.name || '',
                dosage: med.dosage || '',
                frequency: (med as any).frequency || '',
                timing: (med as any).timing || '',
                description: med.description || '',
                id: med.id // Add id for CRUD operations
              }));
            }

            this.loading = false;
            this.showNotification('Medication has been added successfully.');
          },
          error: (error: any) => {
            console.error('Error creating medication:', error);
            this.showErrorNotification('Failed to create medication. Please try again.');
            this.loading = false;
          }
        });
      }
    });
  }

  openEditMedicationDialog(medicine: Medicine): void {
    // Convert Medicine to Medication format for the dialog
    const medication: Medication = {
      id: medicine.id,
      name: medicine.name || '',
      dosage: medicine.dosage || '',
      frequency: (medicine as any).frequency || '',
      timing: (medicine as any).timing || '',
      description: medicine.description || ''
    };

    const dialogRef = this.dialog.open(MedicationFormComponent, {
      width: '500px',
      data: {
        medication,
        patientEmail: this.patientRequest?.patientEmail || this.patientRequest?.userEmail || ''
      }
    }); dialogRef.afterClosed().subscribe(result => {
      if (result && medicine.id) {
        // Create a MedicineRoutine object for the API
        const updatedMedicineRoutine = new MedicineRoutine();
        updatedMedicineRoutine.id = medicine.id!; // Use actual GUID for updating existing medicine routine
        updatedMedicineRoutine.name = result.name;
        updatedMedicineRoutine.dosage = result.dosage;
        updatedMedicineRoutine.frequency = result.frequency;
        updatedMedicineRoutine.timing = result.timing;
        updatedMedicineRoutine.description = result.description;
        updatedMedicineRoutine.patientId = this.requestId || 0;
        updatedMedicineRoutine.createdDate = DateTime.now(); // Add the current date and time

        this.loading = true;
        // Use createOrUpdateMedicine from MedicineRoutineContollerServiceProxy
        this._medicineRoutineService.createOrUpdateMedicine(updatedMedicineRoutine).subscribe({
          next: (response: any) => {
            console.log('Medication updated:', response);

            // Find and update the medication in the medicineData array
            const index = this.medicineData.findIndex(
              item => item.id === medicine.id
            );

            if (index !== -1) {
              // Update the medication with the new data since API doesn't return updated object
              this.medicineData[index].name = updatedMedicineRoutine.name || '';
              this.medicineData[index].dosage = updatedMedicineRoutine.dosage || '';
              this.medicineData[index].description = updatedMedicineRoutine.description || '';
              (this.medicineData[index] as any).frequency = updatedMedicineRoutine.frequency || '';
              (this.medicineData[index] as any).timing = updatedMedicineRoutine.timing || '';
              (this.medicineData[index] as any).patientId = updatedMedicineRoutine.patientId;
            }

            // Update the patient request medications display
            if (this.patientRequest) {
              this.patientRequest.medications = this.medicineData.map(med => ({
                name: med.name || '',
                dosage: med.dosage || '',
                frequency: (med as any).frequency || '',
                timing: (med as any).timing || '',
                description: med.description || '',
                id: med.id // Add id for CRUD operations
              }));
            }

            this.loading = false;
            this.showNotification('Medication has been updated successfully.');
          },
          error: (error: any) => {
            console.error('Error updating medication:', error);
            this.showErrorNotification('Failed to update medication. Please try again.');
            this.loading = false;
          }
        });
      }
    });
  }
  deleteMedication(medicine: Medicine): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Medication',
        message: 'Are you sure you want to delete this medication?',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger',
        itemName: `${medicine.name || 'Unknown'} (${medicine.dosage || 'No dosage'})`
      }
    }); dialogRef.afterClosed().subscribe(result => {
      if (result && medicine.id) {
        this.loading = true;

        // Call the API to delete the medication
        // Use deleteMedicine from MedicineRoutineContollerServiceProxy
        this._medicineRoutineService.deleteMedicine(medicine.id!).subscribe({
          next: () => {
            console.log('Medication deleted successfully');

            // Find and remove the medication from the medicineData array
            const index = this.medicineData.findIndex(
              item => item.id === medicine.id
            );

            if (index !== -1) {
              // Remove the medication from the list
              this.medicineData.splice(index, 1);
            }

            // Update the patient request medications display
            if (this.patientRequest) {
              this.patientRequest.medications = this.medicineData.map(med => ({
                name: med.name || '',
                dosage: med.dosage || '',
                frequency: (med as any).frequency || '',
                timing: (med as any).timing || '',
                description: med.description || '',
                id: med.id // Add id for CRUD operations
              }));
            }

            this.loading = false;
            this.showNotification('Medication deleted successfully.');
          },
          error: (error: any) => {
            console.error('Error deleting medication:', error);
            this.showErrorNotification('Failed to delete medication. Please try again.');
            this.loading = false;
          }
        });
      }
    });
  }

  openAttachmentUploadDialog(): void {
    const dialogRef = this.dialog.open(AttachmentUploadComponent, {
      width: '550px',
      data: {
        projectId: this.patientRequest?.id // Pass the project ID to the dialog
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        console.log('File upload completed successfully:', result);

        // Reload request files to get the latest data with AI analysis
        if (this.requestId) {
          this.loadRequestFiles(this.requestId);
        }

        // Show success notification
        this.showNotification('File uploaded successfully with AI analysis!');
      }
    });


  }

  deleteAttachment(attachmentId: number): void {
    const attachmentToDelete = this.patientRequest?.attachments?.find(item => item.id === attachmentId);

    if (!attachmentToDelete) return;

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Document',
        message: 'Are you sure you want to delete this document?',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger',
        itemName: attachmentToDelete.name
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.patientRequest && this.patientRequest.attachments) {
        // Show loading state
        this.loading = true;

        // Call the API to delete the file
        this._fileService.deleteFile([attachmentToDelete.name]).subscribe({
          next: (res) => {
            console.log('File deleted successfully:', res);

            // Find the index of the attachment to delete
            const index = this.patientRequest!.attachments!.findIndex(item => item.id === attachmentId);

            if (index !== -1) {
              // Remove the attachment from UI
              this.patientRequest!.attachments!.splice(index, 1);

              // Update the categorization
              this.categorizeAttachmentsByDate();
            }

            // Update the wound request on the server
            if (this.patientRequest?.id) {
              // Create a WoundRequestDto object to send to the API
              const woundRequestDto = new WoundRequestDto();
              woundRequestDto.id = this.patientRequest.id;
              woundRequestDto.message = this.patientRequest.message;
              woundRequestDto.title = this.patientRequest.subject;
              woundRequestDto.priority = this.patientRequest.priority;
              woundRequestDto.userEmail = this.patientRequest.userEmail;
              woundRequestDto.status = this.patientRequest.status;

              // Update the fileNames array to include all REMAINING attachments
              woundRequestDto.fileNames = this.patientRequest.attachments?.map(a => a.name).join(',') || '';

              // Call the API to update the wound request
              this._woundRequestService.createOrUpdate(woundRequestDto).subscribe({
                next: (response) => {
                  console.log('Project updated successfully after file deletion:', response);
                  this.showNotification('Document deleted successfully.');
                  this.loading = false;
                },
                error: (error) => {
                  console.error('Error updating project after file deletion:', error);
                  this.loading = false;
                }
              });
            } else {
              this.loading = false;
            }
          },
          error: (error) => {
            console.error('Error deleting file:', error);

            this.loading = false;
          }
        });
      }
    });
  }

  /**
   * Categorizes attachments by their upload date
   */
  categorizeAttachmentsByDate(): void {
    this.attachmentsByDate = {};
    this.attachmentDateKeys = [];

    // Use RequestFiles data if available, otherwise fall back to attachments
    const filesToCategorize = this.requestFiles?.length > 0
      ? this.requestFiles.map((file, index) => ({
        id: index + 1, // Use index as number ID for compatibility
        name: file.originalFileName || 'Unknown file',
        type: this.isImageFile(file.mimeType || '') ? 'Image' : 'Document',
        size: this.formatFileSize(file.fileSize || 0),
        uploadDate: file.createdDate ? new Date(file.createdDate.toString()) : new Date(),
        url: file.blobUrl || file.thumbnailUrl || '',
        description: file.description || '',
        aiAnalysis: file.aiAnalysis || '',
        medicalFindings: file.medicalFindings || '',
        requestFileData: file
      } as any)) // Type assertion to bypass strict type checking
      : this.patientRequest?.attachments || [];

    if (!filesToCategorize.length) {
      return;
    }

    // Group attachments by date
    filesToCategorize.forEach(attachment => {
      const dateKey = this.formatDateKey(attachment.uploadDate);

      if (!this.attachmentsByDate[dateKey]) {
        this.attachmentsByDate[dateKey] = [];
        this.attachmentDateKeys.push(dateKey);
      }

      this.attachmentsByDate[dateKey].push(attachment);
    });

    // Sort date keys in descending order (newest first)
    this.attachmentDateKeys.sort((a, b) => {
      const dateA = this.parseDateKey(a);
      const dateB = this.parseDateKey(b);
      return dateB.getTime() - dateA.getTime();
    });

    console.log('Categorized attachments by date:', this.attachmentsByDate);
    console.log('Date keys:', this.attachmentDateKeys);
  }

  /**
   * Format date to consistent string key format (same as MobileApp)
   */
  private formatDateKey(date: Date): string {
    if (!date) return 'No Date';

    const d = new Date(date);

    // Use local date components to avoid timezone issues
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`; // YYYY-MM-DD format in local timezone
  }

  /**
   * Format date for user-friendly display (same as MobileApp)
   */
  formatDateForDisplay(dateKey: string): string {
    if (!dateKey || dateKey === 'No Date') return 'No Date';

    // Parse the date key (YYYY-MM-DD format)
    const date = new Date(dateKey + 'T00:00:00'); // Add time to avoid timezone issues
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    // Get date strings in YYYY-MM-DD format for comparison
    const dateStr = date.toISOString().split('T')[0];
    const todayStr = today.toISOString().split('T')[0];
    const yesterdayStr = yesterday.toISOString().split('T')[0];

    if (dateStr === todayStr) {
      return 'Today';
    } else if (dateStr === yesterdayStr) {
      return 'Yesterday';
    } else {
      // Format as "July 12" or "July 12, 2024" if different year
      const options: Intl.DateTimeFormatOptions = {
        month: 'long',
        day: 'numeric'
      };

      if (date.getFullYear() !== today.getFullYear()) {
        options.year = 'numeric';
      }

      return date.toLocaleDateString('en-US', options);
    }
  }

  /**
   * Parse date from string key (same as MobileApp)
   */
  private parseDateKey(dateKey: string): Date {
    return new Date(dateKey);
  }

  /**
   * Format file size in bytes to human readable format
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }



  /**
   * Shows a success notification to the user using styled toast
   */
  private showNotification(message: string): void {
    this.globalMessageService.showGeneralNotification(message, 'success');
  }

  /**
   * Shows an error notification to the user using styled toast
   */
  private showErrorNotification(message: string): void {
    this.globalMessageService.showGeneralNotification(message, 'error');
  }
  /**
   * Handle image loading errors
   */
  onImageError(event: Event, file: Attachment): void {
    // Log the error
    console.error('Failed to load image:', file.url);

    const imgElement = event.target as HTMLImageElement;

    // Find the corresponding RequestFile to get alternative URLs
    const requestFile = this.requestFiles.find(rf =>
      rf.originalFileName === file.name || rf.blobFileName === file.name
    );

    if (requestFile) {
      // Try alternative URLs from Azure storage
      if (file.url === requestFile.blobUrl && requestFile.thumbnailUrl) {
        console.log('Trying thumbnail URL as fallback');
        imgElement.src = requestFile.thumbnailUrl;
        return;
      } else if (file.url === requestFile.thumbnailUrl && requestFile.blobUrl) {
        console.log('Trying blob URL as fallback');
        imgElement.src = requestFile.blobUrl;
        return;
      } else if (requestFile.id) {
        // Try download endpoint as last resort
        console.log('Trying download endpoint as fallback');
        imgElement.src = `${this.baseUrl}/api/RequestFiles/Download/${requestFile.id}`;
        return;
      }
    }

    // If all else fails, show a medical placeholder
    console.log('All image URLs failed, showing placeholder');
    imgElement.src = '/assets/images/medical-placeholder.jpg';
  }

  /**
   * Handle successful image loading
   */
  onImageLoad(event: Event): void {
    // Hide the loading indicator when image loads successfully
    const imgElement = event.target as HTMLImageElement;
    const loadingElement = imgElement.previousElementSibling;

    if (loadingElement && loadingElement.classList.contains('image-loading')) {
      loadingElement.classList.add('hidden');
    }

    // Log successful image load
    console.log('Image loaded successfully:', imgElement.src);
  }

  /**
   * Open image preview in a larger view
   */
  openImagePreview(file: Attachment): void {
    if (file.type === 'Image' && file.url) {
      // For now, open in a new tab. Later can be enhanced with a modal
      window.open(file.url, '_blank');
    }
  }



  /**
   * Check if a file is an image based on MIME type
   */
  private isImageFile(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  /**
   * Generate a new GUID
   */
  private generateGuid(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // Status update method
  updateRequestStatus(event: Event): void {
    console.log('updateRequestStatus called');
    const target = event.target as HTMLSelectElement;
    console.log('Target value:', target?.value);
    console.log('Patient request:', this.patientRequest);

    if (target && target.value && this.patientRequest) {
      const newStatus = target.value;
      const oldStatus = this.patientRequest.status; // Store old status for rollback

      console.log('Updating status from', oldStatus, 'to', newStatus);

      // Call the API to update the status
      this._woundRequestService.changeStatus(newStatus, this.patientRequest.id).subscribe({
        next: (response) => {
          console.log('Status update API success:', response);
          // Update the local request object
          this.patientRequest!.status = newStatus;

          // Show success notification
          this.showNotification(`Request status successfully updated to "${newStatus}"`);

          console.log('Status updated successfully:', response);
        },
        error: (error) => {
          console.error('Error updating status:', error);

          // Show error notification
          this.showErrorNotification('Failed to update request status. Please try again.');

          // Reset the dropdown to the original value
          target.value = oldStatus;
        }
      });
    } else {
      console.log('Missing required data for status update');
      this.showErrorNotification('Unable to update status - missing data');
    }
  }



  // Helper methods for template
  getMedicineFrequency(medicine: Medicine): string {
    return (medicine as any).frequency || '';
  }

  getMedicineTiming(medicine: Medicine): string {
    return (medicine as any).timing || '';
  }



  // Utility method to clean and format medical findings
  formatMedicalFindings(findings: string): string {
    if (!findings) return 'No medical findings available';

    // Remove the template text and extract actual findings
    let cleaned = findings
      .replace(/FINDINGS:\s*-\s*Primary observations:\s*/gi, '')
      .replace(/\s*-\s*Secondary observations:.*$/gi, '')
      .replace(/ASSESSMENT:\s*-\s*Classification:.*$/gi, '')
      .replace(/KEYWORDS:.*$/gi, '')
      .replace(/Full Analysis:.*$/gi, '')
      .replace(/Could not generate description for this file\.\.\.*/gi, 'Analysis pending')
      .replace(/\r\n/g, '\n')
      .replace(/\n+/g, '\n')
      .trim();

    return cleaned || 'Analysis pending';
  }

  // Utility method to clean and format AI analysis
  formatAIAnalysis(analysis: string): string {
    if (!analysis) return 'No AI analysis available';

    // Extract the actual analysis content
    let cleaned = analysis
      .replace(/Analysis Date:.*?\n/gi, '')
      .replace(/Medical Image Analysis Context:.*?documentation purposes only\s*/gi, '')
      .replace(/Patient identifier:.*?\n/gi, '')
      .replace(/Image Analysis:\s*/gi, '')
      .replace(/Could not generate description for this file/gi, 'Analysis pending')
      .replace(/\r\n/g, '\n')
      .replace(/\n+/g, '\n')
      .trim();

    return cleaned || 'Analysis pending';
  }

  /**
   * Check if there are any attachments (either old attachments or new requestFiles)
   */
  hasAnyAttachments(): boolean {
    const hasOldAttachments = (this.patientRequest?.attachments?.length ?? 0) > 0;
    const hasNewFiles = (this.requestFiles?.length ?? 0) > 0;
    const hasDateKeys = this.attachmentDateKeys.length > 0;

    console.log('Attachment Debug - hasOldAttachments:', hasOldAttachments);
    console.log('Attachment Debug - hasNewFiles:', hasNewFiles);
    console.log('Attachment Debug - hasDateKeys:', hasDateKeys);
    console.log('Attachment Debug - attachmentDateKeys:', this.attachmentDateKeys);

    return hasOldAttachments || hasNewFiles || hasDateKeys;
  }
}
