import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';

interface RoutineItem {
  day: string;
  time: string;
  activity: string;
  description: string;
}

@Component({
  selector: 'app-routine-form',
  templateUrl: './routine-form.component.html',
  styleUrls: ['./routine-form.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule
  ]
})
export class RoutineFormComponent implements OnInit {
  @ViewChild('routineForm') routineForm!: NgForm;

  routine: RoutineItem = {
    day: '',
    time: '',
    activity: '',
    description: ''
  };

  isEditMode = false;
  dialogTitle = 'Add Routine';

  // Days of the week for select dropdown
  dayOptions = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Weekend',
    'Daily'
  ];

  constructor(
    public dialogRef: MatDialogRef<RoutineFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { routine?: RoutineItem }
  ) {}

  ngOnInit(): void {
    if (this.data && this.data.routine) {
      this.isEditMode = true;
      this.dialogTitle = 'Update Routine';
      this.routine = {...this.data.routine};
    }
  }

  onSubmit(): void {
    if (this.routineForm.valid) {
      this.dialogRef.close(this.routine);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
