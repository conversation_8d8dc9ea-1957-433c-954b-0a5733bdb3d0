import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { WoundRequestServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from './confirmation-dialog/confirmation-dialog.component';
import { AuthService } from '../../../../shared/services/auth.service';
import { Subject, takeUntil } from 'rxjs';



@Component({
  selector: 'app-user-request',
  templateUrl: './user-request.component.html',
  styleUrls: ['./user-request.component.css'],
  standalone: true,
  imports: [
    TableModule,
    CommonModule,
    ButtonModule,
    RouterModule,
    FormsModule,
    TooltipModule,
    MatDialogModule,
    MatSnackBarModule,
  ],
})
export class UserRequestComponent implements OnInit, OnDestroy {
  viewType: 'table' | 'cards' = 'table';
  Math = Math;
  isLoading = false;
  isAdmin = false;
  isDoctor = false;
  patientRequests: any[] = [];
  filteredRequests: any[] = [];
  displayedRequests: any[] = [];
  activeFilter: 'all' | 'pending' | 'recovered' | 'progress' = 'all';
  searchTerm = '';
  currentPage = 1;
  pageSize = 5;
  totalRequests = 0;
  totalPages = 1;
  tablePageSizeOptions = [5, 10, 15, 20, 50];
  cardPageSizeOptions = [6, 12, 24, 48, 72];
  paginationRange: number[] = [];
  private destroy$ = new Subject<void>();
  stats = {
    total: 0,
    pending: 0,
    recovered: 0,
    inTreatment: 0,
    critical: 0,
    progressCount: 0,
    progressPercentage: 0,
  };

  constructor(
    private router: Router,
    private snackBar: MatSnackBar,
    private _woundRequestService: WoundRequestServiceProxy,
    private dialog: MatDialog,
    private authService: AuthService,
  ) { }
  ngOnInit(): void {
    this.isAdmin = this.authService.hasRole('Admin');
    this.isDoctor = this.authService.hasRole('Doctor');
    this.calculateStats();
    this.LoadAllData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private showSnackbar = (message: string, isError = false): void => {
    this.snackBar.open(message, 'Close', { duration: 3000, panelClass: isError ? 'error-snackbar' : 'success-snackbar' });
  }

  private showMessage = (
    severity: 'success' | 'error' | 'info' | 'warn',
    summary: string,
    detail: string
  ): void => {
    const isError = severity === 'error' || severity === 'warn';
    const fullMessage = `${summary}: ${detail}`;
    this.showSnackbar(fullMessage, isError);
  };

  LoadAllData = (): void => {
    this.isLoading = true;
    const apiCall = this.isAdmin
      ? this._woundRequestService.getAll()
      : this._woundRequestService.getAllForDoctor(
        this.authService.getUser()?.email
      );

    apiCall.pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.patientRequests = res.map((woundRequest) => ({
          id: woundRequest.id,
          subject: woundRequest.title || `Request #${woundRequest.id}`,
          patientEmail: woundRequest.userEmail || '',
          status: this.mapWoundStatusToPatientStatus(
            woundRequest.status || 'Pending'
          ),
          problems: woundRequest.message || '',
          requestDate: woundRequest.createdDate
            ? new Date(woundRequest.createdDate.toString())
            : new Date(),
          lastUpdated: woundRequest.completionDate
            ? new Date(woundRequest.completionDate.toString())
            : new Date(),
          filesName: woundRequest.fileNames
            ? woundRequest.fileNames.split(',')
            : [],
          priority: woundRequest.priority,
          assignedEmail: woundRequest.assignedEmail,
          projectCategory: 'Wound Care',
          requestFiles: [],
        }));
        this.filteredRequests = [...this.patientRequests];
        this.calculateStats();
        this.updatePagination();
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
        this.showMessage(
          'error',
          'Error',
          'Failed to load requests. Please try again later.'
        );
      },
    });
  };

  private mapWoundStatusToPatientStatus = (status: string): string => {
    const statusMap: Record<string, string> = {
      Pending: 'Pending',
      Completed: 'Recovered',
      Open: 'Pending',
      'In Progress': 'Progress',
    };
    return statusMap[status] || 'Pending';
  };

  mapPatientStatusToProjectStatus = (patientStatus: string): string => {
    const statusMap: Record<string, string> = {
      Pending: 'Pending',
      Progress: 'Progress',
      Recovered: 'Recovered',
    };
    return statusMap[patientStatus] || 'Pending';
  };

  private mapFrontendStatusToBackend = (frontendStatus: string): string => {
    const statusMap: Record<string, string> = {
      Pending: 'Open',
      Progress: 'In Progress',
      Recovered: 'Completed',
    };
    return statusMap[frontendStatus] || 'Open';
  };

  getCategoryClass = (category: string | undefined): string => {
    if (!category) return 'bg-gray-100 text-gray-600';
    const categoryClasses: Record<string, string> = {
      website: 'bg-blue-100 text-blue-800',
      web: 'bg-blue-100 text-blue-800',
      'web development': 'bg-blue-100 text-blue-800',
      design: 'bg-purple-100 text-purple-800',
      graphics: 'bg-purple-100 text-purple-800',
      'graphic design': 'bg-purple-100 text-purple-800',
      ai: 'bg-green-100 text-green-800',
      'machine learning': 'bg-green-100 text-green-800',
      'artificial intelligence': 'bg-green-100 text-green-800',
      marketing: 'bg-pink-100 text-pink-800',
      ad: 'bg-pink-100 text-pink-800',
      advertisement: 'bg-pink-100 text-pink-800',
    };
    return (
      categoryClasses[category.toLowerCase()] || 'bg-indigo-100 text-indigo-800'
    );
  };

  getPriorityClass = (priority: string | undefined): string => {
    if (!priority) return 'bg-gray-100 text-gray-600';
    const priorityClasses: Record<string, string> = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-amber-100 text-amber-800',
      low: 'bg-green-100 text-green-800',
    };
    return (
      priorityClasses[priority.toLowerCase()] || 'bg-blue-100 text-blue-800'
    );
  };

  getStatusClass = (status: string): string => {
    const statusClasses: Record<string, string> = {
      Pending: 'bg-amber-100 text-amber-800',
      Recovered: 'bg-green-100 text-green-800',
      Progress: 'bg-blue-100 text-blue-800',
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
  };

  private calculateStats = (): void => {
    const total = this.patientRequests.length;
    const pending = this.patientRequests.filter(
      (req) => req.status === 'Pending'
    ).length;
    const recovered = this.patientRequests.filter(
      (req) => req.status === 'Recovered'
    ).length;
    const progress = this.patientRequests.filter(
      (req) => req.status === 'Progress'
    ).length;
    const progressCount = progress + recovered;

    this.stats = {
      total,
      pending,
      recovered,
      inTreatment: progress,
      critical: 0,
      progressCount,
      progressPercentage:
        total > 0 ? Math.round((progressCount / total) * 100) : 0,
    };
  };

  applyFilters = (): void => {
    const term = this.searchTerm.toLowerCase();
    this.filteredRequests = this.patientRequests.filter((req) => {
      const statusMatch =
        this.activeFilter === 'all' ||
        (this.activeFilter === 'pending' && req.status === 'Pending') ||
        (this.activeFilter === 'recovered' && req.status === 'Recovered') ||
        (this.activeFilter === 'progress' && req.status === 'Progress');

      const searchMatch =
        !this.searchTerm ||
        [
          req.patientName,
          req.patientEmail,
          req.problems,
          req.projectCategory,
          req.priority,
        ].some((field) => field?.toLowerCase().includes(term));

      return statusMatch && searchMatch;
    });

    this.currentPage = 1;
    this.updatePagination();
  };

  private updatePagination = (): void => {
    this.totalRequests = this.filteredRequests.length;
    this.totalPages = Math.ceil(this.totalRequests / this.pageSize) || 1;

    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;
    this.calculatePaginationRange();
    this.updateDisplayedRequests();
  };

  private calculatePaginationRange = (): void => {
    const maxPages = 5,
      half = Math.floor(maxPages / 2);
    let start = Math.max(1, this.currentPage - half);
    let end = Math.min(this.totalPages, start + maxPages - 1);
    if (end - start < maxPages - 1) start = Math.max(1, end - maxPages + 1);
    this.paginationRange = Array.from(
      { length: end - start + 1 },
      (_, i) => start + i
    );
  };

  private updateDisplayedRequests = (): void => {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    this.displayedRequests = this.filteredRequests.slice(
      startIndex,
      startIndex + this.pageSize
    );
    console.log(`Showing ${this.displayedRequests.length} items (pageSize: ${this.pageSize})`); // Debug log
  };

  goToPage = (page: number): void => {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.updateDisplayedRequests();
      this.scrollToTop();
    }
  };

  previousPage = (): void => {
    if (this.currentPage > 1) this.goToPage(this.currentPage - 1);
  };
  nextPage = (): void => {
    if (this.currentPage < this.totalPages) this.goToPage(this.currentPage + 1);
  };

  onPageSizeChange = (event: Event): void => {
    this.pageSize = Number((event.target as HTMLSelectElement).value);
    this.currentPage = 1;
    this.updatePagination();
    this.updateDisplayedRequests();
  };

  private scrollToTop = (): void => {
    document
      .querySelector('.bg-white.rounded-xl.shadow-sm.overflow-hidden')
      ?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  setFilter = (filter: 'all' | 'pending' | 'recovered' | 'progress'): void => {
    this.activeFilter = filter;
    this.applyFilters();
  };
  onSearch = (term: string): void => {
    this.searchTerm = term;
    this.applyFilters();
  };
  viewRequest = (id: number): void => {
    this.router.navigateByUrl(`/request/${id}`);
  };
  editRequest = (id: number): void => {
    console.log(`Editing request ${id}`);
  };
  toggleView(viewType: 'table' | 'cards'): void {
    this.viewType = viewType;
    this.pageSize = viewType === 'table' ? 5 : 6;
    this.currentPage = 1;
    this.updatePagination();
  }

  deleteRequest(id: number): void {
    // Find the request to get details for the confirmation dialog
    const requestToDelete = this.patientRequests.find((req) => req.id === id);
    if (!requestToDelete) return;

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        type: 'danger',
        title: 'Delete Request',
        message:
          'Are you sure you want to delete this request? This action cannot be undone.',
        itemName: `Request #${id} - ${requestToDelete.patientName || 'Untitled'
          }`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // User confirmed deletion
        this.isLoading = true;
        this._woundRequestService
          .delete(id)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: () => {
              this.patientRequests = this.patientRequests.filter(
                (req) => req.id !== id
              );
              this.applyFilters();
              this.calculateStats();
              this.showMessage(
                'success',
                'Success',
                'Request deleted successfully'
              );
              this.isLoading = false;
            },
            error: () => {
              this.isLoading = false;
              this.showMessage(
                'error',
                'Error',
                'Failed to delete request. Please try again.'
              );
            },
          });
      }
    });
  }

  viewAttachments = (request: any): void => {
    const hasFiles = request.filesName?.length > 0;
    this.showMessage(
      'info',
      hasFiles ? 'Attachments' : 'No Attachments',
      hasFiles
        ? `Files: ${request.filesName.join(', ')}`
        : 'This request has no attached files'
    );
  };

  // Status update methods
  updateRequestStatus(request: any, event: Event): void {
    console.log('updateRequestStatus called in user-request component');
    const target = event.target as HTMLSelectElement;
    console.log('Target:', target?.value, 'Request:', request);

    if (target && target.value) {
      const newStatus = target.value;
      const oldStatus = request.status; // Store old status for rollback

      console.log('Updating status from', oldStatus, 'to', newStatus);

      // Map frontend status to backend status
      const backendStatus = this.mapFrontendStatusToBackend(newStatus);
      console.log('Backend status:', backendStatus);

      this._woundRequestService
        .changeStatus(backendStatus, request.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            console.log('Status update successful');

            // Update the status locally using the frontend status
            request.status = newStatus;

            // Update the lastUpdated timestamp
            request.lastUpdated = new Date();

            // Update stats, filters, and pagination without API call
            this.calculateStats();
            this.applyFilters();

            // Show enhanced success toaster with contextual information
            // const statusMessage = this.getStatusUpdateMessage(newStatus);
            this.showMessage(
              'success',
              'Status Updated Successfully',
              `${request.patientName || 'Patient'}'s request status changed to "${newStatus}"`
            );

            console.log('Success toast should be visible now');
          },
          error: (error) => {
            console.error('Error updating status:', error);

            // Show enhanced error toaster
            this.showMessage(
              'error',
              'Status Update Failed',
              `Unable to update ${request.patientName || 'patient'}'s request status. Please check your connection and try again.`
            );

            console.log('Error toast should be visible now');

            // Rollback to previous status on error
            target.value = oldStatus;
          },
        });
    } else {
      console.log('Missing target or value for status update');
    }
  }

  getFirstImageUrl = (request: any): string => {
    const imageFile = request.requestFiles?.find(
      (file: any) =>
        file.mimeType?.startsWith('image/') &&
        (file.blobUrl || file.thumbnailUrl)
    );
    return (
      imageFile?.blobUrl ||
      imageFile?.thumbnailUrl ||
      '/assets/images/medical-placeholder.jpg'
    );
  };

  getFileCount = (request: any): number =>
    request.requestFiles?.length || request.filesName?.length || 0;

  getImageCount = (request: any): number =>
    request.requestFiles?.filter((file: any) =>
      file.mimeType?.startsWith('image/')
    ).length || 0;

  formatDateForDisplay = (date: Date): string => {
    if (!date) return 'No Date';
    const d = new Date(date),
      today = new Date();
    const dateStr = d.toISOString().split('T')[0];
    const todayStr = today.toISOString().split('T')[0];
    const yesterdayStr = new Date(today.getTime() - 86400000)
      .toISOString()
      .split('T')[0];

    if (dateStr === todayStr) return 'Today';
    if (dateStr === yesterdayStr) return 'Yesterday';

    const options: Intl.DateTimeFormatOptions = {
      month: 'long',
      day: 'numeric',
    };
    if (d.getFullYear() !== today.getFullYear()) options.year = 'numeric';
    return d.toLocaleDateString('en-US', options);
  };

  onImageError = (event: any): void => {
    const target = event.target as HTMLImageElement;
    if (target) target.src = '/assets/images/medical-placeholder.jpg';
  };

  getPageSizeOptions(): number[] {
    return this.viewType === 'table' ? [5, 10, 15, 20, 50] : [6, 12, 18, 24, 48, 72];
  }

  // Enhanced test method for MatSnackBar toaster
  testToaster(): void {
    console.log('🧪 Testing enhanced MatSnackBar toaster...');

    // Test all notification types with enhanced styling
    const notifications = [
      { message: '✅ Operation Successful: Patient request has been processed successfully', isError: false },
      { message: '💡 System Information: New notifications now feature enhanced design', isError: false },
      { message: '⚠️ Important Notice: Please review the updated patient guidelines', isError: true },
      { message: '❌ Attention Required: Unable to process request. Please verify all fields', isError: true }
    ];

    // Display notifications with staggered timing
    notifications.forEach((notification, index) => {
      setTimeout(() => {
        this.showSnackbar(notification.message, notification.isError);
      }, index * 800);
    });

    console.log('✅ All MatSnackBar test notifications queued successfully');
  }

  /**
   * Get contextual message for status updates
   */
  // private getStatusUpdateMessage(status: string): string {
  //   const messages: { [key: string]: string } = {
  //     'Open': 'The request is now available for review and assignment.',
  //     'In Progress': 'Treatment has been initiated and is being actively monitored.',
  //     'Completed': 'All required treatments have been successfully completed.',
  //     'Critical': 'This request requires immediate attention and priority handling.',
  //     'Pending': 'The request is awaiting additional information or approval.',
  //     'Cancelled': 'The request has been cancelled and will not proceed.'
  //   };

  //   return messages[status] || 'Status has been updated according to current workflow.';
  // }
}
