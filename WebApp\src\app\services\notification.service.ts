import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig, MatSnackBarRef } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private readonly defaultConfig: MatSnackBarConfig = {
    duration: 5000,
    horizontalPosition: 'right',
    verticalPosition: 'top'
  };

  constructor(private snackBar: MatSnackBar) { }

  showSuccess(message: string): void {
    this.showNotification(message, 'success-snackbar');
  }

  showError(message: string): void {
    this.showNotification(message, 'error-snackbar');
  }

  showWarning(message: string): void {
    this.showNotification(message, 'warning-snackbar');
  }

  showInfo(message: string): void {
    this.showNotification(message, 'info-snackbar');
  }

  /**
   * Show message notification toast
   */
  showMessageNotification(senderName: string, messagePreview: string, action?: string): MatSnackBarRef<any> {
    const message = `${senderName}: ${messagePreview}`;
    return this.snackBar.open(message, action || 'View', {
      ...this.defaultConfig,
      duration: 7000, // Longer duration for message notifications
      panelClass: ['message-notification-snackbar']
    });
  }

  private showNotification(message: string, panelClass: string): void {
    this.snackBar.open(message, 'Close', {
      ...this.defaultConfig,
      panelClass: [panelClass]
    });
  }
}
