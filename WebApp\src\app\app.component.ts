import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ThemeService } from './shared/services/theme.service';
import { OnlineStatusService } from './services/online-status.service';
import { StyledToastComponent } from './components/styled-toast/styled-toast.component';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, MatSlideToggleModule, StyledToastComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit {
  title = 'webApp';

  constructor(
    private themeService: ThemeService,
    private onlineStatusService: OnlineStatusService
  ) { }

  ngOnInit(): void {
    // Theme service will automatically initialize from constructor
    // This ensures dark mode classes are applied on app start

    // Initialize online status service - it will auto-connect when user is authenticated
    console.log('🚀 AppComponent: Initializing OnlineStatusService');
  }
}
