/* Dialog Container Styling */
:host ::ng-deep .mat-mdc-dialog-container {
  padding: 0;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

.dialog-header {
  background: linear-gradient(135deg, #0891b2, #0284c7);
  color: white;
  padding: 1.25rem 1.5rem;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dialog-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.dialog-content {
  padding: 1.5rem;
  max-height: 65vh;
  overflow-y: auto;
  background-color: #ffffff;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

/* Form Styling */
.attachment-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #1e293b;
  font-size: 0.875rem;
}

.input-container, .custom-select-container, .textarea-container {
  position: relative;
  width: 100%;
}

.custom-input, .custom-select, .custom-textarea {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.2s;
  background-color: white;
  color: #1e293b;
}

.custom-textarea {
  padding: 0.75rem 1rem;
  resize: vertical;
  min-height: 6rem;
}

.custom-input:focus, .custom-select:focus, .custom-textarea:focus {
  outline: none;
  border-color: #0284c7;
  box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.1);
}

.custom-input.invalid, .custom-select.invalid, .custom-textarea.invalid {
  border-color: #ef4444;
}

.input-icon, .select-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0.75rem;
  color: #64748b;
  pointer-events: none;
}

.select-icon {
  right: 0.75rem;
  left: auto;
}

.error-message {
  font-size: 0.8rem;
  color: #ef4444;
  margin-top: 0.25rem;
}

/* File Upload Styling */
.file-upload-section {
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

/* .file-upload-section:hover {
  border-color: #0284c7;
  background-color: #f8fafc;
} */

.file-upload-section.has-file {
  border-style: solid;
  padding: 1rem;
}

.upload-icon {
  color: #94a3b8;
  margin-bottom: 1rem;
}

.upload-message {
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: #1e293b;
}

.upload-text {
  display: block;
  margin-bottom: 0.5rem;
}

.browse-btn {
  color: #0284c7;
  font-weight: 500;
  cursor: pointer;
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: #e0f2fe;
  transition: all 0.2s;
}

.browse-btn:hover {
  background-color: #bae6fd;
}

.file-hint {
  color: #64748b;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.file-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  cursor: pointer;
}

/* Selected File Styling */
.selected-file-info {
  width: 100%;
}

.file-preview {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 8px;
  background-color: #f8fafc;
  position: relative;
}

.file-icon {
  margin-right: 1rem;
  flex-shrink: 0;
}

.file-details {
  flex-grow: 1;
  overflow: hidden;
}

.file-name {
  margin: 0 0 0.25rem;
  font-size: 0.95rem;
  color: #334155;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.file-size {
  font-size: 0.8rem;
  color: #64748b;
}

.remove-file-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #94a3b8;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.remove-file-btn:hover {
  color: #ef4444;
  background-color: #fee2e2;
}

.file-type-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  margin-top: 0.5rem;
  background-color: #fee2e2;
  border-radius: 6px;
  color: #b91c1c;
  font-size: 0.85rem;
}

/* Button Styling */
.btn {
  padding: 0.625rem 1.25rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #0284c7, #0ea5e9);
  color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.btn-primary:hover:not([disabled]) {
  background: linear-gradient(135deg, #0369a1, #0284c7);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  background: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
}

.btn-cancel {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-cancel:hover {
  background: #f8fafc;
  color: #334155;
}
