import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

export type ButtonVariant = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';
export type ButtonSize = 'sm' | 'md' | 'lg';

@Component({
  selector: 'app-button',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <!-- Router Link Button -->
    <a *ngIf="routerLink"
       [routerLink]="routerLink"
       [class]="buttonClasses"
       [attr.aria-disabled]="disabled || loading">

      <!-- Loading spinner -->
      <div *ngIf="loading" class="animate-spin rounded-full border-2 border-white border-t-transparent mr-2"
           [style.width.px]="spinnerSize"
           [style.height.px]="spinnerSize">
      </div>

      <!-- Icon -->
      <ng-content *ngIf="!loading" select="[slot=icon]"></ng-content>

      <!-- Text content -->
      <span [class.ml-2]="hasIcon && !loading">
        <ng-content></ng-content>
      </span>
    </a>

    <!-- Regular Button -->
    <button *ngIf="!routerLink"
      [type]="type"
      [disabled]="disabled || loading"
      (click)="onClick()"
      [class]="buttonClasses">

      <!-- Loading spinner -->
      <div *ngIf="loading" class="animate-spin rounded-full border-2 border-white border-t-transparent mr-2"
           [style.width.px]="spinnerSize"
           [style.height.px]="spinnerSize">
      </div>

      <!-- Icon -->
      <ng-content *ngIf="!loading" select="[slot=icon]"></ng-content>

      <!-- Text content -->
      <span [class.ml-2]="hasIcon && !loading">
        <ng-content></ng-content>
      </span>
    </button>
  `,
  styles: []
})
export class ButtonComponent {
  @Input() variant: ButtonVariant = 'primary';
  @Input() size: ButtonSize = 'md';
  @Input() type: 'button' | 'submit' | 'reset' = 'button';
  @Input() disabled: boolean = false;
  @Input() loading: boolean = false;
  @Input() fullWidth: boolean = false;
  @Input() outline: boolean = false;
  @Input() hasIcon: boolean = false;
  @Input() routerLink: string | any[] | null = null;

  @Output() clicked = new EventEmitter<void>();

  get buttonClasses(): string {
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    
    const sizeClasses = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-6 py-4 text-lg'
    };

    const variantClasses = this.outline ? this.getOutlineVariantClasses() : this.getSolidVariantClasses();
    
    const widthClass = this.fullWidth ? 'w-full' : '';

    return `${baseClasses} ${sizeClasses[this.size]} ${variantClasses} ${widthClass}`.trim();
  }

  get spinnerSize(): number {
    const sizes = {
      sm: 16,
      md: 20,
      lg: 24
    };
    return sizes[this.size];
  }

  private getSolidVariantClasses(): string {
    const variants = {
      primary: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white',
      secondary: 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500 text-white',
      success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white',
      danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white',
      warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 text-white',
      info: 'bg-cyan-600 hover:bg-cyan-700 focus:ring-cyan-500 text-white',
      light: 'bg-gray-100 hover:bg-gray-200 focus:ring-gray-500 text-gray-900',
      dark: 'bg-gray-900 hover:bg-gray-800 focus:ring-gray-500 text-white'
    };
    return variants[this.variant];
  }

  private getOutlineVariantClasses(): string {
    const variants = {
      primary: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500',
      secondary: 'border-2 border-gray-600 text-gray-600 hover:bg-gray-600 hover:text-white focus:ring-gray-500',
      success: 'border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white focus:ring-green-500',
      danger: 'border-2 border-red-600 text-red-600 hover:bg-red-600 hover:text-white focus:ring-red-500',
      warning: 'border-2 border-yellow-600 text-yellow-600 hover:bg-yellow-600 hover:text-white focus:ring-yellow-500',
      info: 'border-2 border-cyan-600 text-cyan-600 hover:bg-cyan-600 hover:text-white focus:ring-cyan-500',
      light: 'border-2 border-gray-300 text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
      dark: 'border-2 border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white focus:ring-gray-500'
    };
    return variants[this.variant];
  }

  onClick(): void {
    if (!this.disabled && !this.loading) {
      this.clicked.emit();
    }
  }
}
