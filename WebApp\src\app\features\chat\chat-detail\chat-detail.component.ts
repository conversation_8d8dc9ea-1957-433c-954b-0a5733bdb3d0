import { <PERSON>mponent, OnInit, OnDestroy, OnChanges, SimpleChanges, ViewChild, ElementRef, AfterViewChecked, Input, Output, EventEmitter, Inject, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';
import {
  DoctorPatientChatServiceProxy,
  ChatMessageResponseDto,
  SendMessageRequestDto,
  GetChatHistoryRequestDto,
  StartVideoCallRequestDto,
  EndVideoCallRequestDto
} from '../../../../shared/service-proxies/service-proxies';
import { PatientChatService, TypingNotification } from '../../../services/patient-chat.service';
import { ChatCacheService } from '../../../services/chat-cache.service';
import { VideoCallService } from '../../../services/video-call.service';
import { WebRTCService } from '../../../services/webrtc.service';
import { OnlineStatusService } from '../../../services/online-status.service';
import { GlobalMessageNotificationService } from '../../../services/global-message-notification.service';
import { LocalTimePipe } from '../../../shared/pipes/local-time.pipe';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-chat-detail',
  standalone: true,
  imports: [CommonModule, FormsModule, LocalTimePipe, MatIconModule],
  templateUrl: './chat-detail.component.html',
  styleUrls: ['./chat-detail.component.css']
})
export class ChatDetailComponent implements OnInit, OnDestroy, OnChanges, AfterViewChecked {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @Input() doctorId: string = '';
  @Input() patientId: string = '';
  @Output() backToList = new EventEmitter<void>();
  @Output() messagesMarkedAsRead = new EventEmitter<void>();
  @Output() messageSent = new EventEmitter<ChatMessageResponseDto>();

  messages: ChatMessageResponseDto[] = [];
  newMessage: string = '';
  loading = true;
  sending = false;
  error: string | null = null;

  doctorName: string = '';
  patientName: string = '';
  patientEmail: string = '';
  currentUserId: string = '';

  // Real-time features
  otherUserTyping = false;
  otherUserName = '';
  currentUserName = '';
  private typingTimeout: any;

  // Online status
  isUserOnline = false;
  private messageInputSubject = new Subject<string>();

  private destroy$ = new Subject<void>();
  private shouldScrollToBottom = false;

  // Video call state
  isVideoCallVisible = false;
  isIncomingCallVisible = false;
  currentVideoCallId = '';
  videoCallParticipantName = '';
  isMuted = false;
  isVideoEnabled = true;
  callDuration = '00:00';
  isParticipantJoined = false;
  private callStartTime?: Date;
  private durationInterval?: any;
  private syncedCallStartTime?: Date;
  private incomingCallInfo: any = null;
  private currentMediaStream?: MediaStream | null;
  private autoCloseTimeout?: any;
  private isCallBeingEnded = false;
  private callTimeoutTimer?: any;

  // WebRTC connection status
  public webrtcConnectionState: RTCPeerConnectionState = 'new';
  public isWebRTCConnected = false;
  public connectionError: string | null = null;

  constructor(
    private router: Router,
    private chatServiceProxy: DoctorPatientChatServiceProxy,
    public chatService: PatientChatService,
    private chatCacheService: ChatCacheService,
    @Inject(VideoCallService) private videoCallService: VideoCallService,
    private webrtcService: WebRTCService,
    private onlineStatusService: OnlineStatusService,
    private globalMessageService: GlobalMessageNotificationService,
    private cdr: ChangeDetectorRef
  ) {
    console.log('🏗️ ChatDetailComponent constructor - WebRTC Service:', this.webrtcService);
  }

  /**
   * Subscribe to available patients stream to populate patient email/name and update online badge
   */
  private subscribeToAvailablePatients(): void {
    this.chatCacheService.availablePatients$
      .pipe(takeUntil(this.destroy$))
      .subscribe(list => {
        if (!list || list.length === 0) return;

        const patient = list.find(p => p.id === this.patientId);
        if (patient) {
          const newEmail = patient.email || '';
          const newName = patient.fullName || '';

          // Only update and recompute if changed
          const changed = newEmail !== this.patientEmail || newName !== this.patientName;
          this.patientEmail = newEmail;
          this.patientName = newName || this.patientName;

          if (changed && this.patientEmail) {
            this.isUserOnline = this.onlineStatusService.isUserOnline(this.patientEmail);
            this.cdr.detectChanges();
          }
        }
      });
  }



  async ngOnInit(): Promise<void> {
    // Initialize SignalR connection
    await this.initializeSignalR();

    // Subscribe to online status changes
    this.subscribeToOnlineStatus();

    // Subscribe to available patients so we get patient email after reload
    this.subscribeToAvailablePatients();

    if (this.doctorId && this.patientId) {
      this.loadChatHistory();
      this.markMessagesAsRead();
      this.loadPatientInfo();
    } else {
      this.error = 'Invalid chat parameters';
      this.loading = false;
    }
  }

  async ngOnChanges(changes: SimpleChanges): Promise<void> {
    console.log('🔄 WebApp Chat Detail: ngOnChanges called:', changes);

    // Check if doctorId or patientId changed
    if ((changes['doctorId'] || changes['patientId']) && !changes['doctorId']?.firstChange && !changes['patientId']?.firstChange) {
      console.log('🔄 WebApp Chat Detail: Doctor or Patient ID changed:', {
        doctorId: this.doctorId,
        patientId: this.patientId,
        previousDoctorId: changes['doctorId']?.previousValue,
        previousPatientId: changes['patientId']?.previousValue
      });

      if (this.doctorId && this.patientId) {
        // Leave previous chat thread and join new one
        if (changes['doctorId']?.previousValue && changes['patientId']?.previousValue) {
          console.log('🔄 WebApp Chat Detail: Leaving previous chat thread');
          await this.chatService.leaveChatThread(
            changes['doctorId'].previousValue,
            changes['patientId'].previousValue
          );
        }

        console.log('🔄 WebApp Chat Detail: Joining new chat thread');
        await this.chatService.joinChatThread(this.doctorId, this.patientId);

        // Clear previous messages immediately for better UX
        this.messages = [];
        this.newMessage = '';
        this.otherUserTyping = false;
        this.error = null;

        // ALWAYS reload chat history from API to get fresh messages
        console.log('🔄 WebApp Chat Detail: Force loading fresh chat history');
        this.loadChatHistory();

        // ALWAYS mark messages as read when switching to a chat
        console.log('🔄 WebApp Chat Detail: Marking messages as read');
        this.markMessagesAsRead();

        // Load patient info when patient changes
        this.loadPatientInfo();

        // Recompute online status if we already know patientEmail
        if (this.patientEmail) {
          this.isUserOnline = this.onlineStatusService.isUserOnline(this.patientEmail);
          this.cdr.detectChanges();
        }
      } else {
        this.error = 'Invalid chat parameters';
        this.loading = false;
      }
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngAfterViewChecked(): void {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  private loadChatHistory(): void {
    this.loading = true;
    this.error = null;

    // ALWAYS load from API to get fresh messages (bypass cache)
    console.log('🌐 Loading fresh chat history from API (bypass cache)');
    this.chatServiceProxy.getChatHistory(this.doctorId, this.patientId, 1, 50)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          const hadMessages = this.messages.length > 0;

          // Sort messages by creation date (oldest first) for proper chat display
          const sortedMessages = (response.messages || []).sort((a, b) => {
            // Convert to string first, then to Date to handle DateTime objects
            const dateStrA = a.createdDate?.toString() || '';
            const dateStrB = b.createdDate?.toString() || '';
            const dateA = new Date(dateStrA).getTime();
            const dateB = new Date(dateStrB).getTime();

            return dateA - dateB; // Ascending order (oldest first)
          });

          this.messages = sortedMessages;

          // Cache the messages
          this.chatCacheService.setChatMessages(this.doctorId, this.patientId, sortedMessages);

          this.loading = false;

          // Set names from first message if available
          if (this.messages.length > 0) {
            this.doctorName = this.messages[0].doctorName || '';
            this.patientName = this.messages[0].patientName || '';
          }

          // Mark messages as read when chat is loaded
          this.markMessagesAsRead();

          // Scroll to bottom if new messages or first load
          if (!hadMessages || this.messages.length > 0) {
            this.shouldScrollToBottom = true;
          }
        },
        error: (error: any) => {
          this.error = 'Failed to load chat history';
          this.loading = false;
          console.error('Error loading chat history:', error);
        }
      });
  }

  refreshChat(): void {
    this.loading = true;
    this.error = null;
    this.loadChatHistory();
  }

  sendMessage(): void {
    if (!this.newMessage.trim() || this.sending) {
      return;
    }

    this.sending = true;
    const messageText = this.newMessage.trim();
    this.newMessage = '';

    const request = new SendMessageRequestDto({
      doctorId: this.doctorId,
      patientId: this.patientId,
      message: messageText
    });

    this.chatServiceProxy.sendMessage(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (newMessage) => {
          // Check if message already exists to prevent duplicates
          const messageExists = this.messages.some(msg => msg.id === newMessage.id);

          if (!messageExists) {
            // Add new message to current chat history
            this.messages = [...this.messages, newMessage];
            this.shouldScrollToBottom = true;
          }

          // Emit the message to update the chat list sidebar
          this.messageSent.emit(newMessage);

          this.sending = false;

          // Don't reload chat history immediately to prevent flickering
          // SignalR will handle real-time updates for other users
        },
        error: (error: any) => {
          console.error('Error sending message:', error);
          this.sending = false;
          // Restore the message text so user can try again
          this.newMessage = messageText;
          // Show error in UI instead of browser alert
          this.connectionError = 'Failed to send message. Please try again.';
          // Clear error after 3 seconds
          setTimeout(() => {
            this.connectionError = '';
          }, 3000);
        }
      });
  }

  private markMessagesAsRead(): void {
    const request = new GetChatHistoryRequestDto({
      doctorId: this.doctorId,
      patientId: this.patientId,
      pageNumber: 1,
      pageSize: 50
    });

    this.chatServiceProxy.markAsRead(request)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          // Messages marked as read successfully
          console.log('✅ Messages marked as read for thread:', `${this.doctorId}-${this.patientId}`);

          // Clear unread count in global service
          this.globalMessageService.handleMessagesMarkedAsRead(this.doctorId, this.patientId);

          // Emit event to refresh chat list
          this.messagesMarkedAsRead.emit();
        },
        error: (error: any) => {
          console.error('Error marking messages as read:', error);
        }
      });
  }

  private scrollToBottom(): void {
    try {
      if (this.messagesContainer) {
        this.messagesContainer.nativeElement.scrollTop = this.messagesContainer.nativeElement.scrollHeight;
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  goBack(): void {
    this.backToList.emit();
  }

  navigateToPatientDetails(): void {
    if (this.patientId) {
      this.router.navigate(['/patients/detail', this.patientId]);
    }
  }

  isCurrentUserMessage(message: ChatMessageResponseDto): boolean {
    // Simple and reliable: WebApp is for doctors, so doctor messages are from current user
    return message.senderType === 'Doctor';
  }

  formatMessageTime(localDate: Date | null): string {
    if (!localDate) return '';

    const messageDate = localDate;

    // Ensure we have a valid date
    if (isNaN(messageDate.getTime())) {
      return '';
    }

    const now = new Date();
    const isToday = messageDate.toDateString() === now.toDateString();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = messageDate.toDateString() === yesterday.toDateString();

    if (isToday) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
    } else if (isYesterday) {
      return 'Yesterday ' + messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
    } else {
      return messageDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: messageDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      }) + ' ' + messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
    }
  }

  getPatientName(): string {
    return this.patientName || 'Patient';
  }

  getPatientInitials(): string {
    const name = this.patientName || 'Patient';
    return name.split(' ').map((n: string) => n[0]).join('').toUpperCase().substring(0, 2);
  }

  getConnectionStatusText(): string {
    if (this.connectionError) return 'Connection Error';

    switch (this.webrtcConnectionState) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'disconnected':
        return 'Reconnecting...';
      case 'failed':
        return 'Connection Failed';
      case 'closed':
        return 'Disconnected';
      default:
        return this.isParticipantJoined ? 'Connected' : 'Connecting...';
    }
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  onMessageInput(): void {
    // Send typing notification
    this.messageInputSubject.next(this.newMessage);
  }

  trackByMessageId(_index: number, message: ChatMessageResponseDto): string {
    return message.id || _index.toString();
  }

  // 🚀 SignalR Real-time Methods
  private async initializeSignalR(): Promise<void> {
    try {
      // Start SignalR connection
      await this.chatService.connect();

      // Subscribe to real-time messages
      this.chatService.message$.pipe(takeUntil(this.destroy$)).subscribe((message: ChatMessageResponseDto) => {
        // Check if message belongs to current chat
        if (message.doctorId === this.doctorId && message.patientId === this.patientId) {
          // Don't add messages from current user via SignalR - they get added via HTTP response
          // WebApp is for doctors, so ignore doctor messages
          if (message.senderType !== 'Doctor') {
            this.messages = [...this.messages, message];
            this.shouldScrollToBottom = true;
            this.markMessagesAsRead();
          }
        }
      });

      // Subscribe to typing indicators
      this.chatService.typing$.pipe(takeUntil(this.destroy$)).subscribe((typing: TypingNotification) => {
        this.handleTypingIndicator(typing);
      });

      // Subscribe to message notifications
      this.chatService.messageNotification$.pipe(takeUntil(this.destroy$)).subscribe(() => {
        // Handle notifications when user is not in the specific chat
      });

      // Subscribe to video call events
      this.chatService.videoCallStarted$.pipe(takeUntil(this.destroy$)).subscribe((callInfo: any) => {
        console.log('📹 Video call started event received:', callInfo);
        this.handleIncomingVideoCall(callInfo);
      });

      this.chatService.videoCallEnded$.pipe(takeUntil(this.destroy$)).subscribe((callInfo: any) => {
        console.log('📹 Video call ended event received:', callInfo);
        this.handleVideoCallEnded(callInfo);
      });

      // Note: Video call declines are now handled through VideoCallEnded events with endReason='declined'

      // Listen for participant joined events (when someone accepts the call)
      this.chatService.participantJoined$.pipe(takeUntil(this.destroy$)).subscribe((participantInfo: any) => {
        console.log('👤 Participant joined event received:', participantInfo);
        this.handleParticipantJoined(participantInfo);
      });

      // Listen for video call timer sync events
      this.chatService.callTimerSync$.pipe(takeUntil(this.destroy$)).subscribe((timerInfo: any) => {
        console.log('⏱️ Call timer sync received:', timerInfo);
        this.handleCallTimerSync(timerInfo);
      });

      // Listen for WebRTC remote stream
      this.webrtcService.remoteStream$.pipe(takeUntil(this.destroy$)).subscribe((stream: MediaStream) => {
        console.log('📹 Remote stream received:', stream);
        this.handleRemoteStream(stream);
      });

      // Listen for WebRTC connection state changes
      this.webrtcService.connectionState$.pipe(takeUntil(this.destroy$)).subscribe((state: RTCPeerConnectionState) => {
        console.log('🔗 WebRTC connection state:', state);
        this.handleConnectionStateChange(state);
      });



      // Setup typing notification debouncing
      this.messageInputSubject.pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      ).subscribe(message => {
        const isTyping = message.length > 0;
        this.chatService.sendTypingNotification(
          this.doctorId,
          this.patientId,
          this.currentUserName,
          isTyping
        );

        // Clear typing after 3 seconds of no input
        if (this.typingTimeout) {
          clearTimeout(this.typingTimeout);
        }

        if (isTyping) {
          this.typingTimeout = setTimeout(() => {
            this.chatService.sendTypingNotification(
              this.doctorId,
              this.patientId,
              this.currentUserName,
              false
            );
          }, 3000);
        }
      });

      // Join current chat thread if we have the IDs
      if (this.doctorId && this.patientId) {
        await this.chatService.joinChatThread(this.doctorId, this.patientId);
      }

    } catch (error) {
      console.error('Error initializing SignalR:', error);
    }
  }

  private handleTypingIndicator(typing: TypingNotification): void {
    // Only show typing indicator for other users in the CURRENT chat thread
    if (typing.userEmail !== this.getCurrentUserEmail()) {
      // Verify this typing notification is for the current chat thread
      const currentThreadId = `Chat_${this.doctorId}_${this.patientId}`;
      const isCurrentThread = typing.threadId === currentThreadId ||
        (typing.doctorId === this.doctorId && typing.patientId === this.patientId);



      // Only show typing indicator if it's for the current chat thread
      if (isCurrentThread) {
        this.otherUserTyping = typing.isTyping;
        this.otherUserName = typing.userName || 'Someone';

        // Clear typing indicator after 5 seconds
        if (typing.isTyping) {
          setTimeout(() => {
            this.otherUserTyping = false;
          }, 5000);
        }
      } else {

      }
    }
  }

  private getCurrentUserEmail(): string {
    // Not needed for simple role-based detection
    return '';
  }

  // Video Call Integration Methods
  async startVideoCall(): Promise<void> {
    console.log('🎬🎬🎬 START VIDEO CALL METHOD CALLED!');
    try {
      if (!this.doctorId || !this.patientId) {
        console.error('Cannot start video call: missing doctor or patient ID');
        this.connectionError = 'Cannot start video call: missing participant information';
        setTimeout(() => {
          this.connectionError = '';
        }, 3000);
        return;
      }

      console.log('Starting video call between doctor and patient...');

      const request = new StartVideoCallRequestDto({
        doctorId: this.doctorId,
        patientId: this.patientId,
        callType: 'Video',
        topic: 'Doctor-Patient Video Consultation',
        enableVideo: true,
        enableAudio: true
      });

      const session = await this.videoCallService.startVideoCall(request);
      console.log('Video call started successfully:', session);

      // Open video call window
      this.currentVideoCallId = session.callId || '';
      this.videoCallParticipantName = this.patientName || 'Patient';
      this.isVideoCallVisible = true;
      this.isParticipantJoined = false; // Waiting for other participant to join
      this.startCallTimer();
      await this.initializeLocalVideo();

      // Set call timeout (30 seconds for patient to answer)
      this.callTimeoutTimer = setTimeout(() => {
        if (!this.isParticipantJoined) {
          console.log('⏰ Call timeout - patient did not answer');
          this.handleCallTimeout();
        }
      }, 30000); // 30 seconds timeout

      // Initialize WebRTC for call initiator
      console.log('🔍 Checking WebRTC initialization conditions:', {
        hasMediaStream: !!this.currentMediaStream,
        doctorId: this.doctorId,
        patientId: this.patientId,
        callId: this.currentVideoCallId
      });

      if (this.currentMediaStream && this.doctorId && this.patientId) {
        try {
          console.log('🚀 Initializing WebRTC for call initiator...');
          console.log('🔍 WebRTC Service instance:', this.webrtcService);
          await this.webrtcService.initializeCall(
            this.currentVideoCallId,
            this.doctorId,
            this.patientId,
            this.currentMediaStream
          );
          console.log('✅ WebRTC initialized successfully');

          // Create offer as call initiator
          console.log('📤 Creating WebRTC offer...');
          console.log('🔍 About to call webrtcService.createOffer()');
          await this.webrtcService.createOffer();
          console.log('✅ WebRTC offer created and sent');
        } catch (error) {
          console.error('❌ Error initializing WebRTC:', error);
          this.connectionError = 'Failed to initialize video connection. Please try again.';
        }
      } else {
        console.warn('⚠️ Cannot initialize WebRTC - missing requirements:', {
          mediaStream: !!this.currentMediaStream,
          doctorId: !!this.doctorId,
          patientId: !!this.patientId
        });
      }

      // Sync call timer via SignalR
      if (this.doctorId && this.patientId && this.currentVideoCallId) {
        await this.chatService.startVideoCallTimer(this.doctorId, this.patientId, this.currentVideoCallId);
      }

      // Note: isParticipantJoined will be set to true when:
      // 1. Other participant accepts the call (via SignalR)
      // 2. WebRTC connection is established (via connection state change)
      console.log('⏳ Waiting for participant to join and WebRTC connection to establish...');

      console.log(`Video call window opened! Call ID: ${session.callId}`);

    } catch (error) {
      console.error('Error starting video call:', error);
      this.connectionError = 'Failed to start video call. Please try again.';
      setTimeout(() => {
        this.connectionError = '';
      }, 3000);
    }
  }

  // getDoctorParticipant(): ChatParticipant {
  //   return {
  //     id: this.doctorId,
  //     name: this.doctorName || 'Doctor',
  //     type: 'doctor',
  //     isOnline: true
  //   };
  // }

  // getPatientParticipant(): ChatParticipant {
  //   return {
  //     id: this.patientId,
  //     name: this.patientName || 'Patient',
  //     type: 'patient',
  //     isOnline: true
  //   };
  // }

  canStartVideoCall(): boolean {
    // Check if we have required data and not currently in a call
    const hasRequiredData = !!(this.doctorId && this.patientId && this.doctorName && this.patientName);
    const notInCall = !this.isVideoCallVisible && !this.currentVideoCallId;
    const notBeingEnded = !this.isCallBeingEnded;

    return hasRequiredData && notInCall && notBeingEnded;
  }

  isInVideoCall(): boolean {
    return this.videoCallService.isInCall();
  }

  // Video call window event handlers
  onVideoCallEnded(): void {
    this.isVideoCallVisible = false;
    this.currentVideoCallId = '';
    this.videoCallParticipantName = '';
    console.log('Video call ended');
  }

  onVideoCallMuteToggled(isMuted: boolean): void {
    console.log('Microphone toggled:', isMuted ? 'Muted' : 'Unmuted');
  }

  onVideoCallVideoToggled(isVideoEnabled: boolean): void {
    console.log('Camera toggled:', isVideoEnabled ? 'On' : 'Off');
  }

  // Video call control methods
  async toggleMute(): Promise<void> {
    this.isMuted = !this.isMuted;

    if (this.currentMediaStream) {
      const audioTracks = this.currentMediaStream.getAudioTracks();
      audioTracks.forEach(track => {
        track.enabled = !this.isMuted; // Enable/disable track instead of stopping
        console.log(`🎤 Microphone ${this.isMuted ? 'MUTED' : 'UNMUTED'} (track enabled: ${track.enabled})`);
      });
    }

    // Update WebRTC peer connection if it exists
    if (this.webrtcService) {
      this.webrtcService.updateAudioTrackState(!this.isMuted);
    }

    console.log(`🎤 Microphone ${this.isMuted ? 'MUTED' : 'UNMUTED'}`);
  }

  async toggleVideo(): Promise<void> {
    this.isVideoEnabled = !this.isVideoEnabled;

    if (this.currentMediaStream) {
      const videoTracks = this.currentMediaStream.getVideoTracks();
      videoTracks.forEach(track => {
        track.enabled = this.isVideoEnabled; // Enable/disable track instead of stopping
        console.log(`📹 Camera ${this.isVideoEnabled ? 'ENABLED' : 'DISABLED'} (track enabled: ${track.enabled})`);
      });
    }

    // Update local video element visibility
    const localVideo = document.querySelector('.local-video') as HTMLVideoElement;
    if (localVideo) {
      if (this.isVideoEnabled) {
        localVideo.style.display = 'block';
      } else {
        localVideo.style.display = 'none';
      }
    }

    // Update WebRTC peer connection if it exists
    if (this.webrtcService) {
      this.webrtcService.updateVideoTrackState(this.isVideoEnabled);
    }

    console.log(`📹 Camera ${this.isVideoEnabled ? 'ENABLED' : 'DISABLED'}`);
  }

  async endVideoCall(): Promise<void> {
    try {
      // Mark that call is being intentionally ended
      this.isCallBeingEnded = true;

      // Call backend API to properly end the call and notify other participants
      if (this.currentVideoCallId && this.doctorId) {
        console.log('Ending video call via API...');

        const request = new EndVideoCallRequestDto({
          callId: this.currentVideoCallId,
          userId: this.doctorId
        });

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('API call timeout')), 10000)
        );

        await Promise.race([
          this.videoCallService.endVideoCall(request),
          timeoutPromise
        ]);

        console.log('Video call ended successfully via API');
      }

      // Clean up local UI and resources
      this.cleanupVideoCallResources();

      console.log('Video call ended by user');
    } catch (error) {
      console.error('Error ending video call:', error);

      // Still clean up local resources even if API call fails
      this.cleanupVideoCallResources();

      // Don't show alert for timeout errors, just log them
      if (error instanceof Error && error.message !== 'API call timeout') {
        console.warn('Video call ended with error, but resources cleaned up successfully');
      }
    }
  }

  // Force close video call without API call (for auto-close scenarios)
  forceCloseVideoCall(): void {
    console.log('🔚 Force closing video call - bypassing API call');

    // Mark that call is being ended (to prevent error messages)
    this.isCallBeingEnded = true;

    // Directly clean up all resources without calling backend
    this.cleanupVideoCallResources();

    // Force UI update
    this.cdr.detectChanges();

    console.log('✅ Video call force closed successfully');
  }

  // Handle call timeout when patient doesn't answer
  handleCallTimeout(): void {
    console.log('⏰ Call timeout - patient did not answer within 30 seconds');

    // Show timeout message in UI instead of browser alert
    this.connectionError = 'Call timeout - Patient did not answer the call.';

    // Auto-close after showing error for 3 seconds
    setTimeout(() => {
      this.forceCloseVideoCall();
    }, 3000);
  }

  // Note: handleVideoCallDeclined is now integrated into handleVideoCallEnded

  private cleanupVideoCallResources(): void {
    console.log('🧹 Starting comprehensive video call cleanup...');

    // Clean up local UI and resources
    this.isVideoCallVisible = false;
    this.currentVideoCallId = '';
    this.videoCallParticipantName = '';
    this.isParticipantJoined = false;
    this.connectionError = '';
    this.stopCallTimer();

    // Reset call ending flag
    this.isCallBeingEnded = false;

    // Clear any auto-close timeout
    if (this.autoCloseTimeout) {
      clearTimeout(this.autoCloseTimeout);
      this.autoCloseTimeout = undefined;
    }

    // Clear any call timeout
    if (this.callTimeoutTimer) {
      clearTimeout(this.callTimeoutTimer);
      this.callTimeoutTimer = undefined;
    }

    // Hide incoming call invitation if visible
    this.isIncomingCallVisible = false;
    this.incomingCallInfo = null;

    // Clear synced call start time and reset timer state
    this.syncedCallStartTime = undefined;
    this.callStartTime = undefined;

    // Reset video/audio states
    this.isVideoEnabled = true;
    this.isMuted = false;

    // Clean up WebRTC connection
    try {
      this.webrtcService.endCall();
      console.log('🔗 WebRTC connection cleaned up');
    } catch (error) {
      console.error('Error cleaning up WebRTC connection:', error);
    }

    // Stop local media stream
    if (this.currentMediaStream) {
      try {
        this.currentMediaStream.getTracks().forEach(track => {
          track.stop();
          console.log('🎥 Stopped media track:', track.kind);
        });
        this.currentMediaStream = null;
      } catch (error) {
        console.error('Error stopping media stream:', error);
      }
    }

    // Reset WebRTC connection state
    this.webrtcConnectionState = 'new';
    this.isWebRTCConnected = false;

    // Reset video call service state
    this.videoCallService.resetCallState();

    // Clear any video elements
    setTimeout(() => {
      const remoteVideo = document.getElementById('remoteVideo') as HTMLVideoElement;
      if (remoteVideo) {
        remoteVideo.srcObject = null;
        console.log('🎥 Remote video element cleared');
      }

      const localVideo = document.querySelector('.local-video') as HTMLVideoElement;
      if (localVideo) {
        localVideo.srcObject = null;
        console.log('🎥 Local video element cleared');
      }
    }, 100);

    // Force UI update to ensure clean state
    this.cdr.detectChanges();

    console.log('✅ Video call resources cleaned up completely');
  }

  private startCallTimer(): void {
    // Stop any existing timer first
    this.stopCallTimer();

    // Use synced time if available, otherwise use local time
    this.callStartTime = this.syncedCallStartTime || new Date();

    // Initialize duration immediately
    this.callDuration = '00:00';

    this.durationInterval = setInterval(() => {
      if (this.callStartTime) {
        const duration = Date.now() - this.callStartTime.getTime();
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);
        this.callDuration = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // Force UI update to ensure timer displays
        this.cdr.detectChanges();
      }
    }, 1000);

    console.log('⏱️ Call timer started with time:', this.callStartTime, 'Duration:', this.callDuration);
  }

  private stopCallTimer(): void {
    if (this.durationInterval) {
      clearInterval(this.durationInterval);
      this.durationInterval = null;
    }
    this.callDuration = '00:00';
  }

  private async initializeLocalVideo(): Promise<void> {
    try {
      console.log('🎥 Requesting camera and microphone access...');

      // Request camera and microphone permissions
      // Enhanced audio settings to prevent echo and feedback
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          sampleSize: 16,
          channelCount: 1 // Mono to reduce bandwidth and potential echo
        }
      });

      console.log('✅ Camera and microphone access granted', {
        videoTracks: stream.getVideoTracks().length,
        audioTracks: stream.getAudioTracks().length,
        streamId: stream.id
      });

      // Store stream reference for toggle controls
      this.currentMediaStream = stream;

      // Wait a bit for the DOM to be ready
      setTimeout(() => {
        const localVideo = document.querySelector('.local-video') as HTMLVideoElement;
        console.log('🔍 Looking for local video element:', localVideo);

        if (localVideo) {
          localVideo.srcObject = stream;
          localVideo.style.display = 'block';
          localVideo.style.visibility = 'visible';
          localVideo.muted = true; // IMPORTANT: Mute local video to prevent echo/feedback

          localVideo.play().then(() => {
            console.log('✅ Local video stream started and playing (muted to prevent echo)');
          }).catch(e => {
            console.error('❌ Error playing local video:', e);
          });
        } else {
          console.error('❌ Local video element not found in DOM');
          // Try alternative selector
          const altVideo = document.getElementById('localVideo') as HTMLVideoElement;
          if (altVideo) {
            console.log('✅ Found local video with alternative selector');
            altVideo.srcObject = stream;
            altVideo.muted = true; // IMPORTANT: Mute local video to prevent echo/feedback
            altVideo.play();
          }
        }
      }, 100);

    } catch (error) {
      console.error('❌ Error accessing camera/microphone:', error);

      // Show user-friendly error message in UI
      const err = error as any;
      if (err.name === 'NotAllowedError') {
        this.connectionError = 'Camera and microphone access denied. Please allow access and try again.';
      } else if (err.name === 'NotFoundError') {
        this.connectionError = 'No camera or microphone found. Please check your devices.';
      } else if (err.name === 'NotReadableError') {
        this.connectionError = 'Camera/microphone is already in use by another application. Please close other video apps (Zoom, Teams, etc.) and try again.';
      } else {
        this.connectionError = 'Error accessing camera/microphone: ' + (err.message || 'Unknown error');
      }

      // Auto-close after showing error for 5 seconds (longer for device errors)
      setTimeout(() => {
        this.forceCloseVideoCall();
      }, 5000);
    }
  }



  onVideoCallStarted(callId: string): void {
    console.log('Video call started:', callId);
    // You can add additional logic here, like showing a notification
  }

  // Handle incoming video call from SignalR
  handleIncomingVideoCall(callInfo: any): void {
    console.log('🔔 Handling incoming video call:', callInfo);

    // Check if this call involves the current doctor-patient pair
    if (callInfo.doctorId === this.doctorId && callInfo.patientId === this.patientId) {
      // WebApp is for doctors - only show invitation if we're not already in a call
      // and we're not the one who just started the call
      if (!this.isVideoCallVisible && !this.isIncomingCallVisible) {

        console.log('📞 Incoming call received - showing invitation');

        // Small delay to avoid showing invitation for our own call
        setTimeout(() => {
          if (!this.isVideoCallVisible) {
            // Show incoming call invitation for doctor
            this.incomingCallInfo = callInfo;
            this.isIncomingCallVisible = true;
            this.videoCallParticipantName = this.patientName || 'Patient';

            console.log('📞 Incoming video call invitation shown (doctor receiving)');
          }
        }, 500); // 500ms delay to avoid self-invitation
      }
    }
  }

  // Accept incoming video call
  async acceptVideoCall(): Promise<void> {
    if (this.incomingCallInfo) {
      console.log('✅ Accepting video call');

      // Hide invitation and show video call window
      this.isIncomingCallVisible = false;
      this.currentVideoCallId = this.incomingCallInfo.callId;
      this.isVideoCallVisible = true;
      this.isParticipantJoined = false; // Will be set to true when WebRTC connects
      this.startCallTimer();

      // Initialize doctor's camera and WebRTC
      console.log('🚀 Initializing doctor\'s camera and WebRTC...');
      if (this.doctorId && this.patientId) {
        try {
          // Get doctor's camera stream
          await this.initializeLocalVideo();
          console.log('✅ Doctor\'s camera initialized');

          // Initialize WebRTC with doctor's stream
          const doctorStream = this.currentMediaStream;
          if (doctorStream) {
            await this.webrtcService.initializeCall(
              this.currentVideoCallId,
              this.doctorId,
              this.patientId,
              doctorStream
            );
            console.log('📱 WebRTC initialized successfully');
          }
        } catch (error) {
          console.error('❌ Error initializing video call:', error);
          this.connectionError = 'Failed to initialize video connection. Please try again.';
        }
      }

      // Sync call timer via SignalR
      if (this.doctorId && this.patientId && this.currentVideoCallId) {
        await this.chatService.acceptVideoCallTimer(this.doctorId, this.patientId, this.currentVideoCallId);
      }

      this.incomingCallInfo = null;
    }
  }

  // Decline incoming video call
  async declineVideoCall(): Promise<void> {
    if (this.incomingCallInfo) {
      console.log('❌ Declining video call');

      const callId = this.incomingCallInfo.callId || this.incomingCallInfo.CallId;

      // Hide invitation immediately
      this.isIncomingCallVisible = false;
      this.incomingCallInfo = null;
      this.videoCallParticipantName = '';

      if (callId && this.doctorId) {
        try {
          console.log('📞 Ending video call via backend API (doctor declining)');

          // Call the backend API to end the call - this will notify all participants
          const request = new EndVideoCallRequestDto({
            callId: callId,
            userId: this.doctorId
          });

          await this.videoCallService.endVideoCall(request);
          console.log('✅ Video call ended via backend API - all participants notified');
        } catch (error) {
          console.error('Error declining video call:', error);
        }
      }
    }
  }

  // Handle video call ended from SignalR
  handleVideoCallEnded(callInfo: any): void {
    console.log('🔔 Handling video call ended:', callInfo);

    // Check if this is the current active call
    const incomingCallId = callInfo.callId || callInfo.CallId;
    const endedBy = callInfo.endedBy || callInfo.EndedBy;
    const endReason = callInfo.endReason || callInfo.EndReason || 'ended';

    if (incomingCallId === this.currentVideoCallId) {
      console.log('✅ Current video call ended - cleaning up UI', { endedBy, endReason });

      // Show appropriate message based on end reason
      if (endReason === 'declined') {
        this.connectionError = 'Call was declined';
        console.log('📞 Call was declined by the other party');
      } else if (endReason === 'timeout') {
        this.connectionError = 'Call timeout - no answer';
        console.log('⏰ Call timed out - no answer');
      } else {
        this.connectionError = 'Call ended';
        console.log('📞 Call ended normally');
      }

      // Clear call timeout since call ended
      if (this.callTimeoutTimer) {
        clearTimeout(this.callTimeoutTimer);
        this.callTimeoutTimer = undefined;
        console.log('⏰ Call timeout cleared - call ended');
      }

      // Show the message for 3 seconds, then cleanup
      setTimeout(() => {
        this.cleanupVideoCallResources();
        this.cdr.detectChanges();
      }, 3000);

      // Stop local media stream
      if (this.currentMediaStream) {
        try {
          this.currentMediaStream.getTracks().forEach(track => track.stop());
          this.currentMediaStream = undefined;
        } catch (error) {
          console.error('Error stopping media stream:', error);
        }
      }

      console.log('✅ Video call ended message displayed, cleanup scheduled');
    } else {
      console.log('🔍 Video call ended notification for different call:', {
        notificationCallId: incomingCallId,
        currentCallId: this.currentVideoCallId
      });
    }
  }

  // Handle participant joined from SignalR
  handleParticipantJoined(participantInfo: any): void {
    console.log('🔔 Handling participant joined:', participantInfo);

    // Check if this involves the current call
    if (participantInfo.callId === this.currentVideoCallId) {
      this.isParticipantJoined = true;
      console.log('✅ Participant joined - call is now active');

      // Clear call timeout since participant joined
      if (this.callTimeoutTimer) {
        clearTimeout(this.callTimeoutTimer);
        this.callTimeoutTimer = undefined;
        console.log('⏰ Call timeout cleared - participant joined');
      }

      // Sync timer with the patient who just joined
      if (this.callStartTime && this.doctorId && this.patientId) {
        this.chatService.syncCallTimer(this.doctorId, this.patientId, this.currentVideoCallId, this.callStartTime);
        console.log('⏱️ Timer sync sent to patient who just joined');
      }
    }
  }

  // Handle call timer sync from SignalR
  handleCallTimerSync(timerInfo: any): void {
    console.log('🔔 Handling call timer sync:', timerInfo);

    // Check if this involves the current call
    const callId = timerInfo.callId || timerInfo.CallId;
    if (callId === this.currentVideoCallId) {
      // Handle both lowercase and uppercase property names from backend
      const startTime = timerInfo.startTime || timerInfo.StartTime;
      if (startTime) {
        this.syncedCallStartTime = new Date(startTime);
        console.log('⏱️ Call timer synced to:', this.syncedCallStartTime);

        // Restart timer with synced time
        this.stopCallTimer();
        this.startCallTimer();

        console.log('✅ Timer sync complete - doctor timer restarted with correct time');
      } else {
        console.warn('⚠️ Timer sync failed - no start time provided:', timerInfo);
      }
    } else {
      console.log('⚠️ Timer sync ignored - not for current call:', {
        syncCallId: callId,
        currentCallId: this.currentVideoCallId
      });
    }
  }

  // Handle remote video stream from WebRTC
  handleRemoteStream(stream: MediaStream): void {
    console.log('📹📹📹 SETTING UP REMOTE VIDEO STREAM', {
      streamId: stream.id,
      tracks: stream.getTracks().map(t => ({ kind: t.kind, enabled: t.enabled, readyState: t.readyState })),
      hasVideo: stream.getVideoTracks().length > 0,
      hasAudio: stream.getAudioTracks().length > 0,
      videoCallVisible: this.isVideoCallVisible,
      currentCallId: this.currentVideoCallId
    });

    // Wait a bit for DOM to be ready, then try to find video element
    setTimeout(() => {
      const remoteVideo = document.querySelector('#remoteVideo') as HTMLVideoElement;
      console.log('🔍 Looking for remote video element:', remoteVideo);

      if (remoteVideo) {
        console.log('✅ Remote video element found, setting up stream...');

        // Set up video element properties
        remoteVideo.srcObject = stream;
        remoteVideo.autoplay = true;
        remoteVideo.playsInline = true;
        remoteVideo.muted = false; // We want to hear remote audio
        remoteVideo.controls = false;

        // Force video to be visible
        remoteVideo.style.display = 'block';
        remoteVideo.style.position = 'absolute';
        remoteVideo.style.top = '0';
        remoteVideo.style.left = '0';
        remoteVideo.style.width = '100%';
        remoteVideo.style.height = '100%';
        remoteVideo.style.objectFit = 'cover';
        remoteVideo.style.zIndex = '10';
        remoteVideo.style.borderRadius = '18px';

        // Add event listeners for debugging
        remoteVideo.onloadedmetadata = () => {
          console.log('📹 Remote video metadata loaded');
        };

        remoteVideo.oncanplay = () => {
          console.log('📹 Remote video can play');
        };

        remoteVideo.onplaying = () => {
          console.log('✅ Remote video is playing!');

          // Hide avatar when video starts playing
          const avatarElement = document.querySelector('.video-call-avatar');
          if (avatarElement) {
            (avatarElement as HTMLElement).style.display = 'none';
            console.log('👤 Avatar hidden, video should be visible');
          }
        };

        remoteVideo.onerror = (error) => {
          console.error('❌ Remote video error:', error);
        };

        // Try to play the video
        remoteVideo.play().then(() => {
          console.log('✅ Remote video play() succeeded');
        }).catch(error => {
          console.error('❌ Error playing remote video:', error);

          // Try to play again after user interaction
          document.addEventListener('click', () => {
            remoteVideo.play().catch(e => console.error('❌ Retry play failed:', e));
          }, { once: true });
        });

      } else {
        console.warn('⚠️ Remote video element not found, retrying...');
        // Retry after another short delay
        setTimeout(() => {
          const retryVideo = document.querySelector('#remoteVideo') as HTMLVideoElement;
          if (retryVideo) {
            console.log('🔄 Retry: Found remote video element');
            retryVideo.srcObject = stream;
            retryVideo.play().catch(e => console.error('❌ Retry play failed:', e));
          } else {
            console.error('❌ Remote video element still not found after retry');
          }
        }, 1000);
      }
    }, 500);
  }

  // Handle WebRTC connection state changes
  handleConnectionStateChange(state: RTCPeerConnectionState): void {
    console.log('🔗 WebRTC connection state changed to:', state);

    this.webrtcConnectionState = state;
    this.connectionError = null; // Clear previous errors

    switch (state) {
      case 'connected':
        console.log('✅ WebRTC peer connection established');
        this.isParticipantJoined = true;
        this.isWebRTCConnected = true;

        // Clear any auto-close timeout since connection is restored
        if (this.autoCloseTimeout) {
          clearTimeout(this.autoCloseTimeout);
          this.autoCloseTimeout = undefined;
          console.log('✅ Auto-close timeout cancelled - connection restored');
        }

        // Check if we already have a remote stream and apply it
        const existingStream = this.webrtcService.getRemoteStream();
        if (existingStream) {
          console.log('🔄 Applying existing remote stream after connection');
          this.handleRemoteStream(existingStream);
        }
        break;
      case 'disconnected':
        console.log('⚠️ WebRTC peer connection disconnected');
        this.isWebRTCConnected = false;

        // Only show error if call is not being intentionally ended
        if (!this.isCallBeingEnded) {
          // Don't show "Connection lost" immediately - wait for SignalR event
          // This prevents showing wrong message when call is properly ended
          console.log('⏳ WebRTC disconnected - waiting for SignalR call ended event...');

          // Clear any existing auto-close timeout
          if (this.autoCloseTimeout) {
            clearTimeout(this.autoCloseTimeout);
          }

          // Wait for SignalR event first, then show connection error if no event comes
          this.autoCloseTimeout = setTimeout(() => {
            if (!this.isCallBeingEnded && this.isVideoCallVisible) {
              console.log('⚠️ No SignalR event received - showing connection error');
              this.connectionError = 'Connection lost. Call ended unexpectedly.';

              // Auto-close after showing error
              setTimeout(() => {
                console.log('🔚 Auto-closing video call due to connection loss');
                this.forceCloseVideoCall();
              }, 3000);
            }
          }, 2000); // Wait 2 seconds for SignalR event
        }
        break;
      case 'failed':
        console.log('❌ WebRTC peer connection failed');
        this.isParticipantJoined = false;
        this.isWebRTCConnected = false;

        // Only show error if call is not being intentionally ended
        if (!this.isCallBeingEnded) {
          this.connectionError = 'Connection failed. Please try again.';

          // Show avatar again
          const avatarElement = document.querySelector('.video-call-avatar');
          if (avatarElement) {
            (avatarElement as HTMLElement).style.display = 'block';
          }

          // Clear any existing auto-close timeout
          if (this.autoCloseTimeout) {
            clearTimeout(this.autoCloseTimeout);
          }

          // Auto-close video call after connection failure
          this.autoCloseTimeout = setTimeout(() => {
            console.log('🔚 Auto-closing video call due to connection failure');
            this.forceCloseVideoCall();
          }, 6000); // Give 6 seconds to show the error
        }
        break;
      case 'closed':
        console.log('🔒 WebRTC peer connection closed');
        this.isParticipantJoined = false;
        this.isWebRTCConnected = false;
        break;
      case 'connecting':
        console.log('🔄 WebRTC peer connection establishing...');
        this.isWebRTCConnected = false;
        break;
      case 'new':
        console.log('🆕 WebRTC peer connection initialized');
        this.isWebRTCConnected = false;
        break;
    }
  }



  onVideoCallError(error: string): void {
    console.error('Video call error:', error);
    // Show error in UI instead of browser alert
    this.connectionError = 'Video call error: ' + error;
    // Auto-close after showing error for 3 seconds
    setTimeout(() => {
      this.forceCloseVideoCall();
    }, 3000);
  }

  // Online Status Methods - simplified
  private subscribeToOnlineStatus(): void {
    // Subscribe to online users updates - super simple
    this.onlineStatusService.onlineUsers$
      .pipe(takeUntil(this.destroy$))
      .subscribe(onlineUsers => {
        if (this.patientEmail) {
          this.isUserOnline = onlineUsers.has(this.patientEmail);
          this.cdr.detectChanges();
        }
      });
  }

  private async loadPatientInfo(): Promise<void> {
    try {
      // Get patient info from available patients to get email
      const availablePatients = this.chatCacheService.getCachedAvailablePatients();
      if (availablePatients) {
        const patient = availablePatients.find(p => p.id === this.patientId);
        if (patient) {
          this.patientEmail = patient.email || '';
          this.patientName = patient.fullName || '';

          // Check online status for this patient
          if (this.patientEmail) {
            this.isUserOnline = this.onlineStatusService.isUserOnline(this.patientEmail);
          }
        }
      } else {
        // Optimized: Don't make additional API call, use basic info
        console.log('⚠️ Patient info not in cache, using basic info to avoid API call');
        this.patientName = 'Patient'; // Generic name
        this.isUserOnline = false; // Show as offline to avoid API call
      }
    } catch (error) {
      console.error('Error loading patient info:', error);
    }
  }
}
