import { Injectable } from '@angular/core';
import { HubConnection, HubConnectionBuilder, HubConnectionState, LogLevel } from '@microsoft/signalr';
import { Subject, BehaviorSubject, Observable } from 'rxjs';
import { ChatMessageResponseDto } from '../../shared/service-proxies/service-proxies';
import { getRemoteServiceBaseUrl } from '../app.config';
import { GlobalMessageNotificationService } from './global-message-notification.service';

export interface TypingNotification {
  userEmail: string;
  userName: string;
  isTyping: boolean;
  timestamp: Date;
  threadId?: string;
  doctorId?: string;
  patientId?: string;
  senderType?: string;
}



export interface MessageNotification {
  message: ChatMessageResponseDto;
  threadId: string;
  senderName: string;
  chatPartner: string;
}

export interface MessageReadNotification {
  userId: string;
  doctorId: string;
  patientId: string;
  timestamp: Date;
}

export interface ChatConnectionStatus {
  isConnected: boolean;
  isConnecting: boolean;
  connectionState: HubConnectionState;
  lastConnected?: Date;
  reconnectAttempts?: number;
}

export interface VideoCallStartedEvent {
  callId: string;
  doctorId: string;
  patientId: string;
  timestamp: Date;
}

export interface VideoCallEndedEvent {
  callId: string;
  endedBy: string;
  timestamp: Date;
}

export interface VideoCallDeclinedEvent {
  callId: string;
  declinedBy: string;
  timestamp: Date;
}

export interface ParticipantJoinedEvent {
  callId: string;
  userId: string;
  timestamp: Date;
}

export interface ChatThreadCreatedEvent {
  doctorId: string;
  patientId: string;
  threadId: string;
  createdAt: Date;
}

@Injectable({
  providedIn: 'root'
})
export class PatientChatService {
  private hubConnection: HubConnection | null = null;
  private connectionState = new BehaviorSubject<HubConnectionState>(HubConnectionState.Disconnected);
  private isConnecting = false;
  private connectionPromise: Promise<void> | null = null;
  private lastConnected: Date | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectBackoffMultiplier = 1.5;

  // Subjects for real-time events
  private messageSubject = new Subject<ChatMessageResponseDto>();
  private typingSubject = new Subject<TypingNotification>();
  private messageNotificationSubject = new Subject<MessageNotification>();
  private messageReadSubject = new Subject<MessageReadNotification>();

  // Video call event subjects
  private videoCallStartedSubject = new Subject<VideoCallStartedEvent>();
  private videoCallEndedSubject = new Subject<VideoCallEndedEvent>();
  private videoCallDeclinedSubject = new Subject<VideoCallDeclinedEvent>();
  private participantJoinedSubject = new Subject<ParticipantJoinedEvent>();
  private callTimerSyncSubject = new Subject<any>();
  private chatThreadCreatedSubject = new Subject<ChatThreadCreatedEvent>();

  // Public observables
  public connectionState$ = this.connectionState.asObservable();
  public message$ = this.messageSubject.asObservable();
  public typing$ = this.typingSubject.asObservable();
  public messageNotification$ = this.messageNotificationSubject.asObservable();
  public messageRead$ = this.messageReadSubject.asObservable();
  public chatThreadCreated$ = this.chatThreadCreatedSubject.asObservable();

  // Video call observables
  public videoCallStarted$ = this.videoCallStartedSubject.asObservable();
  public videoCallEnded$ = this.videoCallEndedSubject.asObservable();
  public videoCallDeclined$ = this.videoCallDeclinedSubject.asObservable();
  public participantJoined$ = this.participantJoinedSubject.asObservable();
  public callTimerSync$ = this.callTimerSyncSubject.asObservable();

  // Expose hub connection for WebRTC service
  get hubConnectionForWebRTC() {
    return this.hubConnection;
  }

  private apiUrl: string = "";
  constructor(private globalMessageService: GlobalMessageNotificationService) {
    // Auto-reconnect on page visibility change
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.hubConnection?.state === HubConnectionState.Disconnected) {
        this.connect();
      }
    });
    this.apiUrl = getRemoteServiceBaseUrl();
  }

  /**
   * Establishes a connection to the DoctorPatientChatHub
   * @returns Promise that resolves when the connection is established
   */
  async connect(): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      return Promise.resolve();
    }

    if (this.isConnecting && this.connectionPromise) {
      return this.connectionPromise;
    }

    // Check if we've exceeded max reconnect attempts
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log(`❌ Max reconnect attempts (${this.maxReconnectAttempts}) exceeded`);
      return Promise.reject(new Error('Max reconnect attempts exceeded'));
    }

    // Check if user is authenticated before attempting connection
    const token = this.getTokenFromCookie();
    if (!token) {
      console.warn('⚠️ PatientChatService: Cannot connect - no authentication token found');
      console.log('💡 PatientChatService: Please login first before attempting to connect to chat');
      throw new Error('Authentication required. Please login first.');
    }

    this.isConnecting = true;
    this.connectionPromise = this.establishConnection();

    try {
      await this.connectionPromise;
      // Reset reconnect attempts on successful connection
      this.reconnectAttempts = 0;
      console.log('✅ SignalR connection established successfully');
    } catch (error) {
      this.reconnectAttempts++;
      console.log(`❌ Connection attempt ${this.reconnectAttempts} failed:`, error);
      throw error;
    } finally {
      this.isConnecting = false;
      this.connectionPromise = null;
    }
  }

  private async establishConnection(): Promise<void> {
    try {
      console.log('🔄 Attempting DoctorPatientChatHub connection...');

      // Get JWT token from cookies with enhanced debugging
      const token = this.getTokenFromCookie();
      console.log('🔑 Using token for DoctorPatientChatHub:', token ? 'Token found' : 'No token found');
      console.log('🔑 Token length:', token ? token.length : 0);
      console.log('🔑 Token preview:', token ? `${token.substring(0, 20)}...` : 'null');

      // Debug: Check all cookies
      console.log('🍪 All cookies:', document.cookie);

      // If no token, don't attempt connection
      if (!token) {
        throw new Error('No authentication token found. Please login first.');
      }

      this.hubConnection = new HubConnectionBuilder()
        .withUrl(`${this.apiUrl}/doctorpatientchathub`, {
          accessTokenFactory: () => {
            const currentToken = this.getTokenFromCookie();
            console.log('🔑 AccessTokenFactory called, token:', currentToken ? 'found' : 'not found');
            return currentToken || '';
          },
          withCredentials: false,  // Disable credentials; rely on bearer token
          transport: 1,            // WebSockets only for stability behind LB/CDN
          skipNegotiation: true    // Skip negotiation when forcing WebSockets
        })
        .withAutomaticReconnect([0, 2000, 5000, 10000, 30000]) // More gradual backoff
        .configureLogging(LogLevel.Information)
        .build();

      this.setupEventHandlers();

      await this.hubConnection.start();
      this.connectionState.next(this.hubConnection.state);
      this.lastConnected = new Date();
      this.reconnectAttempts = 0;

      console.log('✅ SignalR Connected to DoctorPatientChatHub successfully!');
      console.log('Connection ID:', this.hubConnection.connectionId);
      console.log('🕐 Connected at:', this.lastConnected.toLocaleTimeString());
    } catch (error) {
      console.error('❌ DoctorPatientChatHub Connection Error:', error);
      this.connectionState.next(HubConnectionState.Disconnected);

      // Check if it's an authentication error
      const errorMessage = error instanceof Error ? error.message : String(error);
      const isAuthError = errorMessage.includes('401') ||
        errorMessage.includes('Unauthorized') ||
        errorMessage.includes('authentication') ||
        errorMessage.includes('token');

      if (isAuthError) {
        console.error('🔐 Authentication Error: Please check if you are logged in');
        console.log('💡 Suggestion: Try logging out and logging back in');
        // Don't retry on auth errors
        throw new Error('Authentication failed. Please login again.');
      } else {
        // Retry connection after 5 seconds for non-auth errors
        console.log('🔄 Retrying DoctorPatientChatHub connection in 5 seconds...');
        setTimeout(() => this.connect(), 5000);
      }

      throw error;
    }
  }

  private setupEventHandlers(): void {
    if (!this.hubConnection) return;

    // Handle connection state changes
    this.hubConnection.onreconnecting(() => {
      this.connectionState.next(HubConnectionState.Reconnecting);
      this.reconnectAttempts++;
      console.log(`🔄 DoctorPatientChatHub Reconnecting... (Attempt ${this.reconnectAttempts})`);
    });

    this.hubConnection.onreconnected(() => {
      this.connectionState.next(HubConnectionState.Connected);
      this.lastConnected = new Date();
      console.log('✅ DoctorPatientChatHub Reconnected successfully!');
      console.log('🕐 Reconnected at:', this.lastConnected.toLocaleTimeString());
    });

    this.hubConnection.onclose(() => {
      this.connectionState.next(HubConnectionState.Disconnected);
      console.log('❌ DoctorPatientChatHub Connection Closed');
      console.log('🕐 Disconnected at:', new Date().toLocaleTimeString());
    });

    // Handle real-time events
    this.hubConnection.on('ReceiveNewMessage', (message: ChatMessageResponseDto) => {
      console.log('📨 Received new message via SignalR:', message);
      this.messageSubject.next(message);

      // Handle message through global service for badge counting and notifications
      this.globalMessageService.handleNewMessage(message);
    });

    this.hubConnection.on('UserTyping', (notification: TypingNotification) => {
      console.log('⌨️ Received typing notification:', notification);
      this.typingSubject.next(notification);
    });

    this.hubConnection.on('NewMessageNotification', (notification: MessageNotification) => {
      console.log('🔔 Received message notification:', notification);
      this.messageNotificationSubject.next(notification);

      // Handle notification through global service
      this.globalMessageService.handleMessageNotification(notification);
    });

    this.hubConnection.on('MessagesMarkedAsRead', (notification: MessageReadNotification) => {
      console.log('✅ Messages marked as read:', notification);
      this.messageReadSubject.next(notification);

      // Handle read notification through global service
      this.globalMessageService.handleMessagesMarkedAsRead(notification.doctorId, notification.patientId);
    });

    // Video call event handlers
    this.hubConnection.on('VideoCallStarted', (data: VideoCallStartedEvent) => {
      console.log('📹 Video call started event received:', data);
      this.videoCallStartedSubject.next(data);
    });

    this.hubConnection.on('VideoCallEnded', (data: VideoCallEndedEvent) => {
      console.log('📹 Video call ended event received:', data);
      this.videoCallEndedSubject.next(data);
    });

    this.hubConnection.on('VideoCallDeclined', (data: VideoCallDeclinedEvent) => {
      console.log('❌ Video call declined event received:', data);
      this.videoCallDeclinedSubject.next(data);
    });

    this.hubConnection.on('ParticipantJoined', (data: ParticipantJoinedEvent) => {
      console.log('👤 Participant joined event received:', data);
      this.participantJoinedSubject.next(data);
    });

    this.hubConnection.on('CallTimerSync', (data: any) => {
      console.log('⏱️ Call timer sync event received:', data);
      this.callTimerSyncSubject.next(data);
    });

    this.hubConnection.on('VideoCallAccepted', (data: any) => {
      console.log('✅ Video call accepted event received:', data);
      // This can be used for additional UI updates when call is accepted
    });

    this.hubConnection.on('ChatThreadCreated', (data: ChatThreadCreatedEvent) => {
      console.log('🆕 Chat thread created event received:', data);
      this.chatThreadCreatedSubject.next(data);
    });

    // Handle chat list updates for real-time list synchronization
    this.hubConnection.on('ChatListUpdate', (data: any) => {
      console.log('📋 WebApp Chat list update received:', data);
      this.chatThreadCreatedSubject.next({
        doctorId: data.doctorId,
        patientId: data.patientId,
        threadId: `${data.doctorId}-${data.patientId}`,
        createdAt: new Date()
      });
    });


  }

  /**
   * Disconnects the SignalR connection
   * @returns Promise that resolves when the connection is stopped
   */
  async disconnect(): Promise<void> {
    if (this.hubConnection) {
      try {
        await this.hubConnection.stop();
        this.connectionState.next(HubConnectionState.Disconnected);
        console.log('SignalR connection stopped');
      } catch (error) {
        console.error('Error stopping SignalR connection:', error);
        throw error;
      } finally {
        this.isConnecting = false;
        this.connectionPromise = null;
      }
    }
  }

  // Chat thread management methods
  async joinChatThread(doctorId: string, patientId: string): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('JoinChatThread', doctorId, patientId);
        console.log(`✅ Joined chat thread: ${doctorId}-${patientId}`);
      } catch (error) {
        console.error('❌ Error joining chat thread:', error);
        throw error;
      }
    } else {
      console.warn('⚠️ Cannot join chat thread - SignalR not connected');
    }
  }

  async leaveChatThread(doctorId: string, patientId: string): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('LeaveChatThread', doctorId, patientId);
        console.log(`✅ Left chat thread: ${doctorId}-${patientId}`);
      } catch (error) {
        console.error('❌ Error leaving chat thread:', error);
        throw error;
      }
    }
  }

  async sendTypingNotification(doctorId: string, patientId: string, userName: string, isTyping: boolean): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('NotifyTyping', doctorId, patientId, userName, isTyping);
      } catch (error) {
        console.error('❌ Error sending typing notification:', error);
      }
    }
  }

  async markMessagesAsRead(doctorId: string, patientId: string, userId: string): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('MarkMessagesAsRead', doctorId, patientId, userId);
        console.log(`✅ Marked messages as read for thread: ${doctorId}-${patientId}`);
      } catch (error) {
        console.error('❌ Error marking messages as read:', error);
        throw error;
      }
    }
  }

  // Connection status methods
  isConnected(): boolean {
    return this.hubConnection?.state === HubConnectionState.Connected;
  }

  getConnectionState(): HubConnectionState {
    return this.hubConnection?.state || HubConnectionState.Disconnected;
  }

  getConnectionStatus(): ChatConnectionStatus {
    return {
      isConnected: this.isConnected(),
      isConnecting: this.isConnecting,
      connectionState: this.getConnectionState(),
      lastConnected: this.lastConnected || undefined,
      reconnectAttempts: this.reconnectAttempts
    };
  }



  // Authentication status methods
  isAuthenticated(): boolean {
    const token = this.getTokenFromCookie();
    return token !== null && token.length > 0;
  }

  getAuthenticationStatus(): { isAuthenticated: boolean; hasToken: boolean; tokenLength: number } {
    const token = this.getTokenFromCookie();
    return {
      isAuthenticated: this.isAuthenticated(),
      hasToken: token !== null,
      tokenLength: token ? token.length : 0
    };
  }

  /**
   * Get JWT token from cookies (same method as AuthService)
   */
  private getTokenFromCookie(): string | null {
    try {
      const TOKEN_KEY = 'jwt_token';
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, ...valueParts] = cookie.trim().split('=');
        if (name === TOKEN_KEY) {
          const value = valueParts.join('=');
          if (value && value.length > 0) {
            return value;
          }
        }
      }
      return null;
    } catch (error) {
      console.error('PatientChatService: Error reading token from cookies:', error);
      return null;
    }
  }

  // Video call timer sync methods
  async syncCallTimer(doctorId: string, patientId: string, callId: string, startTime: Date): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('SyncCallTimer', doctorId, patientId, callId, startTime);
        console.log('⏱️ Call timer sync sent:', { doctorId, patientId, callId, startTime });
      } catch (error) {
        console.error('❌ Error syncing call timer:', error);
      }
    } else {
      console.warn('⚠️ Cannot sync call timer - SignalR not connected');
    }
  }

  async startVideoCallTimer(doctorId: string, patientId: string, callId: string): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('StartVideoCall', doctorId, patientId, callId);
        console.log('🎬 Video call timer started:', { doctorId, patientId, callId });
      } catch (error) {
        console.error('❌ Error starting video call timer:', error);
      }
    } else {
      console.warn('⚠️ Cannot start video call timer - SignalR not connected');
    }
  }

  // Decline video call (no SignalR method needed - will use backend API)
  async declineVideoCall(doctorId: string, patientId: string, callId: string): Promise<void> {
    console.log('❌ Video call decline - will be handled by backend API call');
    // Note: The actual decline notification will be sent when the backend API EndVideoCall is called
    // The backend's NotifyVideoCallEnded will handle the SignalR notification to all participants
  }

  async acceptVideoCallTimer(doctorId: string, patientId: string, callId: string): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('AcceptVideoCall', doctorId, patientId, callId);
        console.log('✅ Video call timer accepted:', { doctorId, patientId, callId });
      } catch (error) {
        console.error('❌ Error accepting video call timer:', error);
      }
    } else {
      console.warn('⚠️ Cannot accept video call timer - SignalR not connected');
    }
  }
}
