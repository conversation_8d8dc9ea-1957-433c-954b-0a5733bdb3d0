import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  OrderServiceProxy,
  Order,
  OrderItem,
  UpdateOrderStatusRequest,
  MedicineServiceProxy,
  Medicine,
} from '../../../../../shared/service-proxies/service-proxies';
import { AuthService } from '../../../../../shared/services/auth.service';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { getRemoteServiceBaseUrl } from '../../../../app.config';

import { ServiceProxyModule } from '../../../../../shared/service-proxies/service-proxy.module';

@Component({
  selector: 'app-medicine-order-detail',
  templateUrl: './medicine-order-detail.component.html',
  styleUrls: ['./medicine-order-detail.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, MatSnackBarModule, ServiceProxyModule],
})
export class MedicineOrderDetailComponent implements OnInit {
  // Order info
  orderId: string | null = null;
  order: Order | null = null;
  medicine: Medicine | null = null;
  medicines: Medicine[] = [];

  // UI states
  isLoading: boolean = true;
  isAdmin: boolean = false;
  isUpdatingStatus: boolean = false;
  errorMessage: string | null = null;
  isEditingStatus: boolean = false;
  newStatus: string = '';
  isStatusRecent: boolean = false;

  // Status options
  availableStatuses: string[] = [
    'Pending',
    'Processing',
    'Shipped',
    'Delivered',
    'Cancelled',
  ];

  // Base URL for API calls
  baseUrl: string = getRemoteServiceBaseUrl();
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private orderService: OrderServiceProxy,
    private medicineService: MedicineServiceProxy,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.isAdmin = this.authService.hasRole('Admin');

    // Get order ID from route parameters
    this.orderId = this.route.snapshot.paramMap.get('id');

    if (this.orderId) {
      this.loadOrderDetails();
    } else {
      this.errorMessage = 'Order ID is required';
      this.isLoading = false;
    }
  }

  // This method is no longer needed since we use OrderItem data directly
  // getMedicineInfo(medicineId: string) {
  //   this.medicineCareProductService.getMedicineByStringId(medicineId).subscribe((res) => {
  //   })
  // }

  loadOrderDetails(): void {
    if (!this.orderId) return;

    this.isLoading = true;

    this.orderService.getById(this.orderId).subscribe({
      next: (order: any) => {
        this.order = order;
        this.newStatus = order.status || '';

        // Use order items directly - no need to fetch medicine details from catalog
        // Order items already contain the medicine information as it was when the order was placed
        if (order.orderItems && order.orderItems.length > 0) {
          // Set the first order item as the primary medicine for display
          this.medicine = this.createMedicineFromOrderItem(order.orderItems[0]);
          this.medicines = order.orderItems.map((item: OrderItem) =>
            this.createMedicineFromOrderItem(item)
          );
        }

        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading order details:', error);
        this.errorMessage =
          'Failed to load order details. Please try again later.';
        this.isLoading = false;
      },
    });
  }
  loadMedicineDetails(medicineId: string): void {
    this.medicineService.getById(medicineId).subscribe({
      next: (medicine: Medicine) => {
        console.log(medicine);
        this.medicine = medicine;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('Error loading medicine details:', error);
        this.isLoading = false;
      },
    });
  }

  // This method is no longer needed since we use OrderItem data directly
  // loadMedicineDetailsForItems(orderItems: OrderItem[]): void {
  //   // Load medicine details for all items
  //   const medicinePromises = orderItems.map(item =>
  //     this.medicineCareProductService.getMedicineByStringId(item.medicineId).toPromise()
  //   );
  //
  //   Promise.all(medicinePromises)
  //     .then(medicines => {
  //       // Store medicines for display
  //       this.medicines = medicines.filter(m => m != null) as MedicineCareProduct[];
  //
  //       // Set the first medicine as the primary medicine for display
  //       if (this.medicines.length > 0) {
  //         this.medicine = this.medicines[0];
  //       }
  //
  //       this.isLoading = false;
  //     })
  //     .catch(error => {
  //       console.error('Error loading medicine details:', error);
  //       this.isLoading = false;
  //     });
  // }

  orderDatausUpdate(orderId: string, newStatus: string): void {
    if (!orderId || !newStatus) return;

    this.isUpdatingStatus = true;

    const updateRequest = new UpdateOrderStatusRequest({
      status: newStatus,
      trackingNumber: undefined,
    });

    this.orderService.updateStatus(orderId, updateRequest).subscribe({
      next: (_result: any) => {
        // Update the local order status if this is the current order
        if (this.order && this.order.id === orderId) {
          this.order.status = newStatus;

          // Trigger status animation
          this.isStatusRecent = true;
          setTimeout(() => {
            this.isStatusRecent = false;
          }, 2000);
        }

        this.isEditingStatus = false;
        this.isUpdatingStatus = false;
        this.snackBar.open('Order status updated successfully', 'Close', {
          duration: 3000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
        });
      },
      error: (error: any) => {
        console.error('Error updating order status:', error);
        this.isUpdatingStatus = false;

        this.snackBar.open('Failed to update order status', 'Close', {
          duration: 3000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
        });
      },
    });
  }
  startEditStatus(): void {
    if (!this.isAdmin) return;

    this.isEditingStatus = true;
    this.newStatus = this.order?.status || '';
  }

  cancelEditStatus(): void {
    this.isEditingStatus = false;
    this.newStatus = this.order?.status || '';
  }
  updateOrderStatus(): void {
    if (!this.orderId || !this.order || !this.newStatus) return;

    this.isUpdatingStatus = true;

    const updateRequest = new UpdateOrderStatusRequest({
      status: this.newStatus,
      trackingNumber: undefined,
    });

    this.orderService.updateStatus(this.orderId, updateRequest).subscribe({
      next: (_result: any) => {
        // Update the local order status
        if (this.order) {
          this.order.status = this.newStatus;

          // Trigger status animation
          this.isStatusRecent = true;
          setTimeout(() => {
            this.isStatusRecent = false;
          }, 2000);
        }

        this.isEditingStatus = false;
        this.isUpdatingStatus = false;

        this.snackBar.open('Order status updated successfully', 'Close', {
          duration: 3000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
        });
      },
      error: (error: any) => {
        console.error('Error updating order status:', error);
        this.isUpdatingStatus = false;

        this.snackBar.open('Failed to update order status', 'Close', {
          duration: 3000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
        });
      },
    });
  }
  goBack(): void {
    this.router.navigate(['/medicine/orders']);
  }

 

  isStatusDisabled(status: string): boolean {
    if (!this.order || !this.order.status) return false;

    const currentStatus = this.order.status.toLowerCase();
    const targetStatus = status.toLowerCase();

    // Cancelled can always be selected regardless of current status
    if (targetStatus === 'cancelled') return false;

    // Status workflow logic
    switch (currentStatus) {
      case 'processing':
        // When in processing, can't go back to pending
        return targetStatus === 'pending';
      case 'shipped':
        // When shipped, can't go back to pending or processing
        return targetStatus === 'pending' || targetStatus === 'processing';
      case 'delivered':
        // When delivered, can't go back to pending, processing, or shipped
        return (
          targetStatus === 'pending' ||
          targetStatus === 'processing' ||
          targetStatus === 'shipped'
        );
      case 'cancelled':
        // When cancelled, all statuses are available (to resume the order if needed)
        return false;
      default:
        // For pending or unknown status, all options are available
        return false;
    }
  }

  getStatusButtonClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'border-amber-200 bg-amber-50 text-amber-700 hover:bg-amber-100 dark:border-amber-800 dark:bg-amber-900/30 dark:text-amber-300 dark:hover:bg-amber-900/50';
      case 'processing':
        return 'border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-900/50';
      case 'shipped':
        return 'border-purple-200 bg-purple-50 text-purple-700 hover:bg-purple-100 dark:border-purple-800 dark:bg-purple-900/30 dark:text-purple-300 dark:hover:bg-purple-900/50';
      case 'delivered':
        return 'border-green-200 bg-green-50 text-green-700 hover:bg-green-100 dark:border-green-800 dark:bg-green-900/30 dark:text-green-300 dark:hover:bg-green-900/50';
      case 'cancelled':
        return 'border-red-200 bg-red-50 text-red-700 hover:bg-red-100 dark:border-red-800 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50';
      default:
        return 'border-gray-200 bg-gray-50 text-gray-700 hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700';
    }
  }

  getStatusProgressPercentage(): number {
    if (!this.order || !this.order.status) return 0;

    const statusOrder = {
      pending: 25,
      processing: 50,
      shipped: 75,
      delivered: 100,
      cancelled: 0,
    };

    const currentStatus = this.order.status.toLowerCase();
    return statusOrder[currentStatus as keyof typeof statusOrder] || 0;
  }

  getStatusProgressColorClass(): string {
    if (!this.order || !this.order.status) return 'bg-gray-300';

    const currentStatus = this.order.status.toLowerCase();

    switch (currentStatus) {
      case 'pending':
        return 'bg-amber-500';
      case 'processing':
        return 'bg-blue-500';
      case 'shipped':
        return 'bg-purple-500';
      case 'delivered':
        return 'bg-green-500';
      case 'cancelled':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  }

  formatDate(date: any): string {
    if (!date) return 'N/A';

    try {
      return date.toLocaleString();
    } catch (error) {
      return 'Invalid Date';
    }
  }

  createMedicineFromOrderItem(orderItem: any): any {
    // Convert OrderItem to MedicineCareProduct format for display
    // This shows the medicine information as it was when the order was placed
    return {
      id: orderItem.medicineId,
      name: orderItem.medicineName,
      description: `Ordered medicine - ${orderItem.medicineName}`,
      category: orderItem.medicineCategory || 'General',
      price: orderItem.unitPrice,
      stockQuantity: orderItem.quantity, // Show ordered quantity
      image: '', // Order items don't store images
      expiryDate: new Date(),
      manufacturerDate: new Date(),
      // Additional order-specific information
      orderedQuantity: orderItem.quantity,
      totalPrice: orderItem.totalPrice,
    };
  }
}
