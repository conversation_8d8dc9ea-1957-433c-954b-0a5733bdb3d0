<div class=" p-5">
  <!-- <PERSON> Header -->
  <div
    class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-5 ">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold text-gray-900 dark:!text-white">{{pageTitle}}</h1>
      <button (click)="goBack()"
        class="inline-flex items-center px-4 py-2 border border-gray-300 dark:!border-gray-700 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:!text-gray-100 bg-white dark:!bg-gray-800 hover:bg-gray-50 dark:hover:!bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
          fill="currentColor">
          <path fill-rule="evenodd"
            d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
            clip-rule="evenodd" />
        </svg>
        Back
      </button>
    </div>
    <p class="mt-2 text-sm text-gray-600 dark:!text-gray-300">
      {{ isEditing ? 'Edit existing educational content' : 'Create new educational content for patients' }}
    </p>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center p-12">
    <div class="flex flex-col items-center">
      <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
      <p class="mt-4 text-sm text-gray-500">{{ isEditing ? 'Loading educational content...' : 'Creating new
        educationalcontent...' }}</p>
    </div>
  </div>

  <!-- Form -->
  <div *ngIf="!isLoading" class="bg-white dark:bg-gray-900 rounded-lg shadow overflow-hidden">
    <form #educationalForm="ngForm" (ngSubmit)="saveEducational()" class="p-6">
      <div class="space-y-6">
        <!-- Title -->
        <div>
          <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Title <span
              class="text-red-500">*</span></label>
          <div class="mt-1">
            <input type="text" id="title" name="title" [(ngModel)]="educational.title" required
              class="block w-full px-3 py-2 text-base border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="Enter the title">
          </div>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-300">Enter a title for this content</p>
        </div>
        <!-- Category -->
        <div>
          <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Category <span
              class="text-red-500">*</span></label>
          <div class="relative mt-1">
            <select id="category" name="category" [(ngModel)]="educational.category" required
              class="block w-full pl-3 pr-12 py-2 text-base border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 appearance-none">
              <option disabled value="">Select a category</option>
              <option *ngFor="let category of categoryOptions" [value]="category">{{category}}</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
              <svg class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-300">Select the most appropriate category for this content
          </p>
        </div>

        <!-- Feature Image -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-100">Feature Image</label>

          <!-- Image Preview -->
          <div *ngIf="imagePreview || isImageLoading" class="mt-2 flex justify-center">
            <div class="relative">
              <!-- Loading Indicator -->
              <div *ngIf="isImageLoading"
                class="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75 rounded-lg">
                <div class="animate-spin rounded-full h-10 w-10 border-4 border-blue-500 border-t-transparent"></div>
              </div>

              <!-- Image -->
              <img *ngIf="imagePreview && !isImageLoading" [src]="imagePreview" alt="Feature image preview"
                class="h-64 w-full object-cover rounded-lg shadow-sm" (error)="onImageError($event)">

              <!-- Remove Button -->
              <button type="button" *ngIf="imagePreview && !isImageLoading" (click)="removeImage()"
                class="absolute -top-2 -right-2 bg-red-500 dark:bg-red-900 text-white rounded-full p-1 shadow-sm hover:bg-red-600 focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Error State -->
          <div *ngIf="imageLoadError" class="mt-2 rounded-md dark:bg-gray-900 bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                  fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Failed to load image</h3>
              </div>
            </div>
          </div>

          <!-- Upload Button -->
          <div *ngIf="!imagePreview && !isImageLoading" class="mt-2">
            <div class="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
              <div class="space-y-1 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div class="flex text-sm text-gray-600">
                  <label for="file-upload"
                    class="relative cursor-pointer bg-white dark:bg-gray-900 rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                    <span>Upload a file</span>
                    <input #fileInput id="file-upload" name="file-upload" type="file" class="sr-only"
                      (change)="onFileSelected($event)" accept="image/*">
                  </label>
                  <p class="pl-1">or drag and drop</p>
                </div>
                <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
              </div>
            </div>
          </div>

          <!-- Upload Progress/Error -->
          <div *ngIf="isUploading" class="mt-2">
            <div class="flex items-center">
              <div class="mr-3 animate-spin rounded-full h-5 w-5 border-2 border-blue-500 border-t-transparent"></div>
              <span class="text-sm text-gray-700">Uploading image...</span>
            </div>
          </div>
          <div *ngIf="uploadError" class="mt-1 text-sm text-red-600">{{uploadError}}</div>
        </div>

        <!-- Content -->
        <div>
          <div class="flex justify-between items-center mb-2">
            <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Content <span
                class="text-red-500">*</span></label>
            <!-- Test buttons for development -->
            <div class="flex gap-2" *ngIf="!isLoading">
              <button type="button" (click)="addSampleContent()"
                class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 dark:text-white hover:bg-gray-200 text-gray-700 rounded">
                Add Sample
              </button>
              <button type="button" (click)="testEditor()"
                class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 dark:text-white hover:bg-gray-200 text-gray-700 rounded">
                Test Editor
              </button>
            </div>
          </div>
          <div class="mt-1">
            <!-- EditorJS Container -->
            <div #editorContainer id="editorContainer" class="codex-editor w-full border border-gray-300 rounded-md">
            </div>
            <!-- Hidden input to keep form validation working -->
            <input type="hidden" name="content" [(ngModel)]="educational.content" required>
          </div>

          <!-- Editor Status Messages -->
          <div *ngIf="!editorReady" class="mt-1 text-xs text-gray-500 flex items-center">
            <svg class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
              </path>
            </svg>
            Initializing editor...
          </div>

          <div *ngIf="editorReady && !hasEditorContent" class="mt-1 text-xs text-red-400">
            Please add some content to continue
          </div>

          <div *ngIf="editorReady && hasEditorContent" class="mt-1 text-xs text-green-600">
            ✓ Content added
          </div>

          <p class="mt-1 text-xs dark:text-gray-200 text-gray-500">Use the toolbar to format your content with headings,
            lists, and media</p>
        </div>

        <!-- Reading Time -->
        <div>
          <label for="readingTime" class="block text-sm font-medium text-gray-700 dark:text-gray-200">Reading
            Time</label>
          <div class="mt-1 flex items-center gap-4 rounded-md shadow-sm">
            <input type="text" name="readingTime" id="readingTime" [(ngModel)]="educational.readingTime"
              class="focus:ring-blue-500 focus:border-blue-500 px-4  sm:text-sm border-gray-300 rounded-md py-2"
              placeholder="Auto-calculated">
            <div class=" flex items-center text-gray-500">
              <span>min read</span>
            </div>
          </div>
          <p class="mt-1 text-xs text-gray-500">Will be auto-calculated if left empty</p>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-4">
          <button type="button" (click)="goBack()"
            class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-white dark:border dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Cancel
          </button>
          <button type="submit" [disabled]="!isFormValid()"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-600 disabled:opacity-50 disabled:cursor-not-allowed">
            <span *ngIf="!isLoading">{{ isEditing ? 'Update' : 'Save' }}</span>
            <span *ngIf="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
              {{ isEditing ? 'Updating...' : 'Saving...' }}
            </span>
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
