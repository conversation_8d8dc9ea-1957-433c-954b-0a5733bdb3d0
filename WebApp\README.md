# User Management System

A comprehensive Angular-based user management dashboard with role-based access control (RBAC), modern UI/UX design, and robust security features.

## 🚀 Features

### User Management
- **User Registration** - Secure user signup with password strength validation
- **User Authentication** - Login/logout with JWT token management
- **User Profile Management** - View and edit personal information
- **User List Management** - Admin view of all users with search and filtering
- **User Status Management** - Activate/deactivate users

### Role-Based Access Control (RBAC)
- **Role Management** - Create, edit, and delete user roles
- **Permission System** - Granular permissions for different resources
- **Role Assignment** - Assign multiple roles to users
- **Access Control** - Route-level and component-level permission checks

### Security Features
- **JWT Token Authentication** - Secure token-based authentication
- **Password Strength Validation** - Real-time password strength checking
- **Rate Limiting** - Login attempt rate limiting to prevent brute force attacks
- **Input Sanitization** - XSS protection through input sanitization
- **Token Expiration** - Automatic token validation and expiration handling
- **Secure Storage** - Secure token storage with automatic cleanup

### Modern UI/UX
- **Tailwind CSS** - Modern, responsive design system
- **Professional Aesthetics** - Clean, accessible interface design
- **Interactive Components** - Loading states, form validation, and user feedback
- **Mobile Responsive** - Optimized for all device sizes
- **Accessibility** - WCAG compliant design patterns

## 🛠️ Technology Stack

- **Frontend**: Angular 19 with standalone components
- **Styling**: Tailwind CSS 4.x
- **UI Components**: Angular Material + Custom components
- **State Management**: RxJS Observables
- **Authentication**: JWT tokens with HTTP interceptors
- **Build Tool**: Angular CLI with Vite
- **TypeScript**: Strict mode enabled

## 📦 Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd webApp
```

2. **Install dependencies**
```bash
npm install
```

3. **Start the development server**
```bash
npm start
# or
ng serve
```

4. **Open your browser**
Navigate to `http://localhost:4200/`

## 🔐 Demo Credentials

For testing purposes, use these demo credentials:

**Admin User:**
- Email: `<EMAIL>`
- Password: `admin123`

**Features accessible with admin account:**
- Full user management
- Role management
- Permission assignment
- System administration

## 🏗️ Project Structure

```
src/
├── app/
│   ├── features/           # Feature modules
│   │   ├── auth/          # Authentication (login, register)
│   │   ├── user/          # User management (profile, user-list)
│   │   ├── role/          # Role management
│   │   └── dashboard/     # Main dashboard
│   ├── shared/            # Shared components and utilities
│   │   ├── components/    # Reusable UI components
│   │   ├── layout/        # Layout components
│   │   └── pages/         # Shared pages (unauthorized, etc.)
│   ├── services/          # Business logic services
│   ├── guards/            # Route guards for security
│   ├── interceptors/      # HTTP interceptors
│   └── models/            # TypeScript interfaces and types
```

## 🔑 Key Components

### Authentication System
- **AuthService** - Handles login, logout, token management
- **AuthGuard** - Protects routes based on authentication status
- **AuthInterceptor** - Automatically adds auth tokens to HTTP requests
- **SecurityUtils** - Password validation, rate limiting, input sanitization

### User Management
- **UserService** - CRUD operations for user management
- **ProfileComponent** - User profile editing interface
- **UserListComponent** - Admin interface for managing all users

### Role Management
- **RoleService** - Role and permission management
- **RoleManagementComponent** - Interface for creating and editing roles
- **Permission System** - Granular access control

### UI Components
- **ButtonComponent** - Consistent button styling with variants
- **FormFieldComponent** - Standardized form inputs with validation
- **LoadingSpinnerComponent** - Loading state indicators
- **MainLayoutComponent** - Application layout with navigation

## 🛡️ Security Features

### Authentication & Authorization
- JWT token-based authentication
- Automatic token refresh handling
- Role-based route protection
- Permission-based component access

### Security Measures
- Password strength validation with real-time feedback
- Login attempt rate limiting (5 attempts per 15 minutes)
- XSS protection through input sanitization
- Secure token storage with automatic cleanup
- CSRF protection ready (tokens can be easily added)

### Data Validation
- Client-side form validation
- Email format validation
- Password complexity requirements
- Username pattern validation

## 🎨 Design System

### Tailwind CSS Configuration
- Custom color palette for consistent branding
- Responsive design utilities
- Accessibility-focused components
- Modern gradient backgrounds and shadows

### Component Design Patterns
- Consistent spacing and typography
- Interactive states (hover, focus, disabled)
- Loading and error states
- Form validation feedback
- Mobile-first responsive design

## 🚦 Usage Guide

### For Regular Users
1. **Registration**: Create account with strong password
2. **Login**: Access dashboard with credentials
3. **Profile Management**: Update personal information
4. **Dashboard**: View system overview and quick actions

### For Administrators
1. **User Management**: View, edit, activate/deactivate users
2. **Role Management**: Create custom roles with specific permissions
3. **Permission Assignment**: Assign roles to users
4. **System Monitoring**: Track user activity and system stats

## 🔧 Development

### Adding New Features
1. Create feature module in `src/app/features/`
2. Add routing configuration
3. Implement service layer for business logic
4. Create components with consistent styling
5. Add appropriate guards and permissions

### Security Considerations
- Always validate user input
- Use the SecurityUtils for common security operations
- Implement proper error handling
- Follow the established authentication patterns
- Test permission-based access thoroughly

### Styling Guidelines
- Use Tailwind utility classes
- Follow the established color scheme
- Maintain consistent spacing (4, 6, 8, 12, 16, 24px)
- Ensure accessibility compliance
- Test on multiple screen sizes

## 🧪 Testing

### Running Tests
```bash
# Unit tests
npm test

# E2E tests (when configured)
npm run e2e
```

### Test Coverage
- Authentication flows
- User management operations
- Role and permission assignments
- Form validation
- Security features

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Environment Configuration
- Update API endpoints in environment files
- Configure proper JWT secret keys
- Set up HTTPS for production
- Configure CORS policies
- Set up proper logging

## 📝 API Integration

The current implementation uses mock services. To integrate with a real backend:

1. **Replace mock services** with HTTP calls
2. **Update AuthService** to use real authentication endpoints
3. **Configure API base URLs** in environment files
4. **Implement error handling** for network requests
5. **Add request/response interceptors** as needed

### Expected API Endpoints
```
POST /auth/login
POST /auth/register
POST /auth/logout
GET /auth/me
PUT /auth/profile

GET /users
POST /users
PUT /users/:id
DELETE /users/:id

GET /roles
POST /roles
PUT /roles/:id
DELETE /roles/:id

GET /permissions
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Follow the established code patterns
4. Add appropriate tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For questions or issues:
1. Check the documentation
2. Review the demo credentials and features
3. Examine the code examples
4. Create an issue for bugs or feature requests

---

**Built with ❤️ using Angular and Tailwind CSS**
