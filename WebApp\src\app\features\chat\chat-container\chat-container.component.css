/* Mobile-first responsive chat container using only Tailwind CSS */
/* All styling is handled through Tailwind utility classes in the HTML template */

/* Custom animations for enhanced mobile experience */
@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Mobile touch optimization */
@media (max-width: 768px) {
  /* Ensure touch targets are at least 44px */
  button, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve scroll performance on mobile */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  /* Enhance readability in dark mode */
  .text-gray-600 {
    color: rgb(156 163 175);
  }

  .text-gray-900 {
    color: rgb(243 244 246);
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
