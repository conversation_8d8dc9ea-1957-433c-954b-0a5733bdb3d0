/* User list component specific styles */
.header-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-container {
  width: 100%;
  margin-bottom: 0.5rem;
}

/* Force light mode styles for search input */
.search-container input.search-input {
  height: 38px;
  width: 100%;
  padding-left: 2.5rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border: 1px solid #d1d5db ;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: #ffffff ;
  color: #374151 ;
  transition: all 0.2s ease;
}

.search-container input.search-input::placeholder {
  color: #9ca3af !important;
}

.search-container input.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5) !important;
  border-color: #3b82f6 !important;
}

/* Dark mode styles for search input */
@media (prefers-color-scheme: dark) {
  .search-container input.search-input {
    border-color: #4b5563 !important;
    background-color: #374151 !important;
    color: #f3f4f6 !important;
  }

  .search-container input.search-input::placeholder {
    color: #9ca3af !important;
  }

  .search-container input.search-input:focus {
    border-color: #60a5fa !important;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5) !important;
  }
}

:root.dark .search-container input.search-input {
  border-color: #4b5563 !important;
  background-color: #374151 !important;
  color: #f3f4f6 !important;
}

:root.dark .search-container input.search-input::placeholder {
  color: #9ca3af !important;
}

:root.dark .search-container input.search-input:focus {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5) !important;
}

.filters-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

/* Force light mode styles for filter select */
.filters-container select.filter-select {
  height: 38px;
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 0.875rem;
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-size: 1.5em 1.5em;
  background-color: #ffffff !important;
  color: #374151 !important;
  appearance: none;
  transition: all 0.2s ease;
}

.filters-container select.filter-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5) !important;
  border-color: #3b82f6 !important;
}

/* Dark mode styles for filter select */
/* @media (prefers-color-scheme:  .dark) {
  .filters-container select.filter-select {
    border-color: #4b5563 !important;
    background-color: #374151 !important;
    color: #f3f4f6 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  }

  .filters-container select.filter-select:focus {
    border-color: #60a5fa !important;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5) !important;
  }
} */

:root.dark .filters-container select.filter-select {
  border-color: #4b5563 !important;
  background-color: #374151 !important;
  color: #f3f4f6 !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}

:root.dark .filters-container select.filter-select:focus {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.5) !important;
}

/* Table styles */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

th {
  position: sticky;
  top: 0;
  z-index: 10;
  transition: background-color 0.2s ease;
}

/* Only override tbody styling when no specific classes are applied */
tbody tr:not([class*="bg-"]) {
  background-color: #ffffff;
}

tbody tr:not([class*="bg-"]):hover {
  background-color: #f9fafb;
  transition: background-color 0.2s ease;
}

/* Dark mode styles for table */
@media (prefers-color-scheme: dark) {
  tbody tr:not([class*="bg-"]) {
    background-color: #1f2937;
  }

  tbody tr:not([class*="bg-"]):hover {
    background-color: #374151;
  }
}

:root.dark tbody tr:not([class*="bg-"]) {
  background-color: #1f2937;
}

:root.dark tbody tr:not([class*="bg-"]):hover {
  background-color: #374151;
}

/* Role badges */
.role-badge {
  position: relative;
  transition: all 0.2s ease;
  cursor: default;
  padding-right: 0.75rem;
}

.role-badge:hover {
  transform: scale(1.05);
}

.role-badge:hover .role-remove-btn {
  opacity: 1;
}

.role-remove-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
  position: relative;
  top: 0;
  right: -2px;
}

.role-add-btn {
  opacity: 0.7;
  transition: all 0.2s ease;
}

.role-add-btn:hover {
  opacity: 1;
  transform: scale(1.15);
}

/* Role Popover */
.role-popover {
  min-width: 220px;
  animation: fadeIn 0.2s ease;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  z-index: 50;
}

/* Loading spinner */
.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive layouts */
@media (min-width: 768px) {
  .header-container {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  .header-right {
    flex-direction: row;
    align-items: center;
    min-width: 600px;
    justify-content: flex-end;
    gap: 1rem;
  }

  .search-container {
    width: 220px;
    margin-bottom: 0;
  }

  .filters-container {
    flex-direction: row;
    width: auto;
    gap: 0.5rem;
  }

  .filter-select {
    width: 120px;
  }
}

/* For smaller screens, make sure the table is scrollable */
@media (max-width: 767px) {
  .bg-white.rounded-lg.overflow-hidden {
    overflow-x: auto;
  }

  table {
    min-width: 650px;
  }

  .header-right {
    width: 100%;
  }

  .role-popover {
    position: fixed;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 300px;
  }
}

/* Skills text wrap */
td .text-sm {
  white-space: normal;
  word-break: break-word;
  max-width: 200px;
}

/* User avatar styles */
.flex-shrink-0.h-10.w-10 {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e5e7eb;
  color: #4b5563;
  font-weight: 600;
  text-transform: uppercase;
}

/* Hover effects */
.hover\:bg-indigo-50:hover {
  background-color: #eef2ff;
}

.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.hover\:bg-indigo-700:hover {
  background-color: #4338ca;
}

.hover\:text-indigo-900:hover {
  color: #312e81;
}

/* Checkbox styles in role modal */
input[type="checkbox"] {
  cursor: pointer;
}

input[type="checkbox"]:checked {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

input[type="checkbox"]:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
}

/* Disabled button styles */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Delete confirmation dialog styles */
.animate-fadeIn {
  animation: fadeIn 0.2s ease-out;
}
