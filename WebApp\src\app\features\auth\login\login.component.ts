import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { ButtonComponent } from '../../../shared/components/button/button.component';
import { AuthServiceProxy, LoginDto, TokenResponseDto } from '../../../../shared/service-proxies/service-proxies';
import { AuthService, AuthTokenData, AuthUser } from '../../../../shared/services/auth.service';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';

// User credentials interface
interface UserCredentials {
  email: string;
  password: string;
}

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ButtonComponent,
    ServiceProxyModule
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  user: UserCredentials = {
    email: '',
    password: '',
  };

  isLoading = false;
  errorMessage = '';
  returnUrl = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private _authService: AuthServiceProxy,
    private auth: AuthService,



  ) { }

  ngOnInit(): void {
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';

    // Check for error messages from query parameters
    const error = this.route.snapshot.queryParams['error'];
    if (error === 'unauthorized_role') {
      this.errorMessage = 'Access denied. This portal is for administrators and doctors only. Please contact support if you believe this is an error.';
    } else if (error === 'patient_access_denied') {
      this.errorMessage = 'This portal is for administrators and doctors only. Please use the mobile app for patient access.';
    } else if (error === 'session_expired') {
      this.errorMessage = 'Your session has expired. Please log in again.';
    }

    // Check if user is already logged in
    if (this.auth.isUserLoggedIn) {
      const user = this.auth.getUser();
      console.log('User already logged in:', user);

      // Redirect based on role
      if (user && user.role && user.role.includes('Admin')) {
        this.router.navigate(['/dashboard']);
      } else {
        this.router.navigate([this.returnUrl]);
      }
    }
  }

  onSubmit() {
    // Clear any previous error messages
    this.errorMessage = '';

    // Validate form inputs
    if (!this.user.email || !this.user.password) {
      this.errorMessage = 'Please enter both email and password.';
      return;
    }

    if (!this.isValidEmail(this.user.email)) {
      this.errorMessage = 'Please enter a valid email address.';
      return;
    }

    this.isLoading = true;

    // Log the form values for debugging
    console.log('Login attempt with:', this.user.email);

    const loginDto = { email: this.user.email, password: this.user.password } as LoginDto;

    // Call the actual API service for authentication
    this._authService.login(loginDto).subscribe(
      (res) => {
        this.isLoading = false;
        console.log('Login response:', res);
        if (res === null) {
          this.errorMessage = 'Login failed. Please try again.';
          return;
        }

        // Create user data from response for auth service
        const userData = {
          id: this.user.email, // Use email as ID since UserDto doesn't have an id property
          email: res.user?.email || this.user.email,
          firstName: res.user?.name,
          role: res.user?.roles || []
        };

        // Validate user role for web app (Admin/Doctor only, no regular Users)
        const userRole: string[] | string = userData.role || [];
        const allowedRoles = ['Admin', 'Doctor'];
        const hasValidRole = allowedRoles.some(role => {
          if (Array.isArray(userRole)) {
            return (userRole as string[]).includes(role);
          } else if (typeof userRole === 'string') {
            return (userRole as string).includes(role);
          }
          return false;
        });

        if (!hasValidRole) {
          this.errorMessage = 'Access denied. This portal is for administrators and doctors only. Please use the mobile app for patient access.';
          this.auth.logout(); // Clear any tokens that might have been set
          return;
        }

        // Create token data for auth service
        const tokenData = {
          token: res.token || '',
          expiresAt: res.expiration,
          refreshToken: '' // Add refresh token if your API provides it
        };

        // Save the complete user session using our auth service
        this.auth.saveUserSession(userData, tokenData);

        console.log('User authenticated successfully:', userData);

        // Redirect based on role if user is admin
        if (userData.role.includes('Admin')) {
          this.router.navigate(['/dashboard']);
        } else {
          // Otherwise go to requested return URL or default
          this.router.navigate([this.returnUrl]);
        }
      },
      (error) => {
        console.error('Login error:', error);
        this.isLoading = false;

        // Handle different types of errors
        let errorMsg = 'An error occurred during login. Please try again.';

        if (error.status === 401) {
          errorMsg = 'Invalid email or password. Please check your credentials and try again.';
        } else if (error.status === 403) {
          errorMsg = 'Access denied. You do not have permission to access this system.';
        } else if (error.status === 0) {
          errorMsg = 'Unable to connect to the server. Please check your internet connection.';
        } else if (error.error?.message) {
          errorMsg = error.error.message;
        } else if (error.message) {
          errorMsg = error.message;
        }

        this.handleInvalidToken(errorMsg);
      }
    );

  }

  private handleInvalidToken(errorMessage: string): void {
    this.isLoading = false;
    this.errorMessage = errorMessage;
    this.auth.logout(); // Clear any existing invalid tokens
    console.error('Authentication error:', errorMessage);
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  clearErrorMessage(): void {
    this.errorMessage = '';
  }
}
