<div class=" p-5 bg-white dark:bg-gray-900 transition-colors duration-300">
  <!-- Header with gradient background -->
  <div
    class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 overflow-hidden p-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="bg-blue-500 dark:bg-blue-700 p-2 rounded-lg mr-3">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
          </svg>
        </div>
        <div>
          <h1 class="text-xl font-bold text-gray-800 dark:text-white">{{ pageTitle }}</h1>
          <p class="text-gray-600 dark:text-gray-300 text-sm">
            {{ isEditing ? 'Update medicine details, stock levels, and image' : 'Add a new medicine to your inventory'
            }}
          </p>
        </div>
      </div>
      <button (click)="goBack()"
        class="back-btn">
        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to List
      </button>
    </div>
  </div>
  <!-- Main Content Area -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-300">
    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="flex justify-center items-center p-12">
      <div class="flex flex-col items-center">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 dark:border-blue-400 border-t-transparent"></div>
        <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">{{ isEditing ? 'Loading medicine details...' : 'Preparing form...' }}</p>
      </div>
    </div>

    <div *ngIf="!isLoading" class="p-6">
      <form #medicineForm="ngForm" (ngSubmit)="onSubmit()" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Left Column - Basic Info -->
          <div class="lg:col-span-2 space-y-6">
            <div>
              <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">Basic Information</h2>

              <!-- Name -->
              <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Medicine Name</label>
                <input type="text" id="name" name="name" [(ngModel)]="medicine.name" #name="ngModel" required
                  maxlength="100"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                  [ngClass]="{'border-red-500 ring-red-500': name.invalid && name.touched}">
                <div *ngIf="name.invalid && name.touched" class="text-red-500 dark:text-red-400 text-sm mt-1">
                  <div *ngIf="name.errors?.['required']">Medicine name is required</div>
                </div>
              </div>

              <!-- Description -->
              <div class="mb-4">
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                <textarea id="description" name="description" [(ngModel)]="medicine.description" rows="3"
                  placeholder="Enter a detailed description of the medicine"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"></textarea>
              </div>

              <!-- Category -->
              <div class="mb-4">
                <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
                <div class="flex gap-2">
                  <select id="category" name="category" [(ngModel)]="medicine.category" #category="ngModel" required
                    class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                    [ngClass]="{'border-red-500 ring-red-500': category.invalid && category.touched}">
                    <option value="" disabled>Select a category</option>
                    <option *ngFor="let option of categoryOptions" [value]="option">{{ option }}</option>
                  </select>
                  <button type="button" 
                    (click)="openAddCategoryDialog()"
                    class="px-3 py-2 bg-green-600 dark:bg-green-700 text-white rounded-lg hover:bg-green-700 dark:hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 transition-colors flex items-center justify-center min-w-[44px]"
                    title="Add new category">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                  </button>
                </div>
                <div *ngIf="category.invalid && category.touched" class="text-red-500 dark:text-red-400 text-sm mt-1">
                  <div *ngIf="category.errors?.['required']">Category is required</div>
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Select an existing category or click the + button to add a new one
                </div>
              </div>
            </div>

            <div>
              <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">Dates & Inventory</h2>

              <!-- Two columns for dates -->
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <!-- Expiry Date -->
                <div>
                  <label for="expiryDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Expiry Date</label>
                  <input type="date" id="expiryDate" name="expiryDate" [(ngModel)]="medicine.expiryDate"
                    #expiryDate="ngModel" required
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                    [ngClass]="{'border-red-500 ring-red-500': expiryDate.invalid && expiryDate.touched}">
                  <div *ngIf="expiryDate.invalid && expiryDate.touched" class="text-red-500 dark:text-red-400 text-sm mt-1">
                    <div *ngIf="expiryDate.errors?.['required']">Expiry date is required</div>
                  </div>
                </div>

              </div>

              <!-- Two columns for price and stock -->
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <!-- Price -->
                <div>
                  <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Price</label>
                  <div class="relative">
                    <span class="absolute left-3 top-2 text-gray-500 dark:text-gray-400">$</span>
                    <input type="number" step="0.01" id="price" name="price" [(ngModel)]="medicine.price"
                      #price="ngModel" required min="0"
                      class="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                      [ngClass]="{'border-red-500 ring-red-500': price.invalid && price.touched}">
                  </div>
                  <div *ngIf="price.invalid && price.touched" class="text-red-500 dark:text-red-400 text-sm mt-1">
                    <div *ngIf="price.errors?.['required']">Price is required</div>
                    <div *ngIf="price.errors?.['min']">Price must be positive</div>
                  </div>
                </div>

                <!-- Stock Quantity -->
                <div>
                  <label for="stockQuantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Stock Quantity</label>
                  <input type="number" id="stockQuantity" name="stockQuantity" [(ngModel)]="medicine.stockQuantity"
                    #stockQuantity="ngModel" required min="0"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100"
                    [ngClass]="{'border-red-500 ring-red-500': stockQuantity.invalid && stockQuantity.touched}">
                  <div *ngIf="stockQuantity.invalid && stockQuantity.touched" class="text-red-500 dark:text-red-400 text-sm mt-1">
                    <div *ngIf="stockQuantity.errors?.['required']">Stock quantity is required</div>
                    <div *ngIf="stockQuantity.errors?.['min']">Stock quantity must be positive</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column - Image Upload -->
          <div>
            <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">Medicine Image</h2>

            <!-- Image Upload Card -->
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4 transition-colors duration-300">
              <!-- File input -->
              <div class="flex flex-col mb-4">
                <input #fileInput type="file" accept="image/*" class="hidden" (change)="onFileSelected($event)" />
                <button type="button" (click)="fileInput.click()"
                  class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors mb-2"
                  [disabled]="isUploading">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  {{ isUploading ? 'Uploading...' : 'Choose Image' }}
                </button>
                <span class="text-sm text-gray-500 dark:text-gray-400 text-center" *ngIf="selectedFile">
                  {{ selectedFile.name }} ({{ (selectedFile.size / 1024).toFixed(2) }} KB)
                </span>
              </div>

              <!-- Upload error message -->
              <div *ngIf="uploadError" class="text-red-500 dark:text-red-400 text-sm mb-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-2 rounded">
                <div class="flex items-center">
                  <svg class="h-4 w-4 mr-1 text-red-500 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd" />
                  </svg>
                  {{ uploadError }}
                </div>
              </div>

              <!-- Image preview with loading indicator and error handling -->
              <div *ngIf="imagePreview || (medicine.imageUrl && !imagePreview)" class="mt-2 mb-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Preview:</span>
                  <button type="button" (click)="removeImage()"
                    class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 flex items-center text-sm font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Remove
                  </button>
                </div>

                <div class="border rounded-lg overflow-hidden w-full relative bg-white dark:bg-gray-800">
                  <!-- Loading spinner -->
                  <div *ngIf="isImageLoading"
                    class="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-900 bg-opacity-80">
                    <div class="w-8 h-8 border-4 border-t-blue-500 dark:border-t-blue-400 border-blue-200 dark:border-blue-900 rounded-full animate-spin"></div>
                  </div>

                  <!-- Image -->
                  <img *ngIf="imagePreview" [src]="imagePreview" class="w-full h-auto max-h-64 object-contain"
                    alt="Medicine preview" />

                  <!-- Fallback for existing image from DB -->
                  <img *ngIf="!imagePreview && medicine.imageUrl"
                    [src]="baseUrl + '/api/File/Getfile/' + medicine.imageUrl"
                    class="w-full h-auto max-h-64 object-contain" alt="Medicine image" (error)="imageLoadError = true"
                    [class.hidden]="imageLoadError" />

                  <!-- Error placeholder -->
                  <div *ngIf="imageLoadError"
                    class="bg-gray-100 dark:bg-gray-900 w-full h-64 flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p>Image could not be loaded</p>
                  </div>
                </div>
              </div>

              <div *ngIf="!imagePreview && !medicine.imageUrl"
                class="bg-gray-100 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-6 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500 mb-2" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p class="text-sm text-gray-500 dark:text-gray-400">No image selected</p>
                <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">Click "Choose Image" to upload</p>
              </div>

              <input type="hidden" id="image" name="image" [(ngModel)]="medicine.imageUrl">
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-8">
          <div class="flex justify-end space-x-4">
            <button type="button" (click)="goBack()"
              class="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-gray-400 text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800">
              Cancel
            </button>
            <button type="submit" [disabled]="medicineForm.invalid || isLoading || isUploading"
              class="px-6 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors disabled:bg-blue-300 dark:disabled:bg-blue-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 disabled:cursor-not-allowed">
              <div class="flex items-center">
                <svg *ngIf="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                {{ isEditing ? 'Update' : 'Save' }} Medicine
              </div>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
