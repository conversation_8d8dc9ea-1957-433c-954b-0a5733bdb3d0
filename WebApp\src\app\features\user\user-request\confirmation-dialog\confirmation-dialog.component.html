<div class="dialog-header dark:!border-gray-900 rounded-t-lg flex items-center gap-2 px-4 py-3"
     [ngClass]="{
       'bg-amber-600 dark:!bg-amber-700': data.type === 'warning',
       'bg-red-600 dark:!bg-red-700': data.type === 'danger',
       'bg-blue-600 dark:!bg-blue-700': data.type === 'info'
     }">
  <div class="dialog-icon" *ngIf="data.type === 'warning'">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white dark:!text-gray-100">
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
      <line x1="12" y1="9" x2="12" y2="13"></line>
      <line x1="12" y1="17" x2="12.01" y2="17"></line>
    </svg>
  </div>
  <div class="dialog-icon" *ngIf="data.type === 'danger'">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white dark:!text-gray-100">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="15" y1="9" x2="9" y2="15"></line>
      <line x1="9" y1="9" x2="15" y2="15"></line>
    </svg>
  </div>
  <div class="dialog-icon" *ngIf="data.type === 'info'">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white dark:!text-gray-100">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="12" y1="16" x2="12" y2="12"></line>
      <line x1="12" y1="8" x2="12.01" y2="8"></line>
    </svg>
  </div>
  <h2 class="dialog-title text-white dark:!text-gray-100 text-lg font-semibold">{{ data.title }}</h2>
</div>

<div class="dialog-content bg-white dark:!bg-gray-900 p-6 border border-gray-200 dark:border dark:!border-gray-900 transition-colors duration-200">
  <p class="dialog-message text-gray-700 dark:!text-gray-200 text-base mb-2">{{ data.message }}</p>
  <div *ngIf="data.itemName" class="item-highlight flex items-center gap-2 bg-gray-50 dark:!bg-gray-900 rounded px-3 py-2 mt-2 border border-gray-100 dark:!border-gray-700 transition-colors duration-200">
    <span class="item-label text-gray-500 dark:!text-gray-400 font-medium">Item:</span>
    <span class="item-name text-gray-900 dark:!text-gray-100 font-semibold">{{ data.itemName }}</span>
  </div>
</div>

<div class="dialog-actions  dark:!bg-gray-900 dark:!border-gray-900 flex justify-end gap-2 px-6 pb-6">
  <button class="btn btn-cancel px-4 py-2 rounded bg-gray-100 dark:!bg-gray-900 text-gray-700 dark:!text-gray-200 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:!ring-gray-800 transition-colors duration-200" (click)="onCancel()">
    {{ data.cancelText }}
  </button>
  <button class="btn px-4 py-2 rounded text-white font-semibold focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-offset-white dark:!focus:ring-offset-gray-900 transition-colors duration-200"
    [ngClass]="{
      'bg-amber-600 dark:!bg-amber-700 hover:bg-amber-700 dark:!hover:bg-amber-800 focus:ring-amber-500 dark:!focus:ring-amber-400': data.type === 'warning',
      'bg-red-600 dark:!bg-red-700 hover:bg-red-700 dark:!hover:bg-red-800 focus:ring-red-500 dark:!focus:ring-red-400': data.type === 'danger',
      'bg-blue-600 dark:!bg-blue-700 hover:bg-blue-700 dark:!hover:bg-blue-800 focus:ring-blue-500 dark:!focus:ring-blue-400': data.type === 'info'
    }"
    (click)="onConfirm()">
    {{ data.confirmText }}
  </button>
</div>
