{"version": 3, "sources": ["../../../../../../node_modules/@editorjs/header/dist/header.mjs"], "sourcesContent": ["(function () {\n  \"use strict\";\n\n  try {\n    if (typeof document < \"u\") {\n      var e = document.createElement(\"style\");\n      e.appendChild(document.createTextNode(\".ce-header{padding:.6em 0 3px;margin:0;line-height:1.25em;outline:none}.ce-header p,.ce-header div{padding:0!important;margin:0!important}\")), document.head.appendChild(e);\n    }\n  } catch (n) {\n    console.error(\"vite-plugin-css-injected-by-js\", n);\n  }\n})();\nconst a = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M19 17V10.2135C19 10.1287 18.9011 10.0824 18.836 10.1367L16 12.5\"/></svg>',\n  l = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 11C16 10 19 9.5 19 12C19 13.9771 16.0684 13.9997 16.0012 16.8981C15.9999 16.9533 16.0448 17 16.1 17L19.3 17\"/></svg>',\n  o = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 11C16 10.5 16.8323 10 17.6 10C18.3677 10 19.5 10.311 19.5 11.5C19.5 12.5315 18.7474 12.9022 18.548 12.9823C18.5378 12.9864 18.5395 13.0047 18.5503 13.0063C18.8115 13.0456 20 13.3065 20 14.8C20 16 19.5 17 17.8 17C17.8 17 16 17 16 16.3\"/></svg>',\n  h = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18 10L15.2834 14.8511C15.246 14.9178 15.294 15 15.3704 15C16.8489 15 18.7561 15 20.2 15M19 17C19 15.7187 19 14.8813 19 13.6\"/></svg>',\n  d = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 15.9C16 15.9 16.3768 17 17.8 17C19.5 17 20 15.6199 20 14.7C20 12.7323 17.6745 12.0486 16.1635 12.9894C16.094 13.0327 16 12.9846 16 12.9027V10.1C16 10.0448 16.0448 10 16.1 10H19.8\"/></svg>',\n  u = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M19.5 10C16.5 10.5 16 13.3285 16 15M16 15V15C16 16.1046 16.8954 17 18 17H18.3246C19.3251 17 20.3191 16.3492 20.2522 15.3509C20.0612 12.4958 16 12.6611 16 15Z\"/></svg>',\n  g = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9 7L9 12M9 17V12M9 12L15 12M15 7V12M15 17L15 12\"/></svg>';\n/**\n * Header block for the Editor.js.\n *\n * <AUTHOR> (<EMAIL>)\n * @copyright CodeX 2018\n * @license MIT\n * @version 2.0.0\n */\nclass v {\n  constructor({\n    data: e,\n    config: t,\n    api: s,\n    readOnly: r\n  }) {\n    this.api = s, this.readOnly = r, this._settings = t, this._data = this.normalizeData(e), this._element = this.getTag();\n  }\n  /**\n   * Styles\n   */\n  get _CSS() {\n    return {\n      block: this.api.styles.block,\n      wrapper: \"ce-header\"\n    };\n  }\n  /**\n   * Check if data is valid\n   * \n   * @param {any} data - data to check\n   * @returns {data is HeaderData}\n   * @private\n   */\n  isHeaderData(e) {\n    return e.text !== void 0;\n  }\n  /**\n   * Normalize input data\n   *\n   * @param {HeaderData} data - saved data to process\n   *\n   * @returns {HeaderData}\n   * @private\n   */\n  normalizeData(e) {\n    const t = {\n      text: \"\",\n      level: this.defaultLevel.number\n    };\n    return this.isHeaderData(e) && (t.text = e.text || \"\", e.level !== void 0 && !isNaN(parseInt(e.level.toString())) && (t.level = parseInt(e.level.toString()))), t;\n  }\n  /**\n   * Return Tool's view\n   *\n   * @returns {HTMLHeadingElement}\n   * @public\n   */\n  render() {\n    return this._element;\n  }\n  /**\n   * Returns header block tunes config\n   *\n   * @returns {Array}\n   */\n  renderSettings() {\n    return this.levels.map(e => ({\n      icon: e.svg,\n      label: this.api.i18n.t(`Heading ${e.number}`),\n      onActivate: () => this.setLevel(e.number),\n      closeOnActivate: !0,\n      isActive: this.currentLevel.number === e.number,\n      render: () => document.createElement(\"div\")\n    }));\n  }\n  /**\n   * Callback for Block's settings buttons\n   *\n   * @param {number} level - level to set\n   */\n  setLevel(e) {\n    this.data = {\n      level: e,\n      text: this.data.text\n    };\n  }\n  /**\n   * Method that specified how to merge two Text blocks.\n   * Called by Editor.js by backspace at the beginning of the Block\n   *\n   * @param {HeaderData} data - saved data to merger with current block\n   * @public\n   */\n  merge(e) {\n    this._element.insertAdjacentHTML(\"beforeend\", e.text);\n  }\n  /**\n   * Validate Text block data:\n   * - check for emptiness\n   *\n   * @param {HeaderData} blockData — data received after saving\n   * @returns {boolean} false if saved data is not correct, otherwise true\n   * @public\n   */\n  validate(e) {\n    return e.text.trim() !== \"\";\n  }\n  /**\n   * Extract Tool's data from the view\n   *\n   * @param {HTMLHeadingElement} toolsContent - Text tools rendered view\n   * @returns {HeaderData} - saved data\n   * @public\n   */\n  save(e) {\n    return {\n      text: e.innerHTML,\n      level: this.currentLevel.number\n    };\n  }\n  /**\n   * Allow Header to be converted to/from other blocks\n   */\n  static get conversionConfig() {\n    return {\n      export: \"text\",\n      // use 'text' property for other blocks\n      import: \"text\"\n      // fill 'text' property from other block's export string\n    };\n  }\n  /**\n   * Sanitizer Rules\n   */\n  static get sanitize() {\n    return {\n      level: !1,\n      text: {}\n    };\n  }\n  /**\n   * Returns true to notify core that read-only is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get current Tools`s data\n   *\n   * @returns {HeaderData} Current data\n   * @private\n   */\n  get data() {\n    return this._data.text = this._element.innerHTML, this._data.level = this.currentLevel.number, this._data;\n  }\n  /**\n   * Store data in plugin:\n   * - at the this._data property\n   * - at the HTML\n   *\n   * @param {HeaderData} data — data to set\n   * @private\n   */\n  set data(e) {\n    if (this._data = this.normalizeData(e), e.level !== void 0 && this._element.parentNode) {\n      const t = this.getTag();\n      t.innerHTML = this._element.innerHTML, this._element.parentNode.replaceChild(t, this._element), this._element = t;\n    }\n    e.text !== void 0 && (this._element.innerHTML = this._data.text || \"\");\n  }\n  /**\n   * Get tag for target level\n   * By default returns second-leveled header\n   *\n   * @returns {HTMLElement}\n   */\n  getTag() {\n    const e = document.createElement(this.currentLevel.tag);\n    return e.innerHTML = this._data.text || \"\", e.classList.add(this._CSS.wrapper), e.contentEditable = this.readOnly ? \"false\" : \"true\", e.dataset.placeholder = this.api.i18n.t(this._settings.placeholder || \"\"), e;\n  }\n  /**\n   * Get current level\n   *\n   * @returns {level}\n   */\n  get currentLevel() {\n    let e = this.levels.find(t => t.number === this._data.level);\n    return e || (e = this.defaultLevel), e;\n  }\n  /**\n   * Return default level\n   *\n   * @returns {level}\n   */\n  get defaultLevel() {\n    if (this._settings.defaultLevel) {\n      const e = this.levels.find(t => t.number === this._settings.defaultLevel);\n      if (e) return e;\n      console.warn(\"(ง'̀-'́)ง Heading Tool: the default level specified was not found in available levels\");\n    }\n    return this.levels[1];\n  }\n  /**\n   * @typedef {object} level\n   * @property {number} number - level number\n   * @property {string} tag - tag corresponds with level number\n   * @property {string} svg - icon\n   */\n  /**\n   * Available header levels\n   *\n   * @returns {level[]}\n   */\n  get levels() {\n    const e = [{\n      number: 1,\n      tag: \"H1\",\n      svg: a\n    }, {\n      number: 2,\n      tag: \"H2\",\n      svg: l\n    }, {\n      number: 3,\n      tag: \"H3\",\n      svg: o\n    }, {\n      number: 4,\n      tag: \"H4\",\n      svg: h\n    }, {\n      number: 5,\n      tag: \"H5\",\n      svg: d\n    }, {\n      number: 6,\n      tag: \"H6\",\n      svg: u\n    }];\n    return this._settings.levels ? e.filter(t => this._settings.levels.includes(t.number)) : e;\n  }\n  /**\n   * Handle H1-H6 tags on paste to substitute it with header Tool\n   *\n   * @param {PasteEvent} event - event with pasted content\n   */\n  onPaste(e) {\n    const t = e.detail;\n    if (\"data\" in t) {\n      const s = t.data;\n      let r = this.defaultLevel.number;\n      switch (s.tagName) {\n        case \"H1\":\n          r = 1;\n          break;\n        case \"H2\":\n          r = 2;\n          break;\n        case \"H3\":\n          r = 3;\n          break;\n        case \"H4\":\n          r = 4;\n          break;\n        case \"H5\":\n          r = 5;\n          break;\n        case \"H6\":\n          r = 6;\n          break;\n      }\n      this._settings.levels && (r = this._settings.levels.reduce((n, i) => Math.abs(i - r) < Math.abs(n - r) ? i : n)), this.data = {\n        level: r,\n        text: s.innerHTML\n      };\n    }\n  }\n  /**\n   * Used by Editor.js paste handling API.\n   * Provides configuration to handle H1-H6 tags.\n   *\n   * @returns {{handler: (function(HTMLElement): {text: string}), tags: string[]}}\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"H1\", \"H2\", \"H3\", \"H4\", \"H5\", \"H6\"]\n    };\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: g,\n      title: \"Heading\"\n    };\n  }\n}\nexport { v as default };"], "mappings": ";;;CAAC,WAAY;AACX;AAEA,MAAI;AACF,QAAI,OAAO,WAAW,KAAK;AACzB,UAAI,IAAI,SAAS,cAAc,OAAO;AACtC,QAAE,YAAY,SAAS,eAAe,4IAA4I,CAAC,GAAG,SAAS,KAAK,YAAY,CAAC;AAAA,IACnN;AAAA,EACF,SAAS,GAAG;AACV,YAAQ,MAAM,kCAAkC,CAAC;AAAA,EACnD;AACF,GAAG;AACH,IAAM,IAAI;AAAV,IACE,IAAI;AADN,IAEE,IAAI;AAFN,IAGE,IAAI;AAHN,IAIE,IAAI;AAJN,IAKE,IAAI;AALN,IAME,IAAI;AASN,IAAM,IAAN,MAAQ;AAAA,EACN,YAAY;AAAA,IACV,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,UAAU;AAAA,EACZ,GAAG;AACD,SAAK,MAAM,GAAG,KAAK,WAAW,GAAG,KAAK,YAAY,GAAG,KAAK,QAAQ,KAAK,cAAc,CAAC,GAAG,KAAK,WAAW,KAAK,OAAO;AAAA,EACvH;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,WAAO;AAAA,MACL,OAAO,KAAK,IAAI,OAAO;AAAA,MACvB,SAAS;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,GAAG;AACd,WAAO,EAAE,SAAS;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,GAAG;AACf,UAAM,IAAI;AAAA,MACR,MAAM;AAAA,MACN,OAAO,KAAK,aAAa;AAAA,IAC3B;AACA,WAAO,KAAK,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,UAAU,UAAU,CAAC,MAAM,SAAS,EAAE,MAAM,SAAS,CAAC,CAAC,MAAM,EAAE,QAAQ,SAAS,EAAE,MAAM,SAAS,CAAC,KAAK;AAAA,EAClK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,WAAO,KAAK,OAAO,IAAI,QAAM;AAAA,MAC3B,MAAM,EAAE;AAAA,MACR,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE;AAAA,MAC5C,YAAY,MAAM,KAAK,SAAS,EAAE,MAAM;AAAA,MACxC,iBAAiB;AAAA,MACjB,UAAU,KAAK,aAAa,WAAW,EAAE;AAAA,MACzC,QAAQ,MAAM,SAAS,cAAc,KAAK;AAAA,IAC5C,EAAE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG;AACV,SAAK,OAAO;AAAA,MACV,OAAO;AAAA,MACP,MAAM,KAAK,KAAK;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,GAAG;AACP,SAAK,SAAS,mBAAmB,aAAa,EAAE,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,GAAG;AACV,WAAO,EAAE,KAAK,KAAK,MAAM;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,GAAG;AACN,WAAO;AAAA,MACL,MAAM,EAAE;AAAA,MACR,OAAO,KAAK,aAAa;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,mBAAmB;AAC5B,WAAO;AAAA,MACL,QAAQ;AAAA;AAAA,MAER,QAAQ;AAAA;AAAA,IAEV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,WAAW;AACpB,WAAO;AAAA,MACL,OAAO;AAAA,MACP,MAAM,CAAC;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,sBAAsB;AAC/B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,OAAO;AACT,WAAO,KAAK,MAAM,OAAO,KAAK,SAAS,WAAW,KAAK,MAAM,QAAQ,KAAK,aAAa,QAAQ,KAAK;AAAA,EACtG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,KAAK,GAAG;AACV,QAAI,KAAK,QAAQ,KAAK,cAAc,CAAC,GAAG,EAAE,UAAU,UAAU,KAAK,SAAS,YAAY;AACtF,YAAM,IAAI,KAAK,OAAO;AACtB,QAAE,YAAY,KAAK,SAAS,WAAW,KAAK,SAAS,WAAW,aAAa,GAAG,KAAK,QAAQ,GAAG,KAAK,WAAW;AAAA,IAClH;AACA,MAAE,SAAS,WAAW,KAAK,SAAS,YAAY,KAAK,MAAM,QAAQ;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACP,UAAM,IAAI,SAAS,cAAc,KAAK,aAAa,GAAG;AACtD,WAAO,EAAE,YAAY,KAAK,MAAM,QAAQ,IAAI,EAAE,UAAU,IAAI,KAAK,KAAK,OAAO,GAAG,EAAE,kBAAkB,KAAK,WAAW,UAAU,QAAQ,EAAE,QAAQ,cAAc,KAAK,IAAI,KAAK,EAAE,KAAK,UAAU,eAAe,EAAE,GAAG;AAAA,EACnN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,eAAe;AACjB,QAAI,IAAI,KAAK,OAAO,KAAK,OAAK,EAAE,WAAW,KAAK,MAAM,KAAK;AAC3D,WAAO,MAAM,IAAI,KAAK,eAAe;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,eAAe;AACjB,QAAI,KAAK,UAAU,cAAc;AAC/B,YAAM,IAAI,KAAK,OAAO,KAAK,OAAK,EAAE,WAAW,KAAK,UAAU,YAAY;AACxE,UAAI,EAAG,QAAO;AACd,cAAQ,KAAK,uFAAuF;AAAA,IACtG;AACA,WAAO,KAAK,OAAO,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,IAAI,SAAS;AACX,UAAM,IAAI,CAAC;AAAA,MACT,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,KAAK;AAAA,IACP,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,KAAK;AAAA,IACP,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,KAAK;AAAA,IACP,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,KAAK;AAAA,IACP,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,KAAK;AAAA,IACP,GAAG;AAAA,MACD,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,KAAK;AAAA,IACP,CAAC;AACD,WAAO,KAAK,UAAU,SAAS,EAAE,OAAO,OAAK,KAAK,UAAU,OAAO,SAAS,EAAE,MAAM,CAAC,IAAI;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG;AACT,UAAM,IAAI,EAAE;AACZ,QAAI,UAAU,GAAG;AACf,YAAM,IAAI,EAAE;AACZ,UAAI,IAAI,KAAK,aAAa;AAC1B,cAAQ,EAAE,SAAS;AAAA,QACjB,KAAK;AACH,cAAI;AACJ;AAAA,QACF,KAAK;AACH,cAAI;AACJ;AAAA,QACF,KAAK;AACH,cAAI;AACJ;AAAA,QACF,KAAK;AACH,cAAI;AACJ;AAAA,QACF,KAAK;AACH,cAAI;AACJ;AAAA,QACF,KAAK;AACH,cAAI;AACJ;AAAA,MACJ;AACA,WAAK,UAAU,WAAW,IAAI,KAAK,UAAU,OAAO,OAAO,CAAC,GAAG,MAAM,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO;AAAA,QAC5H,OAAO;AAAA,QACP,MAAM,EAAE;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,cAAc;AACvB,WAAO;AAAA,MACL,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,UAAU;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}