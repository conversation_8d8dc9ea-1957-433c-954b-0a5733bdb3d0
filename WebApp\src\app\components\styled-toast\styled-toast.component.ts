import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { Router } from '@angular/router';
import { GlobalMessageNotificationService, ToastNotificationData } from '../../services/global-message-notification.service';

export interface ToastNotification extends ToastNotificationData {
  id: string;
  isVisible: boolean;
}

@Component({
  selector: 'app-styled-toast',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- Toast Notification Container -->
    <div class="toast-container fixed top-6 right-6 z-50 space-y-4 max-w-md" *ngIf="toastNotifications.length > 0">
      <div *ngFor="let toast of toastNotifications; trackBy: trackByToastId"
        class="toast-notification transform transition-all duration-500 ease-out"
        [class.animate-slide-in]="toast.isVisible"
        [class.animate-slide-out]="!toast.isVisible"
        [ngClass]="{
          'border-l-4 border-green-500 shadow-lg shadow-green-100/30': isSuccessToast(toast),
          'border-l-4 border-red-500 shadow-lg shadow-red-100/30': isErrorToast(toast),
          'border-l-4 border-blue-500 shadow-lg shadow-blue-100/30': isChatToast(toast),
          'border-l-4 border-gray-500 shadow-lg shadow-gray-100/30': isSystemToast(toast)
        }"
        class="bg-white rounded-xl shadow-2xl backdrop-blur-sm border border-gray-200 p-5 min-w-[380px] relative overflow-hidden">


        <div class="flex items-start space-x-4 relative z-10">
          <!-- Enhanced Icon with Animation -->
          <div class="flex-shrink-0 relative">
            <div
              class="w-12 h-12 rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300 hover:scale-110"
              [ngClass]="{
                'bg-gradient-to-br from-green-400 to-emerald-500 text-white': isSuccessToast(toast),
                'bg-gradient-to-br from-red-400 to-rose-500 text-white': isErrorToast(toast),
                'bg-gradient-to-br from-blue-400 to-indigo-500 text-white': isChatToast(toast),
                'bg-gradient-to-br from-gray-400 to-slate-500 text-white': isSystemToast(toast)
              }">



              <!-- Error Icon -->
              <svg *ngIf="isErrorToast(toast)" class="w-6 h-6 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>

              <!-- Chat Icon -->
              <svg *ngIf="isChatToast(toast)" class="w-6 h-6 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5"
                  d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 4v-4z" />
              </svg>

              <!-- System Icon -->
              <svg *ngIf="isSystemToast(toast)" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>

           
          </div>

          <!-- Enhanced Content -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-3">
                <p class="text-lg font-bold tracking-tight text-gray-900">
                  {{ isSuccessToast(toast) ? 'Success' : isErrorToast(toast) ? 'Error' : isChatToast(toast) ? 'Chat' : 'System' }}
                </p>
              </div>

              <!-- Close Button -->
              <button (click)="dismissToast(toast.id)"
                class="ml-3 flex-shrink-0 rounded-full p-1.5 transition-all duration-200 transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:ring-gray-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <p class="text-sm leading-relaxed font-medium mb-4 text-gray-800">
              {{ toast.messagePreview }}
            </p>

            <div class="flex items-center justify-between pt-3 border-t border-gray-200">


              <button *ngIf="isChatToast(toast)" (click)="navigateToChat(toast)"
                class="text-xs font-bold px-4 py-2 rounded-full transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2"
                [ngClass]="{
                  'bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 focus:ring-blue-300': isChatToast(toast)
                }">
                <span class="flex items-center space-x-1">
                  <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                  </svg>
                  <span>View Chat</span>
                </span>
              </button>
            </div>
          </div>
        </div>

        <!-- Progress Bar for Auto-dismiss -->
        <div class="absolute bottom-0 left-0 w-full h-1 bg-black bg-opacity-5">
          <div class="h-full animate-shrink"
            [ngClass]="{
              'bg-gradient-to-r from-green-400 to-emerald-500': isSuccessToast(toast),
              'bg-gradient-to-r from-red-400 to-rose-500': isErrorToast(toast),
              'bg-gradient-to-r from-blue-400 to-indigo-500': isChatToast(toast),
              'bg-gradient-to-r from-gray-400 to-slate-500': isSystemToast(toast)
            }"></div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .toast-container {
      pointer-events: none;
    }

    .toast-notification {
      pointer-events: auto;
      min-width: 380px;
      max-width: 420px;
    }

    .animate-slide-in {
      animation: slideIn 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
    }

    .animate-slide-out {
      animation: slideOut 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
    }

    .animate-bounce-slow {
      animation: bounce-slow 2s infinite;
    }

    .animate-shrink {
      animation: shrink 7s linear forwards;
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%) scale(0.9);
        opacity: 0;
      }
      to {
        transform: translateX(0) scale(1);
        opacity: 1;
      }
    }

    @keyframes slideOut {
      from {
        transform: translateX(0) scale(1);
        opacity: 1;
      }
      to {
        transform: translateX(100%) scale(0.9);
        opacity: 0;
      }
    }

    @keyframes bounce-slow {
      0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        transform: translate3d(0,0,0);
      }
      40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -15px, 0) scale(1.1);
      }
      70% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -7px, 0) scale(1.05);
      }
      90% {
        transform: translate3d(0,-2px,0) scale(1.02);
      }
    }

    @keyframes shrink {
      from {
        width: 100%;
      }
      to {
        width: 0%;
      }
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.7;
        transform: scale(1.05);
      }
    }

    /* Enhanced Tailwind-like utilities */
    .fixed { position: fixed; }
    .top-6 { top: 1.5rem; }
    .right-6 { right: 1.5rem; }
    .z-50 { z-index: 50; }
    .space-y-4 > * + * { margin-top: 1rem; }
    .max-w-md { max-width: 28rem; }
    .transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
    .transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
    .duration-500 { transition-duration: 500ms; }
    .duration-300 { transition-duration: 300ms; }
    .duration-200 { transition-duration: 200ms; }
    .ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }

    /* Background gradients */
    .bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
    .from-green-50 { --tw-gradient-from: #f0fdf4; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(240, 253, 244, 0)); }
    .to-emerald-50 { --tw-gradient-to: #ecfdf5; }
    .from-red-50 { --tw-gradient-from: #fef2f2; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(254, 242, 242, 0)); }
    .to-rose-50 { --tw-gradient-to: #fff1f2; }
    .from-blue-50 { --tw-gradient-from: #eff6ff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(239, 246, 255, 0)); }
    .to-indigo-50 { --tw-gradient-to: #eef2ff; }
    .from-gray-50 { --tw-gradient-from: #f9fafb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(249, 250, 251, 0)); }
    .to-slate-50 { --tw-gradient-to: #f8fafc; }

    /* Border styles */
    .border-l-4 { border-left-width: 4px; }
    .border { border-width: 1px; }
    .border-t { border-top-width: 1px; }
    .border-green-400 { border-color: #4ade80; }
    .border-red-400 { border-color: #f87171; }
    .border-blue-400 { border-color: #60a5fa; }
    .border-gray-400 { border-color: #9ca3af; }
    .border-white\/20 { border-color: rgba(255, 255, 255, 0.2); }
    .border-opacity-20 { --tw-border-opacity: 0.2; }

    /* Shadows */
    .shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
    .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
    .shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
    .shadow-green-100 { --tw-shadow-color: #dcfce7; --tw-shadow: var(--tw-shadow-colored); }
    .shadow-red-100 { --tw-shadow-color: #fee2e2; --tw-shadow: var(--tw-shadow-colored); }
    .shadow-blue-100 { --tw-shadow-color: #dbeafe; --tw-shadow: var(--tw-shadow-colored); }
    .shadow-gray-100 { --tw-shadow-color: #f3f4f6; --tw-shadow: var(--tw-shadow-colored); }

    /* Layout */
    .flex { display: flex; }
    .items-start { align-items: flex-start; }
    .items-center { align-items: center; }
    .justify-between { justify-content: space-between; }
    .space-x-4 > * + * { margin-left: 1rem; }
    .space-x-3 > * + * { margin-left: 0.75rem; }
    .space-x-1 > * + * { margin-left: 0.25rem; }
    .flex-shrink-0 { flex-shrink: 0; }
    .flex-1 { flex: 1 1 0%; }
    .min-w-0 { min-width: 0px; }
    .relative { position: relative; }
    .absolute { position: absolute; }
    .inset-0 { top: 0px; right: 0px; bottom: 0px; left: 0px; }
    .bottom-0 { bottom: 0px; }
    .left-0 { left: 0px; }
    .overflow-hidden { overflow: hidden; }

    /* Sizing */
    .w-12 { width: 3rem; }
    .h-12 { height: 3rem; }
    .w-6 { width: 1.5rem; }
    .h-6 { height: 1.5rem; }
    .w-5 { width: 1.25rem; }
    .h-5 { height: 1.25rem; }
    .w-3\.5 { width: 0.875rem; }
    .h-3\.5 { height: 0.875rem; }
    .w-full { width: 100%; }
    .h-full { height: 100%; }
    .h-1 { height: 0.25rem; }

    /* Colors and backgrounds */
    .bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
    .from-green-400 { --tw-gradient-from: #4ade80; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(74, 222, 128, 0)); }
    .to-emerald-500 { --tw-gradient-to: #10b981; }
    .from-red-400 { --tw-gradient-from: #f87171; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(248, 113, 113, 0)); }
    .to-rose-500 { --tw-gradient-to: #f43f5e; }
    .from-blue-400 { --tw-gradient-from: #60a5fa; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(96, 165, 250, 0)); }
    .to-indigo-500 { --tw-gradient-to: #6366f1; }
    .from-gray-400 { --tw-gradient-from: #9ca3af; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(156, 163, 175, 0)); }
    .to-slate-500 { --tw-gradient-to: #64748b; }
    .text-white { color: #ffffff; }
    .text-green-800 { color: #166534; }
    .text-red-800 { color: #991b1b; }
    .text-blue-800 { color: #1e40af; }
    .text-gray-800 { color: #1f2937; }
    .text-green-700 { color: #15803d; }
    .text-red-700 { color: #b91c1c; }
    .text-blue-700 { color: #1d4ed8; }
    .text-gray-700 { color: #374151; }
    .text-green-500 { color: #22c55e; }
    .text-red-500 { color: #ef4444; }
    .text-blue-500 { color: #3b82f6; }
    .text-gray-500 { color: #6b7280; }
    .text-green-400 { color: #4ade80; }
    .text-red-400 { color: #f87171; }
    .text-blue-400 { color: #60a5fa; }
    .text-gray-400 { color: #9ca3af; }
    .bg-black { background-color: #000000; }
    .bg-opacity-5 { --tw-bg-opacity: 0.05; }
    .opacity-5 { opacity: 0.05; }
    .opacity-20 { opacity: 0.2; }

    /* Typography */
    .text-base { font-size: 1rem; line-height: 1.5rem; }
    .text-sm { font-size: 0.875rem; line-height: 1.25rem; }
    .text-xs { font-size: 0.75rem; line-height: 1rem; }
    .font-bold { font-weight: 700; }
    .font-semibold { font-weight: 600; }
    .font-medium { font-weight: 500; }
    .tracking-tight { letter-spacing: -0.025em; }
    .tracking-wide { letter-spacing: 0.025em; }
    .leading-relaxed { line-height: 1.625; }

    /* Spacing */
    .p-5 { padding: 1.25rem; }
    .p-1\.5 { padding: 0.375rem; }
    .px-2\.5 { padding-left: 0.625rem; padding-right: 0.625rem; }
    .py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
    .px-4 { padding-left: 1rem; padding-right: 1rem; }
    .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
    .pt-3 { padding-top: 0.75rem; }
    .mb-2 { margin-bottom: 0.5rem; }
    .mb-4 { margin-bottom: 1rem; }
    .ml-3 { margin-left: 0.75rem; }

    /* Interactive states */
    .hover\:scale-110:hover { --tw-scale-x: 1.1; --tw-scale-y: 1.1; }
    .hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }
    .hover\:text-green-600:hover { color: #16a34a; }
    .hover\:text-red-600:hover { color: #dc2626; }
    .hover\:text-blue-600:hover { color: #2563eb; }
    .hover\:text-gray-600:hover { color: #4b5563; }
    .hover\:bg-green-100:hover { background-color: #dcfce7; }
    .hover\:bg-red-100:hover { background-color: #fee2e2; }
    .hover\:bg-blue-100:hover { background-color: #dbeafe; }
    .hover\:bg-gray-100:hover { background-color: #f3f4f6; }
    .hover\:from-blue-600:hover { --tw-gradient-from: #2563eb; }
    .hover\:to-indigo-700:hover { --tw-gradient-to: #4338ca; }
    .hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

    /* Rounded corners */
    .rounded-xl { border-radius: 0.75rem; }
    .rounded-full { border-radius: 9999px; }

    /* Focus styles */
    .focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
    .focus\:ring-2:focus { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }
    .focus\:ring-offset-2:focus { --tw-ring-offset-width: 2px; }
    .focus\:ring-green-300:focus { --tw-ring-color: #86efac; }
    .focus\:ring-red-300:focus { --tw-ring-color: #fca5a5; }
    .focus\:ring-blue-300:focus { --tw-ring-color: #93c5fd; }
    .focus\:ring-gray-300:focus { --tw-ring-color: #d1d5db; }

    /* Background colors */
    .bg-green-100 { background-color: #dcfce7; }
    .bg-red-100 { background-color: #fee2e2; }
    .bg-blue-100 { background-color: #dbeafe; }
    .bg-gray-100 { background-color: #f3f4f6; }
    .border-green-200 { border-color: #bbf7d0; }
    .border-red-200 { border-color: #fecaca; }
    .border-blue-200 { border-color: #bfdbfe; }
    .border-gray-200 { border-color: #e5e7eb; }

    /* Z-index */
    .z-10 { z-index: 10; }

    /* Backdrop */
    .backdrop-blur-sm { backdrop-filter: blur(4px); }

    /* Animation utilities */
    .animate-ping { animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite; }
    .animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

    @keyframes ping {
      75%, 100% {
        transform: scale(2);
        opacity: 0;
      }
    }
  `]
})
export class StyledToastComponent implements OnInit, OnDestroy {
  toastNotifications: ToastNotification[] = [];
  private destroy$ = new Subject<void>();

  constructor(
    private globalMessageService: GlobalMessageNotificationService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    // Subscribe to toast notifications from global message service
    this.globalMessageService.toastNotification$.pipe(takeUntil(this.destroy$)).subscribe(toastData => {
      console.log('🍞 WebApp StyledToast: Showing styled toast notification:', toastData);
      this.showToast(toastData);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private showToast(toastData: ToastNotificationData): void {
    const toast: ToastNotification = {
      ...toastData,
      id: this.generateToastId(),
      isVisible: true
    };

    this.toastNotifications.push(toast);
    this.cdr.detectChanges();

    // Auto-dismiss after 7 seconds
    setTimeout(() => {
      this.dismissToast(toast.id);
    }, 7000);
  }

  dismissToast(toastId: string): void {
    const toastIndex = this.toastNotifications.findIndex(t => t.id === toastId);
    if (toastIndex >= 0) {
      // Start slide-out animation
      this.toastNotifications[toastIndex].isVisible = false;
      this.cdr.detectChanges();

      // Remove from array after animation
      setTimeout(() => {
        this.toastNotifications = this.toastNotifications.filter(t => t.id !== toastId);
        this.cdr.detectChanges();
      }, 300);
    }
  }

  navigateToChat(toast: ToastNotification): void {
    this.router.navigate(['/chat', toast.doctorId, toast.patientId]);
    this.dismissToast(toast.id);
  }

  trackByToastId(_index: number, toast: ToastNotification): string {
    return toast.id;
  }



  private generateToastId(): string {
    return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Helper methods for toast type checking
  isSuccessToast(toast: ToastNotification): boolean {
    return toast.senderName === 'Success' || toast.threadId.includes('success');
  }

  isErrorToast(toast: ToastNotification): boolean {
    return toast.senderName === 'System Error' || toast.threadId.includes('error');
  }

  isChatToast(toast: ToastNotification): boolean {
    return !toast.threadId.startsWith('system-') && toast.senderName !== 'Success' && toast.senderName !== 'System Error';
  }

  isSystemToast(toast: ToastNotification): boolean {
    return toast.senderName === 'System' || (toast.threadId.includes('system') && !this.isSuccessToast(toast) && !this.isErrorToast(toast));
  }

  getToastTypeLabel(toast: ToastNotification): string {
    if (this.isSuccessToast(toast)) return 'Success';
    if (this.isErrorToast(toast)) return 'Error';
    if (this.isChatToast(toast)) return 'New Message';
    return 'Notification';
  }
}
