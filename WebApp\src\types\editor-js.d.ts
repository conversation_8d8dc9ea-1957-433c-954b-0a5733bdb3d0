// Type definitions for Editor.js
declare module '@editorjs/editorjs' {
  export interface OutputData {
    time: number;
    blocks: OutputBlockData[];
    version: string;
  }

  export interface OutputBlockData {
    id?: string;
    type: string;
    data: any;
    tunes?: any;
  }

  export interface EditorConfig {
    holder?: string | HTMLElement;
    tools?: any;
    autofocus?: boolean;
    placeholder?: string;
    inlineToolbar?: boolean | string[];
    data?: OutputData;
    readOnly?: boolean;
    onReady?: () => void;
    onChange?: (api: API, event: CustomEvent) => void;
  }

  export interface API {
    blocks: {
      render(blocks: OutputData): Promise<void>;
    }
    saver: {
      save(): Promise<OutputData>;
    }
  }

  export default class EditorJS {
    constructor(config?: EditorConfig);
    isReady: Promise<boolean>;
    save(): Promise<OutputData>;
    render(data: OutputData): Promise<void>;
    destroy(): void;
  }
}

declare module '@editorjs/header' {
  import { BlockTool, BlockToolConstructable } from '@editorjs/editorjs';
  export default class Header implements BlockTool {
    static get toolbox(): any;
  }
}

declare module '@editorjs/list' {
  import { BlockTool, BlockToolConstructable } from '@editorjs/editorjs';
  export default class List implements BlockTool {
    static get toolbox(): any;
  }
}

declare module '@editorjs/paragraph' {
  import { BlockTool, BlockToolConstructable } from '@editorjs/editorjs';
  export default class Paragraph implements BlockTool {
    static get toolbox(): any;
  }
}

declare module '@editorjs/image' {
  import { BlockTool, BlockToolConstructable } from '@editorjs/editorjs';
  export default class ImageTool implements BlockTool {
    static get toolbox(): any;
  }
}

declare module '@editorjs/embed' {
  import { BlockTool, BlockToolConstructable } from '@editorjs/editorjs';
  export default class Embed implements BlockTool {
    static get toolbox(): any;
  }
}
