<div class="p-6">
  <!-- Page Header -->
  <div
    class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-5 transition-colors duration-200">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">Patient Requests</h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Manage and track patient treatment requests and statuses</p>
      </div>
      <!-- Test Toaster Button (for debugging) -->

    </div>
  </div>

  <!-- Stats Cards Section -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Patients Card -->
    <div
      class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200">
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center w-14 h-14">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">Total Patients</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-blue-800 dark:text-blue-300">{{stats.total}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">patients</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="px-4 pb-3 pt-2">
        <div class="flex justify-between items-center mb-1">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Patients Count</span>
          <span class="text-xs font-semibold text-gray-700 dark:text-gray-300">{{stats.total}}/{{stats.total}}</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <div class="bg-blue-500 dark:bg-blue-400 h-2 rounded-full" [style.width.%]="100"></div>
        </div>
      </div>
    </div>

    <!-- Pending Patients Card -->
    <div
      class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200">
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-amber-100 dark:bg-amber-900/50 flex items-center justify-center w-14 h-14">
            <svg class="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">Pending</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-amber-600 dark:text-amber-400">{{stats.pending}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">patients</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="px-4 pb-3 pt-2">
        <div class="flex justify-between items-center mb-1">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Pending Rate</span>
          <span class="text-xs font-semibold text-gray-700 dark:text-gray-300">{{stats.pending}}/{{stats.total}}</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <div class="bg-amber-500 dark:bg-amber-400 h-2 rounded-full"
            [style.width.%]="stats.total > 0 ? (stats.pending/stats.total) * 100 : 0"></div>
        </div>
      </div>
    </div>

    <!-- Recovered Patients Card -->
    <div
      class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200">
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center w-14 h-14">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">Recovered</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{stats.recovered}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">patients</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="px-4 pb-3 pt-2">
        <div class="flex justify-between items-center mb-1">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Recovery Rate</span>
          <span
            class="text-xs font-semibold text-gray-700 dark:text-gray-300">{{stats.recovered}}/{{stats.total}}</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <div class="bg-green-500 dark:bg-green-400 h-2 rounded-full"
            [style.width.%]="stats.total > 0 ? (stats.recovered/stats.total) * 100 : 0"></div>
        </div>
      </div>
    </div>

    <!-- Progress Count Card -->
    <div
      class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200">
      <div class="p-4">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center w-14 h-14">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z">
              </path>
            </svg>
          </div>
          <div class="ml-4">
            <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">Progress</div>
            <div class="flex items-baseline">
              <span class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{stats.inTreatment}}</span>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">patients</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="px-4 pb-3 pt-2">
        <div class="flex justify-between items-center mb-1">
          <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Progress Rate</span>
          <span class="text-xs font-semibold text-gray-700 dark:text-gray-300">{{stats.inTreatment}}/{{stats.total}}</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <div class="bg-purple-500 dark:bg-purple-400 h-2 rounded-full"
            [style.width.%]="stats.total > 0 ? (stats.inTreatment/stats.total) * 100 : 0">
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Search and Filter Section -->
  <div
    class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-4 transition-colors duration-200">
    <div class="flex flex-col sm:flex-row items-center justify-between gap-3">
      <!-- Search Box -->
      <div class="w-full sm:w-64">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
            <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input type="text" [(ngModel)]="searchTerm" (input)="applyFilters()"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-shadow"
            placeholder="Search patients...">
        </div>
      </div>

      <!-- Filter and View Toggle Buttons -->
      <div class="flex items-center flex-wrap space-y-4 md:space-y-0 justify-center space-x-4">
        <!-- Filter Buttons -->
        <div class="flex space-x-2">
          <button (click)="setFilter('all')" class="px-3 py-1.5 text-sm rounded-md border transition-all"
            [ngClass]="activeFilter === 'all' ?
              'bg-indigo-600 dark:bg-indigo-500 text-white border-indigo-600 dark:border-indigo-500 hover:bg-indigo-700 dark:hover:bg-indigo-600' :
              'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'">
            All Patients
          </button>
          <button (click)="setFilter('pending')" class="px-3 py-1.5 text-sm rounded-md border transition-all"
            [ngClass]="activeFilter === 'pending' ?
              'bg-amber-600 dark:bg-amber-500 text-white border-amber-600 dark:border-amber-500 hover:bg-amber-700 dark:hover:bg-amber-600' :
              'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'">
            Pending
          </button>
          <button (click)="setFilter('recovered')" class="px-3 py-1.5 text-sm rounded-md border transition-all"
            [ngClass]="activeFilter === 'recovered' ?
              'bg-green-600 dark:bg-green-500 text-white border-green-600 dark:border-green-500 hover:bg-green-700 dark:hover:bg-green-600' :
              'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'">
            Recovered
          </button>
        </div>

        <!-- View Toggle Buttons -->
        <div class="flex border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
          <button (click)="toggleView('cards')" class="px-3 py-1.5 text-sm transition-all flex items-center space-x-1"
            [ngClass]="viewType === 'cards' ? 'bg-blue-600 dark:bg-blue-500 text-white' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600'">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
            <span>Cards</span>
          </button>
          <button (click)="toggleView('table')" class="px-3 py-1.5 text-sm transition-all flex items-center space-x-1"
            [ngClass]="viewType === 'table' ? 'bg-blue-600 dark:bg-blue-500 text-white' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600'">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h12a1 1 0 011 1v16a1 1 0 01-1 1H4a1 1 0 01-1-1z" />
            </svg>
            <span>Table</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Projects Table/Cards Container -->
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden mb-8 transition-colors duration-200">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="p-8 flex justify-center items-center">
      <div class="flex flex-col items-center">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 dark:border-indigo-400">
        </div>
        <p class="mt-3 text-sm text-gray-600 dark:text-gray-400">Loading patients data...</p>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoading && filteredRequests.length === 0" class="p-12 text-center">
      <div class="flex flex-col items-center">
        <svg class="w-16 h-16 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 22">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
            d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h3 class="mt-3 text-lg font-medium text-gray-900 dark:text-gray-100">No patients found</h3>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400 max-w-md">
          We couldn't find any patient records matching your search criteria. Try adjusting your filters or search
          terms.
        </p>
        <button (click)="setFilter('all'); searchTerm = ''; applyFilters();"
          class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 dark:bg-indigo-500 hover:bg-indigo-700 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-indigo-400">
          Reset Filters
        </button>
      </div>
    </div>

    <!-- Table View -->
    <div *ngIf="!isLoading && filteredRequests.length > 0 && viewType === 'table'" class="overflow-x-auto">
      <!-- Data Table -->
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Subject
            </th>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              User Email
            </th>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Assigned Email
            </th>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Status
            </th>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Priority
            </th>
            <th scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Problems
            </th>
            <th scope="col"
              class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
          <tr *ngFor="let request of displayedRequests" class="hover:bg-gray-50 dark:hover:bg-gray-700">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">

                <div class="">
                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{request.subject.slice(0,18)}}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900 dark:text-gray-100">{{request.patientEmail}}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900 dark:text-gray-100">{{request.assignedEmail || 'Not Assigned'}}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex flex-col space-y-2">
                <!-- Status Update Dropdown (for Admin/Doctor) -->
                <select *ngIf="isAdmin || isDoctor" [value]="request.status"
                  (change)="updateRequestStatus(request, $event)"
                  class="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                  <option value="Pending">Pending</option>
                  <option value="Recovered">Recovered</option>
                  <option value="Progress">Progress</option>
                </select>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                [class]="getPriorityClass(request.priority) + ' px-2 inline-flex text-xs leading-5 font-semibold rounded-full'">
                {{request.priority || 'Normal'}}
              </span>
            </td>
            <td class="px-6 py-4">
              <div class="text-sm text-gray-900 dark:text-gray-100 line-clamp-2 max-w-xs">{{request.problems}}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end space-x-2">
                <button (click)="viewRequest(request.id)"
                  class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 rounded-full p-1"
                  title="View Details">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                    </path>
                  </svg>
                </button>
                <button (click)="deleteRequest(request.id)"
                  class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-red-400 rounded-full p-1"
                  title="Delete Request">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                    </path>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>


    </div>

    <!-- Cards View -->
    <div *ngIf="!isLoading && filteredRequests.length > 0 && viewType === 'cards'"
      class="p-6 bg-white dark:bg-gray-800 transition-colors duration-200">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div *ngFor="let request of displayedRequests"
          class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm hover:shadow-md dark:hover:shadow-lg transition-all duration-200 overflow-hidden group">
          <!-- Card Header with Image -->
          <div
            class="relative h-48 bg-gradient-to-br from-blue-50 dark:from-blue-900/50 to-indigo-100 dark:to-indigo-900/50 overflow-hidden">
            <!-- Request Image Preview -->
            <div class="absolute inset-0 flex items-center justify-center">
              <!-- Show actual image if available -->
              <div
                *ngIf="getFirstImageUrl(request) !== '/assets/images/medical-placeholder.jpg'; else placeholderTemplate"
                class="w-full h-full">
                <img [src]="getFirstImageUrl(request)" [alt]="request.patientName + ' medical image'"
                  class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  (error)="onImageError($event)">
              </div>

              <!-- Placeholder template -->
              <ng-template #placeholderTemplate>
                <div
                  class="w-full h-full bg-gradient-to-br from-blue-100 dark:from-blue-800/50 to-purple-100 dark:to-purple-800/50 flex items-center justify-center">
                  <!-- Medical Icon -->
                  <div class="text-center">
                    <svg class="w-16 h-16 text-blue-400 dark:text-blue-300 mx-auto mb-2" fill="none"
                      stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p class="text-xs text-blue-600 dark:text-blue-300 font-medium">
                      <span *ngIf="getImageCount(request) > 0; else fileCountTemplate">
                        {{ getImageCount(request) }} {{ getImageCount(request) === 1 ? 'image' : 'images' }}
                      </span>
                      <ng-template #fileCountTemplate>
                        {{ getFileCount(request) }} {{ getFileCount(request) === 1 ? 'file' : 'files' }}
                      </ng-template>
                    </p>
                  </div>
                </div>
              </ng-template>
            </div>

            <!-- Status Badge -->
            <div class="absolute top-3 right-3">
              <span
                [class]="getStatusClass(request.status) + ' px-2.5 py-1 rounded-full text-xs font-medium shadow-sm'">
                {{ request.status }}
              </span>
            </div>

            <!-- Priority Badge -->
            <div class="absolute top-3 left-3" *ngIf="request.priority">
              <span
                [class]="getStatusClass(request.priority) + ' px-2.5 py-1 rounded-full text-xs font-medium shadow-sm'">
                {{ request.priority }}
              </span>
            </div>
          </div>

          <!-- Card Content -->
          <div class="p-5">
            <!-- Patient Info -->
            <div class="flex items-center mb-3">
              <div class="flex-shrink-0 h-10 w-10">
                <div
                  class="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 dark:from-blue-600 to-purple-600 dark:to-purple-700 flex items-center justify-center text-white font-medium text-sm">
                  {{ request.patientName?.charAt(0) || '#' }}{{ request.patientName?.split(' ')[1] ?
                  request.patientName?.split(' ')[1].charAt(0) : '' }}
                </div>
              </div>
              <div class="ml-3 flex-1 min-w-0">
                <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">{{ request.patientName }}
                </h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ request.patientEmail }}</p>
              </div>
            </div>

            <!-- Request Details -->
            <div class="space-y-2 mb-4">
              <div class="text-sm text-gray-600 dark:text-gray-400">
                <span class="font-medium">Problems:</span>
                <p class="text-gray-800 dark:text-gray-200 line-clamp-2 mt-1">{{ request.problems }}</p>
              </div>

              <div class="text-xs text-gray-500 dark:text-gray-400" *ngIf="request.assignedEmail">
                <span class="font-medium">Assigned to:</span> {{ request.assignedEmail }}
              </div>

              <div class="text-xs text-gray-500 dark:text-gray-400">
                <span class="font-medium">Created:</span> {{ formatDateForDisplay(request.requestDate) }}
              </div>
            </div>

            <!-- Status Update (for Admin/Doctor) -->
            <div class="mb-4" *ngIf="isAdmin || isDoctor">
              <label class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Update Status</label>
              <select [value]="request.status"
                (change)="updateRequestStatus(request, $event)"
                class="w-full text-xs border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                <option value="Pending">Pending</option>
                <option value="Recovered">Recovered</option>
                <option value="Progress">Progress</option>
              </select>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center pt-3 border-t border-gray-100 dark:border-gray-600">
              <button (click)="viewRequest(request.id)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/50 hover:bg-blue-100 dark:hover:bg-blue-800/50 transition-colors">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                  </path>
                </svg>
                View Details
              </button>

              <button (click)="deleteRequest(request.id)"
                class="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/50 hover:bg-red-100 dark:hover:bg-red-800/50 transition-colors">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                  </path>
                </svg>
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>



    </div>
  </div>
  <!-- Pagination Controls -->
  <div class="bg-white dark:bg-gray-900 px-6 py-4 border-t border-gray-200 dark:border-gray-700 rounded-b-lg shadow">
    <div class="flex flex-col sm:flex-row items-center justify-between">
      <!-- Results Info -->
      <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
        Showing <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> to
        <span class="font-medium">{{ Math.min(currentPage * pageSize, totalRequests) }}</span> of
        <span class="font-medium">{{ totalRequests }}</span> patients
      </div>

      <!-- Pagination Navigation -->
      <div class="flex items-center space-x-2">
        <!-- Page Size Selector -->
        <div class="flex items-center mr-3">
          <label for="page-size" class="text-sm text-gray-600 dark:text-gray-400 mr-2">Show:</label>
          <select id="page-size" [(ngModel)]="pageSize" (change)="onPageSizeChange($event)"
            class="min-w-[56px] py-1 px-2 rounded-md border border-gray-300 dark:border-gray-600 text-sm bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition">
            <option *ngFor="let size of getPageSizeOptions()" [value]="size">{{ size }}</option>
          </select>
        </div>
        <!-- Previous Button -->
        <button (click)="previousPage()" [disabled]="currentPage === 1"
          class="w-8 h-8 flex items-center justify-center rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <!-- First Page Button (if not in view) -->
        <button *ngIf="paginationRange[0] > 1" (click)="goToPage(1)"
          class="w-8 h-8 flex items-center justify-center rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 transition-colors">
          1
        </button>
        <!-- Ellipsis (if needed) -->
        <span *ngIf="paginationRange[0] > 2" class="px-1 text-gray-400 dark:text-gray-500">…</span>
        <!-- Page Numbers -->
        <button *ngFor="let page of paginationRange" (click)="goToPage(page)"
          class="w-8 h-8 flex items-center justify-center rounded-md mx-0.5 border transition-colors"
          [ngClass]="page === currentPage
                ? 'bg-blue-600 text-white border-blue-600 font-medium'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border-gray-300 dark:border-gray-600'">
          {{ page }}
        </button>
        <!-- Ellipsis (if needed) -->
        <span *ngIf="paginationRange[paginationRange.length - 1] < totalPages - 1"
          class="px-1 text-gray-400 dark:text-gray-500">…</span>
        <!-- Last Page Button (if not in view) -->
        <button *ngIf="paginationRange[paginationRange.length - 1] < totalPages" (click)="goToPage(totalPages)"
          class="w-8 h-8 flex items-center justify-center rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 transition-colors">
          {{ totalPages }}
        </button>
        <!-- Next Button -->
        <button (click)="nextPage()" [disabled]="currentPage === totalPages"
          class="w-8 h-8 flex items-center justify-center rounded-md bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- MatSnackBar toaster notifications are handled automatically by Angular Material -->
