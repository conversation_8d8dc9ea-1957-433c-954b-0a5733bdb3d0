/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  height: 100%;
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  margin: 0;
  font-family: <PERSON>o, "Helvetica Neue", sans-serif;
}

/* Dark mode root styles */
html.dark {
  color-scheme: dark;
}

/* ========================================
   ENHANCED TOASTER/SNACKBAR STYLES
   Modern design with glassmorphism effect
======================================== */

/* MatSnackBar Container Positioning */
.mat-snack-bar-container {
  position: fixed !important;
  z-index: 10000 !important;
  top: 24px !important;
  right: 24px !important;
  max-width: 420px !important;
  min-width: 320px !important;
}

.mdc-snackbar {
  position: fixed !important;
  z-index: 10000 !important;
  top: 24px !important;
  right: 24px !important;
}

/* Base Snackbar Surface with Glassmorphism */
.mdc-snackbar__surface {
  border-radius: 12px !important;
  backdrop-filter: blur(12px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-family: 'Roboto', 'Helvetica Neue', sans-serif !important;
}

/* Slide-in Animation */
@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Success Snackbar - Green Theme */
/* .success-snackbar .mdc-snackbar__surface {
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.95) 0%,
    rgba(22, 163, 74, 0.95) 100%) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
  color: white !important;
} */
/* ========================================
   PRIMENG TOAST STYLES
   Matching the project theme
======================================== */

/* Toast Container */
.p-toast {
  position: fixed !important;
  top: 24px !important;
  right: 24px !important;
  z-index: 10000 !important;
  width: 420px !important;
}

/* Base Toast Item */
.p-toast .p-toast-message {
  margin: 0 0 12px 0 !important;
  border-radius: 12px !important;
  backdrop-filter: blur(12px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-family: 'Roboto', 'Helvetica Neue', sans-serif !important;
  padding: 16px 20px !important;
}

/* Toast Content */
.p-toast .p-toast-message-content {
  display: flex !important;
  align-items: flex-start !important;
  padding: 0 !important;
}

.p-toast .p-toast-message-icon {
  width: 24px !important;
  height: 24px !important;
  margin-right: 12px !important;
  margin-top: 2px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  flex-shrink: 0 !important;
}

.p-toast .p-toast-message-text {
  flex: 1 !important;
}

.p-toast .p-toast-summary {
  font-weight: 600 !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  margin: 0 0 4px 0 !important;
  color: white !important;
}

.p-toast .p-toast-detail {
  font-weight: 400 !important;
  font-size: 13px !important;
  line-height: 1.4 !important;
  margin: 0 !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Close Button */
.p-toast .p-toast-icon-close {
  width: 20px !important;
  height: 20px !important;
  color: rgba(255, 255, 255, 0.8) !important;
  margin-left: 12px !important;
  margin-top: 2px !important;
  cursor: pointer !important;
  transition: color 0.2s ease !important;
  background: none !important;
  border: none !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
}

.p-toast .p-toast-icon-close:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Success Toast - Green */

.p-toast .p-toast-message-success .p-toast-message-icon {
  color: white !important;
}

.p-toast .p-toast-message-success .p-toast-message-icon::before {
  content: "✓" !important;
  font-weight: bold !important;
  font-size: 14px !important;
}

/* Info Toast - Blue */
.p-toast .p-toast-message-info {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.95) 0%,
    rgba(37, 99, 235, 0.95) 100%) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
}

.p-toast .p-toast-message-info .p-toast-message-icon {
  color: white !important;
}

.p-toast .p-toast-message-info .p-toast-message-icon::before {
  content: "ⓘ" !important;
  font-weight: bold !important;
  font-size: 14px !important;
}

/* Warning Toast - Amber */
.p-toast .p-toast-message-warn {
  background: linear-gradient(135deg,
    rgba(245, 158, 11, 0.95) 0%,
    rgba(217, 119, 6, 0.95) 100%) !important;
  border-color: rgba(245, 158, 11, 0.3) !important;
}

.p-toast .p-toast-message-warn .p-toast-message-icon {
  color: white !important;
}

.p-toast .p-toast-message-warn .p-toast-message-icon::before {
  content: "⚠" !important;
  font-weight: bold !important;
  font-size: 14px !important;
}

/* Error Toast - Red */
.p-toast .p-toast-message-error {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.95) 0%,
    rgba(220, 38, 38, 0.95) 100%) !important;
  border-color: rgba(239, 68, 68, 0.3) !important;
}

.p-toast .p-toast-message-error .p-toast-message-icon {
  color: white !important;
}

.p-toast .p-toast-message-error .p-toast-message-icon::before {
  content: "✕" !important;
  font-weight: bold !important;
  font-size: 14px !important;
}

/* Dark Mode Adjustments for PrimeNG Toast */
html.dark .p-toast .p-toast-message {
  border-color: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(16px) !important;
}



html.dark .p-toast .p-toast-message-info {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.9) 0%,
    rgba(37, 99, 235, 0.9) 100%) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
}

html.dark .p-toast .p-toast-message-warn {
  background: linear-gradient(135deg,
    rgba(245, 158, 11, 0.9) 0%,
    rgba(217, 119, 6, 0.9) 100%) !important;
  border-color: rgba(245, 158, 11, 0.4) !important;
}

html.dark .p-toast .p-toast-message-error {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.9) 0%,
    rgba(220, 38, 38, 0.9) 100%) !important;
  border-color: rgba(239, 68, 68, 0.4) !important;
}

/* Custom Message Type - Primary */
.p-toast .p-toast-message-primary {
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.95) 0%,
    rgba(79, 70, 229, 0.95) 100%) !important;
  border-color: rgba(99, 102, 241, 0.3) !important;
}

.p-toast .p-toast-message-primary .p-toast-message-icon {
  color: white !important;
}

.p-toast .p-toast-message-primary .p-toast-message-icon::before {
  content: "📋" !important;
  font-size: 14px !important;
}

html.dark .p-toast .p-toast-message-primary {
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.9) 0%,
    rgba(79, 70, 229, 0.9) 100%) !important;
  border-color: rgba(99, 102, 241, 0.4) !important;
}.warning-snackbar {
  background-color: rgb(245, 158, 11) !important;
  /* Amber-500 */
  color: white !important;
}

.info-snackbar {
  background-color: rgb(59, 130, 246) !important;
  /* Blue-500 */
  color: white !important;
}

.message-notification-snackbar {
  background-color: rgb(99, 102, 241) !important;
  /* Indigo-500 */
  color: white !important;
  font-weight: 500 !important;
}

.warning-snackbar {
  background-color: rgb(234, 88, 12) !important;
  /* Orange-600 */
  color: white !important;
}

/* Dark mode SnackBar styles */
html.dark .error-snackbar {
  background-color: rgb(239, 68, 68) !important;
  /* Red-500 */
}

html.dark .success-snackbar {
  /* background-color: rgb(34, 197, 94) !important; */
  /* Green-500 */
}

html.dark .warning-snackbar {
  background-color: rgb(249, 115, 22) !important;
  /* Orange-500 */
}

/* Smooth transitions for theme switching */
* {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar for dark mode */
html.dark ::-webkit-scrollbar {
  width: 8px;
}

html.dark ::-webkit-scrollbar-track {
  background: rgb(31, 41, 55);
  /* gray-800 */
}

html.dark ::-webkit-scrollbar-thumb {
  background: rgb(75, 85, 99);
  /* gray-600 */
  border-radius: 4px;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(107, 114, 128);
  /* gray-500 */
}

/* Light mode scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(243, 244, 246);
  /* gray-100 */
}

::-webkit-scrollbar-thumb {
  background: rgb(209, 213, 219);
  /* gray-300 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(156, 163, 175);
  /* gray-400 */
}

/* Custom scrollbar for role popover */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgb(249, 250, 251);
  /* gray-50 */
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(209, 213, 219);
  /* gray-300 */
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(156, 163, 175);
  /* gray-400 */
}

/* Dark mode custom scrollbar for role popover */
html.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: rgb(31, 41, 55);
  /* gray-800 */
}

html.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(75, 85, 99);
  /* gray-600 */
}

html.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(107, 114, 128);
  /* gray-500 */
}

/* Role item styles for smooth interactions */
.role-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.role-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

html.dark .role-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgb(209, 213, 219);
  /* gray-300 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(156, 163, 175);
  /* gray-400 */
}

/* Form elements dark mode support */
html.dark input,
html.dark textarea,
html.dark select {
  background-color: rgb(31, 41, 55);
  /* gray-800 */
  border-color: rgb(75, 85, 99);
  /* gray-600 */
  color: rgb(243, 244, 246);
  /* gray-100 */
}

html.dark input:focus,
html.dark textarea:focus,
html.dark select:focus {
  border-color: rgb(59, 130, 246);
  /* blue-500 */
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Material Design components dark mode support */
html.dark .mat-mdc-form-field {
  --mdc-filled-text-field-container-color: rgb(31, 41, 55);
  --mdc-filled-text-field-label-text-color: rgb(156, 163, 175);
  --mdc-filled-text-field-input-text-color: rgb(243, 244, 246);
}

html.dark .mat-mdc-button {
  --mdc-text-button-label-text-color: rgb(243, 244, 246);
}

html.dark .mat-mdc-raised-button {
  --mdc-protected-button-container-color: rgb(31, 41, 55);
  --mdc-protected-button-label-text-color: rgb(243, 244, 246);
}

/* Global dark mode fix for innerHTML content */
html.dark .article-content * {
  color: #e2e8f0 !important;
}

html.dark .article-content h1,
html.dark .article-content h2,
html.dark .article-content h3,
html.dark .article-content h4,
html.dark .article-content h5,
html.dark .article-content h6 {
  color: #ffffff !important;
}

html.dark .article-content strong,
html.dark .article-content b {
  color: #ffffff !important;
}

html.dark .article-content a {
  color: #60a5fa !important;
}

html.dark .article-content code {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

html.dark .article-content pre {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
}

@layer component {
  .add-btn {
    @apply w-full text-left !px-4 !py-2 flex rounded-md !border-gray-400 shadow-md items-center gap-2 text-sm !bg-white dark:!bg-blue-600 hover:!bg-gray-50 dark:hover:!bg-gray-700 transition-colors duration-200
  }

  .back-btn {
    @apply inline-flex items-center !px-3 !py-2 !border !border-gray-300 dark:!border-gray-600 rounded-md shadow-sm text-sm font-medium !text-gray-700 dark:!text-gray-200 !bg-white dark:!bg-gray-800 hover:!bg-gray-50 dark:hover:!bg-gray-700 focus:!outline-none focus:ring-2 focus:!ring-blue-500 dark:focus:!ring-blue-400
  }
}
