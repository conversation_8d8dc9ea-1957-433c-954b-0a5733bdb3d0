<!-- Article Container -->
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex justify-center items-center p-12">
    <div class="flex flex-col items-center">
      <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
      <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">Loading educational content...</p>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && loadError" class="p-12">
    <div class="text-center p-6 bg-red-50 dark:bg-red-900/30 rounded-lg max-w-lg mx-auto">
      <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-red-400 dark:text-red-300" fill="none"
        viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-red-800 dark:text-red-200">Error Loading Content</h3>
      <p class="mt-1 text-sm text-red-600 dark:text-red-300">{{ loadError }}</p>
      <div class="mt-4">
        <button (click)="goToList()"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 dark:text-red-200 bg-red-100 dark:bg-red-800 hover:bg-red-200 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-red-400">
          Back to List
        </button>
      </div>
    </div>
  </div>

  <!-- Article Content -->
  <div *ngIf="!isLoading && !loadError && educational" class="p-5">

    <!-- Floating Actions (Admin Only) -->
    <div *ngIf="isAdmin" class="absolute top-4 right-4 z-10 flex space-x-2 space-y-2">
      <button (click)="editEducational()"
        class="inline-flex items-center px-3 py-2 border border-transparent shadow-lg text-sm font-medium rounded-full text-white bg-blue-600 dark:bg-blue-900 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-200">
        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path
            d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
        </svg>
        <span class="ml-1">Edit</span>
      </button>
      <button (click)="deleteEducational()"
        class="inline-flex items-center px-3 py-2 border border-transparent shadow-lg text-sm font-medium rounded-full text-white bg-red-600 dark:bg-red-900 hover:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-red-400 transition-all duration-200">
        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
            clip-rule="evenodd" />
        </svg>
        <span class="ml-1">Delete</span>
      </button>
    </div>

    <!-- Back Navigation -->
    <div class="p-6 pb-0">
      <button (click)="goToList()"
        class="inline-flex items-center text-sm text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white font-medium group transition-colors duration-200">
        <svg class="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200" fill="none"
          stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Articles
      </button>
    </div>

    <!-- Article Header -->
    <header class="px-6 py-8">
      <!-- Category and Reading Time -->
      <div class="flex items-center space-x-4 mb-6">
        <span
          class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-blue-500 dark:bg-blue-950 dark:border-blue-800 dark:border text-white shadow-lg">
          {{ educational.category }}
        </span>
        <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
          <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          {{ educational.readingTime || '5 min read' }}
        </div>
      </div>

      <!-- Featured Image (Hero) -->
      <div *ngIf="educational.featuredImg" class="mb-8 -mx-6">
        <div class="relative overflow-hidden rounded-2xl shadow-2xl">
          <img [src]="educational.featuredImg" alt="Featured image"
            class="w-full h-64 md:h-80 lg:h-96 object-cover bg-gray-100 dark:bg-gray-800"
            (error)="onImageError($event)">
          <div
            class="absolute inset-0 bg-gradient-to-t from-black/20  to-transparent dark:bg-gradient-to-t dark:from-gray-600 dark:to-transparent">
          </div>
        </div>
      </div>
    </header>

    <!-- Article Body -->
    <article
      class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">

      <!-- Article Content -->
      <div class="p-4 sm:p-6 lg:p-8">
        <div
          class="article-content dark:!text-white max-w-none prose prose-lg dark:prose-invert prose-blue dark:prose-headings:!text-white dark:prose-p:!text-gray-100 dark:prose-strong:!text-white dark:prose-a:!text-blue-400 dark:prose-li:!text-gray-100">
          <div [innerHTML]="getHTMLContent()"></div>
        </div>
      </div>

      <!-- Article Footer -->
      <footer class="mt-16 pt-8 border-t border-gray-200 dark:border-gray-700">
        <!-- Author and Date Info -->
        <div class="flex flex-col px-10 md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div
                class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <span class="text-white font-semibold text-sm">
                  {{ (educational.createdBy || 'Unknown')[0].toUpperCase() }}
                </span>
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ educational.createdBy || 'Unknown Author' }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Content Creator</p>
            </div>
          </div>

          <div class="flex flex-col md:items-end text-sm text-gray-500 dark:text-gray-400">
            <p>Published: {{ formatDate(educational.createDate) }}</p>
            <p *ngIf="educational.updateDate" class="text-xs">
              Last updated: {{ formatDate(educational.updateDate) }}
            </p>
          </div>
        </div>

        <!-- Share Actions (Optional) -->
        <div class="mt-8 pt-6 pb-10 border-t border-gray-100 dark:border-gray-700">
          <div class="flex items-center justify-center">
            <p class="text-sm text-gray-500 dark:text-gray-400">Was this article helpful?</p>
          </div>
        </div>
      </footer>
    </article>

    <!-- Spacer for bottom -->
    <div class="h-12"></div>
  </div>
</div>
