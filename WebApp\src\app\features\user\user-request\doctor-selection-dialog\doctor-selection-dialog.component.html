<div class="doctor-selection-dialog bg-white dark:bg-gray-900 rounded-lg shadow-lg transition-colors duration-200 max-h-[90vh] overflow-hidden flex flex-col">
  <!-- Dialog Header -->
  <div class="dialog-header p-6 pb-4 flex-shrink-0">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100">Select Doctor</h2>
    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Assign a doctor to this patient request</p>
  </div>

  <!-- Search Bar -->
  <div class="search-container px-6 pb-4 flex-shrink-0">
    <div class="relative">
      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <svg class="w-5 h-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>
      <input
        type="text"
        [(ngModel)]="searchQuery"
        placeholder="Search doctor by name or email"
        class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-colors duration-200"
      />
    </div>
  </div>

  <!-- Doctors List -->
  <div class="doctors-list flex-1 overflow-y-auto px-6 pb-4 min-h-0 max-h-80 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800">
    <div *ngIf="loading" class="flex justify-center p-4">
      <div class="animate-spin rounded-full h-8 w-8 border-4 border-blue-500 dark:border-blue-400 border-t-transparent"></div>
    </div>

    <div *ngIf="!loading && filteredDoctors.length === 0" class="text-center py-6 text-gray-500 dark:text-gray-400">
      <svg class="w-12 h-12 mx-auto text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <p class="mt-2">No doctors found</p>
    </div>

    <div *ngFor="let doctor of filteredDoctors"
         class="doctor-item p-3 border-b border-gray-100 dark:border-gray-700 cursor-pointer transition-colors hover:bg-blue-50 dark:hover:bg-blue-900/50"
         [class.selected]="selectedDoctor && selectedDoctor.email === doctor.email"
         (click)="selectDoctor(doctor)">
      <div class="flex items-center">
        <!-- Doctor Avatar -->
        <div class="doctor-avatar flex-shrink-0">
          <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-400 to-indigo-500 dark:from-blue-500 dark:to-indigo-600 flex items-center justify-center text-white font-medium text-xl">
            {{ doctor.name.charAt(0) | uppercase }}
          </div>
        </div>

        <!-- Doctor Info -->
        <div class="doctor-info ml-3 flex-grow">
          <h3 class="font-medium text-gray-800 dark:text-gray-100">{{ doctor.name }}</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">{{ doctor.email }}</p>
        </div>

        <!-- Selection indicator -->
        <div class="selection-indicator" *ngIf="selectedDoctor && selectedDoctor.email === doctor.email">
          <svg class="w-6 h-6 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Dialog Actions -->
  <div class="dialog-actions p-6 pt-4 flex justify-end space-x-3 border-t border-gray-100 dark:border-gray-700 flex-shrink-0">
    <button
      (click)="cancel()"
      class="px-4 py-2 text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-500 transition-colors duration-200"
    >
      Cancel
    </button>
    <button
      (click)="confirm()"
      [disabled]="!selectedDoctor"
      [ngClass]="{'bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700': selectedDoctor, 'bg-blue-300 dark:bg-blue-800/50 cursor-not-allowed': !selectedDoctor}"
      class="px-4 py-2 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-1 dark:focus:ring-offset-gray-900 transition-colors duration-200"
    >
      Assign Doctor
    </button>
  </div>
</div>
