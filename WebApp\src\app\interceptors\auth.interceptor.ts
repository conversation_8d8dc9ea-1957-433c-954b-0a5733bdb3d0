import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService } from '../../shared/services/auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(
    private authService: AuthService,
    private router: Router
  ) { }

  // Check if the URL is a public endpoint that doesn't need authentication
  private isPublicEndpoint(url: string): boolean {
    const publicEndpoints = [
      '/auth/login',
      '/auth/register',
      '/auth/forgot-password',
      '/auth/reset-password',
    ];

    return publicEndpoints.some(endpoint => url.includes(endpoint));
  }

  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    // Skip authentication for public endpoints
    if (this.isPublicEndpoint(req.url)) {
      return next.handle(req);
    }

    // Get the auth token from the service
    const authToken = this.authService.getToken();

    // Clone the request and add the authorization header if token exists
    let authReq = req;
    if (authToken) {
      // Only validate token if we have one, but don't be too aggressive
      if (this.authService.isTokenValid()) {
        authReq = req.clone({
          setHeaders: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        });
        console.log('AuthInterceptor: Adding valid token to request');
      } else {
        console.log('AuthInterceptor: Token appears invalid, but letting request proceed');
        // Still send the token, let the server decide
        authReq = req.clone({
          setHeaders: {
            Authorization: `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        });
      }
    } else {
      console.log('AuthInterceptor: No token available for request to:', req.url);
      // For non-auth endpoints, proceed without token
      authReq = req.clone({
        setHeaders: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Send the cloned request with header to the next handler
    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        // Handle authentication errors more carefully
        if (error.status === 401) {
          console.log('AuthInterceptor: 401 Unauthorized received for:', req.url);

          // Only logout if we're sure the token is invalid
          // Check if we still have a token and if it's really expired
          const currentToken = this.authService.getToken();
          if (!currentToken || !this.authService.isTokenValid()) {
            console.log('AuthInterceptor: Token is missing or expired, forcing logout');
            this.authService.forceLogout('Authentication failed');
          } else {
            console.log('AuthInterceptor: Token exists and appears valid, not logging out');
            // Don't logout, just pass the error through
          }
        } else if (error.status === 403) {
          console.log('AuthInterceptor: 403 Forbidden - Insufficient permissions');
          // Forbidden - user doesn't have permission
          this.router.navigate(['/unauthorized']);
        }

        return throwError(() => error);
      })
    );
  }
}
