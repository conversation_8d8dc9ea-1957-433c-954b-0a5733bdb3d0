<div
  class="dialog-header bg-white dark:!bg-gray-800 rounded-t-lg px-4 py-3 border-b border-gray-200 dark:!border-gray-700">
  <h2 class="dialog-title text-lg font-semibold text-white">{{ dialogTitle }}</h2>
</div>

<div
  class="dialog-content bg-white dark:!bg-gray-800 p-6 rounded-b-lg border border-gray-200 dark:!border-gray-700 transition-colors duration-200">
  <form #routineForm="ngForm" class="routine-form space-y-6">
    <div class="form-group">
      <label for="day" class="form-label block text-sm font-medium text-gray-700 dark:!text-gray-300 mb-1">Day</label>
      <div class="custom-select-container relative">
        <select id="day" name="day" [(ngModel)]="routine.day" required
          class="custom-select w-full bg-white dark:!bg-gray-700 text-gray-900 dark:!text-gray-100 border border-gray-300 dark:!border-gray-600 rounded-md px-3 py-2 pl-10 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400"
          #day="ngModel" [class.invalid]="day.invalid && day.touched">
          <option value="" disabled selected>Select a day</option>
          <option *ngFor="let day of dayOptions" [value]="day">{{ day }}</option>
        </select>
        <div class="input-icon absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:!text-gray-500 pointer-events-none z-10">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
          </svg>

        </div>
      </div>
      <div class="error-message text-red-600 dark:!text-red-400 text-sm mt-1" *ngIf="day.invalid && day.touched">
        Day is required
      </div>
    </div>

    <div class="form-group">
      <label for="time" class="form-label block text-sm font-medium text-gray-700 dark:!text-gray-300 mb-1">Time</label>
      <div class="input-container relative">
        <input id="time" type="text" name="time" [(ngModel)]="routine.time" required
          class="custom-input w-full bg-white dark:!bg-gray-700 text-gray-900 dark:!text-gray-100 border border-gray-300 dark:!border-gray-600 rounded-md px-3 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400"
          placeholder="e.g., 08:00 AM" #time="ngModel" [class.invalid]="time.invalid && time.touched">
        <div class="input-icon absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:!text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
              clip-rule="evenodd" />
          </svg>
        </div>
      </div>
      <div class="error-message text-red-600 dark:!text-red-400 text-sm mt-1" *ngIf="time.invalid && time.touched">
        Time is required
      </div>
    </div>

    <div class="form-group">
      <label for="activity"
        class="form-label block text-sm font-medium text-gray-700 dark:!text-gray-300 mb-1">Activity</label>
      <div class="input-container relative">
        <input id="activity" type="text" name="activity" [(ngModel)]="routine.activity" required
          class="custom-input w-full bg-white dark:!bg-gray-700 text-gray-900 dark:!text-gray-100 border border-gray-300 dark:!border-gray-600 rounded-md px-3 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400"
          placeholder="Enter activity" #activity="ngModel" [class.invalid]="activity.invalid && activity.touched">
        <div class="input-icon absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:!text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path
              d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v1h8v-1zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-1a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v1h-3zM4.75 12.094A5.973 5.973 0 004 15v1H1v-1a3 3 0 013.75-2.906z" />
          </svg>
        </div>
      </div>
      <div class="error-message text-red-600 dark:!text-red-400 text-sm mt-1"
        *ngIf="activity.invalid && activity.touched">
        Activity is required
      </div>
    </div>

    <div class="form-group">
      <label for="description"
        class="form-label block text-sm font-medium text-gray-700 dark:!text-gray-300 mb-1">Description</label>
      <div class="textarea-container">
        <textarea id="description" name="description" [(ngModel)]="routine.description" required
          class="custom-textarea w-full bg-white dark:!bg-gray-700 text-gray-900 dark:!text-gray-100 border border-gray-300 dark:!border-gray-600 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:!focus:ring-blue-400"
          rows="3" placeholder="Enter description" #description="ngModel"
          [class.invalid]="description.invalid && description.touched"></textarea>
      </div>
      <div class="error-message text-red-600 dark:!text-red-400 text-sm mt-1"
        *ngIf="description.invalid && description.touched">
        Description is required
      </div>
    </div>
  </form>
</div>

<div
  class="dialog-actions flex justify-end gap-3 px-6 pb-6 bg-gray-50 dark:!bg-gray-800 border-t border-gray-200 dark:!border-gray-700 rounded-b-lg">
  <button
    class="btn btn-cancel px-4 py-2 rounded-md text-sm font-medium bg-white dark:!bg-gray-700 text-gray-700 dark:!text-gray-300 border border-gray-300 dark:!border-gray-600 hover:bg-gray-50 dark:!hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:!focus:ring-offset-gray-800 transition-colors duration-200"
    (click)="onCancel()">
    Cancel
  </button>
  <button
    class="btn btn-primary px-4 py-2 rounded-md text-sm font-medium text-white bg-blue-600 dark:!bg-blue-500 hover:bg-blue-700 dark:!hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:!focus:ring-offset-gray-800 transition-colors duration-200"
    [disabled]="routineForm.invalid" (click)="onSubmit()">
    {{ isEditMode ? 'Update' : 'Add' }}
  </button>
</div>
