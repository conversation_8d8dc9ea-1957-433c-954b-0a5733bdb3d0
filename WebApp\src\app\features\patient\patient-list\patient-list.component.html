<div class="min-h-screen  p-6">
  <div class="">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-5">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Patient Information</h1>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
            Manage and track patient records with active requests
          </p>
        </div>
       
      </div>
    </div>

    <!-- Stats Cards Section -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Patients Card -->
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden stats-card">
        <div class="p-4">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center w-14 h-14">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div class="ml-4">
              <div class="text-lg font-semibold text-gray-900 dark:text-white">Patients with Requests</div>
              <div class="flex items-baseline">
                <span class="text-3xl font-bold text-blue-800 dark:text-blue-300">{{patients.length || 0}}</span>
                <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">with requests</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Active Patients Card -->
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden stats-card">
        <div class="p-4">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center w-14 h-14">
              <svg class="w-6 h-6 text-green-600 dark:text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-4">
              <div class="text-lg font-semibold text-gray-900 dark:text-white">Active</div>
              <div class="flex items-baseline">
                <span class="text-3xl font-bold text-green-800 dark:text-green-300">{{filteredPatients.length || 0}}</span>
                <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">filtered</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Patients Card -->
      <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden stats-card">
        <div class="p-4">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center w-14 h-14">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-4">
              <div class="text-lg font-semibold text-gray-900 dark:text-white">All Patients</div>
              <div class="flex items-baseline">
                <span class="text-3xl font-bold text-purple-800 dark:text-purple-300">{{allPatients.length || 0}}</span>
                <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">total</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sync Patients Button - Only for Admins -->
      <div
        class="bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-700 dark:to-indigo-800 rounded-xl shadow-sm overflow-hidden stats-card cursor-pointer"
        (click)="syncPatients()" *ngIf="isAdmin">
        <div class="p-4 h-full flex items-center justify-center">
          <div class="text-center">
            <div class="p-3 rounded-full bg-white/20 flex items-center justify-center w-14 h-14 mx-auto mb-2">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            <div class="text-lg font-semibold text-white">Sync Patients</div>
            <div class="text-sm text-white/80">Import from registrations</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
      <div class="p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <!-- Search Bar -->
          <div class="flex-1 max-w-lg">
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            <input type="text" [(ngModel)]="searchTerm" (input)="onSearchChange()"
                class="w-full pl-10 pr-3 py-2 border outline-none border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 search-input"
                placeholder="Search patients by name, email, or phone...">
            </div>
          </div>

          <!-- Sort Options -->
          <div class="flex items-center gap-2">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-200">Sort by:</span>
            <div class="flex rounded-lg border border-gray-300 dark:border-gray-700 overflow-hidden">
              <button (click)="onSortChange('fullName')"
                class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-100  dark:hover:bg-gray-700 sort-button"
                [class.bg-blue-50]="sortBy === 'fullName'" [class.text-blue-700]="sortBy === 'fullName'" [class.dark:bg-blue-900]="sortBy === 'fullName'" [class.dark:text-blue-200]="sortBy === 'fullName'">
                Name
                <svg *ngIf="sortBy === 'fullName'" class="inline w-4 h-4 ml-1" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    [attr.d]="sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'" />
                </svg>
              </button>
              <button (click)="onSortChange('email')"
                class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 border-l border-gray-300 dark:border-gray-700 sort-button"
                [class.bg-blue-50]="sortBy === 'email'" [class.text-blue-700]="sortBy === 'email'" [class.dark:bg-blue-900]="sortBy === 'email'" [class.dark:text-blue-200]="sortBy === 'email'">
                Email
                <svg *ngIf="sortBy === 'email'" class="inline w-4 h-4 ml-1" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    [attr.d]="sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'" />
                </svg>
              </button>
              <button (click)="onSortChange('dateOfBirth')"
                class="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 border-l border-gray-300 dark:border-gray-700 sort-button"
                [class.bg-blue-50]="sortBy === 'dateOfBirth'" [class.text-blue-700]="sortBy === 'dateOfBirth'" [class.dark:bg-blue-900]="sortBy === 'dateOfBirth'" [class.dark:text-blue-200]="sortBy === 'dateOfBirth'">
                Age
                <svg *ngIf="sortBy === 'dateOfBirth'" class="inline w-4 h-4 ml-1" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    [attr.d]="sortDirection === 'asc' ? 'M5 15l7-7 7 7' : 'M19 9l-7 7-7-7'" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex justify-center items-center py-12">
      <div class="flex flex-col items-center">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent loading-spinner">
        </div>
        <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">Loading patients...</p>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoading && filteredPatients.length === 0"
      class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
      <div class="max-w-md mx-auto">
        <svg class="mx-auto h-16 w-16 text-gray-400 dark:text-gray-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No patients found</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
          <ng-container *ngIf="searchTerm; else noSearchTerm">
            No patients with requests match your search criteria.
          </ng-container>
          <ng-template #noSearchTerm>
            <ng-container *ngIf="isAdmin; else notAdmin">
              No patients with active requests found. Patients appear here when they submit requests through the mobile app.
            </ng-container>
            <ng-template #notAdmin>
              No patients with requests are currently assigned to you. Patients will appear here when requests are assigned to you.
            </ng-template>
          </ng-template>
        </p>
        <button *ngIf="isAdmin" (click)="syncPatients()"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Sync Patients
        </button>
      </div>
    </div>

    <!-- Patients Grid -->
    <div *ngIf="!isLoading && filteredPatients.length > 0" class="space-y-6">
      <!-- Patients List -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div *ngFor="let patient of getPaginatedPatients()"
          class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden patient-card hover:shadow-md dark:hover:shadow-lg transition-all duration-200 fade-in cursor-pointer relative group"
          (click)="viewPatientDetails(patient)">

          <!-- Edit/Delete Actions - Show on hover -->
          <div
            class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2 z-10">
            <button (click)="startChatWithPatient(patient); $event.stopPropagation()"
              class="p-2 rounded-full bg-white dark:bg-gray-800 shadow-md border border-gray-200 dark:border-gray-700 text-green-600 dark:text-green-300 hover:bg-green-50 dark:hover:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 action-button"
              title="Start Chat">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </button>
            <button *ngIf="canManagePatients" (click)="editPatient(patient); $event.stopPropagation()"
              class="p-2 rounded-full bg-white dark:bg-gray-800 shadow-md border border-gray-200 dark:border-gray-700 text-blue-600 dark:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 action-button">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            <button *ngIf="isAdmin" (click)="deletePatient(patient); $event.stopPropagation()"
              class="p-2 rounded-full bg-white dark:bg-gray-800 shadow-md border border-gray-200 dark:border-gray-700 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 action-button">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>

          <div class="p-6">
            <div class="flex items-start justify-between">
              <div class="flex items-center">
                <!-- Patient Avatar -->
                <div class="relative">
                  <div *ngIf="patient.profileImageUrl; else avatarFallback"
                    class="w-12 h-12 rounded-full overflow-hidden patient-avatar">
                    <img [src]="getImageUrl(patient.profileImageUrl)" [alt]="patient.fullName"
                      class="w-full h-full object-cover" (error)="onImageError($event)">
                  </div>
                  <ng-template #avatarFallback>
                    <div
                      class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-400 to-indigo-500 flex items-center justify-center patient-avatar">
                      <span class="text-white font-semibold text-lg">{{getPatientInitials(patient)}}</span>
                    </div>
                  </ng-template>
                </div>

                <div class="ml-4">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{(patient.fullName || 'Unnamed Patient').slice(0,11)}}<span
                      *ngIf="(patient.fullName || '').length > 11">...</span>
                  </h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">{{patient.email || 'No email'}}</p>
                  <div class="flex items-center mt-1">
                    <svg class="w-4 h-4 text-gray-400 dark:text-gray-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span class="text-xs text-gray-500 dark:text-gray-400">Age: {{getPatientAge(patient.dateOfBirth)}} years</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Patient Details -->
            <div class="mt-4 space-y-2">
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21L6.002 10l1.5 3 1.5 3 4.218-2.224a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.948V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                <span>{{patient.phoneNumber || 'No phone number'}}</span>
              </div>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span class="truncate">{{patient.address || 'No address provided'}}</span>
              </div>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>Born: {{formatDate(patient.dateOfBirth)}}</span>
              </div>

              <!-- Medical Information Indicators -->
              <div class="flex items-center space-x-2 mt-2">
                <div *ngIf="patient.allergies"
                  class="flex items-center px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-full text-xs">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z">
                    </path>
                  </svg>
                  Allergies
                </div>
                <div *ngIf="patient.medicalHistory"
                  class="flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-xs">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                    </path>
                  </svg>
                  Medical History
                </div>
                <div *ngIf="patient.emergencyContact"
                  class="flex items-center px-2 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-full text-xs">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Emergency Contact
                </div>
              </div>

              <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Updated: {{formatDate(patient.updatedDate)}}</span>
              </div>
            </div>

            <!-- Click to view indicator -->
            <div
              class="mt-4 flex items-center justify-center text-xs text-gray-400 dark:text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              Click to view details
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div *ngIf="totalPages > 1" class="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 px-6 py-4">
        <div class="flex flex-col sm:flex-row items-center justify-between">
          <!-- Results Info -->
          <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
            Showing <span class="font-medium">{{getStartIndex()}}</span> to
            <span class="font-medium">{{getEndIndex()}}</span> of
            <span class="font-medium">{{filteredPatients.length}}</span> patients
          </div>

          <!-- Pagination Navigation -->
          <div class="flex items-center space-x-2">
            <!-- Page Size Selector -->
            <div class="flex items-center mr-3">
              <label for="page-size" class="text-sm text-gray-600 dark:text-gray-400 mr-2">Show:</label>
              <select id="page-size" [(ngModel)]="patientsPerPage" (change)="onPageSizeChange()"
                class="border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-100 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500">
                <option value="6">6</option>
                <option value="12">12</option>
                <option value="18">18</option>
                <option value="24">24</option>
                <option value="30">30</option>
              </select>
            </div>

            <!-- Previous Button -->
            <button (click)="goToPage(currentPage - 1)" [disabled]="currentPage === 1"
              class="px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <!-- Page Numbers -->
            <button *ngFor="let page of getPaginationRange()" (click)="goToPage(page)" [disabled]="page < 0"
              [class]="page === currentPage ?
                'px-4 py-2 rounded-lg border border-blue-500 dark:border-blue-700 bg-blue-50 dark:bg-blue-900 text-sm font-medium text-blue-600 dark:text-blue-200' :
                page < 0 ? 'px-2 py-2 text-gray-400 dark:text-gray-500 cursor-default' :
                'px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'"
              class="transition-colors duration-200">
              <ng-container *ngIf="page > 0; else ellipsis">{{page}}</ng-container>
              <ng-template #ellipsis>...</ng-template>
            </button>

            <!-- Next Button -->
            <button (click)="goToPage(currentPage + 1)" [disabled]="currentPage === totalPages"
              class="px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
