import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ChatThreadDto, AvailablePatientDto, ChatMessageResponseDto } from '../../shared/service-proxies/service-proxies';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

@Injectable({
  providedIn: 'root'
})
export class ChatCacheService {
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MESSAGE_CACHE_TTL = 30 * 60 * 1000; // 30 minutes for messages

  // Cache storage
  private chatThreadsCache: CacheEntry<ChatThreadDto[]> | null = null;
  private availablePatientsCache: CacheEntry<AvailablePatientDto[]> | null = null;

  // Loading state tracking to prevent duplicate API calls
  private isLoadingChatThreads = false;
  private isLoadingAvailablePatients = false;
  private messagesCache = new Map<string, CacheEntry<ChatMessageResponseDto[]>>();

  // Subjects for reactive updates
  private chatThreadsSubject = new BehaviorSubject<ChatThreadDto[]>([]);
  private availablePatientsSubject = new BehaviorSubject<AvailablePatientDto[]>([]);

  // Observable streams
  public chatThreads$ = this.chatThreadsSubject.asObservable();
  public availablePatients$ = this.availablePatientsSubject.asObservable();

  constructor() { }

  // Chat Threads Caching
  setChatThreads(threads: ChatThreadDto[]): void {
    this.chatThreadsCache = {
      data: threads,
      timestamp: Date.now(),
      ttl: this.CACHE_TTL
    };
    this.chatThreadsSubject.next(threads);
  }

  getCachedChatThreads(): ChatThreadDto[] | null {
    if (!this.chatThreadsCache || this.isExpired(this.chatThreadsCache)) {
      return null;
    }
    return this.chatThreadsCache.data;
  }

  // Available Patients Caching
  setAvailablePatients(patients: AvailablePatientDto[]): void {
    this.availablePatientsCache = {
      data: patients,
      timestamp: Date.now(),
      ttl: this.CACHE_TTL
    };
    this.availablePatientsSubject.next(patients);
  }

  getCachedAvailablePatients(): AvailablePatientDto[] | null {
    if (!this.availablePatientsCache || this.isExpired(this.availablePatientsCache)) {
      return null;
    }
    return this.availablePatientsCache.data;
  }

  // Messages Caching
  setChatMessages(doctorId: string, patientId: string, messages: ChatMessageResponseDto[]): void {
    const key = this.getChatKey(doctorId, patientId);
    this.messagesCache.set(key, {
      data: messages,
      timestamp: Date.now(),
      ttl: this.MESSAGE_CACHE_TTL
    });
  }

  getCachedChatMessages(doctorId: string, patientId: string): ChatMessageResponseDto[] | null {
    const key = this.getChatKey(doctorId, patientId);
    const cached = this.messagesCache.get(key);

    if (!cached || this.isExpired(cached)) {
      this.messagesCache.delete(key);
      return null;
    }

    return cached.data;
  }

  // Update specific chat thread (for real-time updates)
  updateChatThread(updatedThread: ChatThreadDto): void {
    if (this.chatThreadsCache) {
      const threads = this.chatThreadsCache.data;
      const index = threads.findIndex(t =>
        t.doctorId === updatedThread.doctorId && t.patientId === updatedThread.patientId
      );

      if (index >= 0) {
        threads[index] = updatedThread;
        this.chatThreadsSubject.next([...threads]);
      }
    }
  }

  // Add new message to cached messages
  addMessageToCache(doctorId: string, patientId: string, message: ChatMessageResponseDto): void {
    const key = this.getChatKey(doctorId, patientId);
    const cached = this.messagesCache.get(key);

    if (cached && !this.isExpired(cached)) {
      cached.data.push(message);
      // Update timestamp to keep cache fresh
      cached.timestamp = Date.now();
    }
  }

  // Clear specific cache entries
  clearChatThreadsCache(): void {
    this.chatThreadsCache = null;
  }

  clearAvailablePatientsCache(): void {
    this.availablePatientsCache = null;
  }

  clearChatMessagesCache(doctorId?: string, patientId?: string): void {
    if (doctorId && patientId) {
      const key = this.getChatKey(doctorId, patientId);
      this.messagesCache.delete(key);
    } else {
      this.messagesCache.clear();
    }
  }

  // Loading state management to prevent duplicate API calls
  isLoadingChatThreadsState(): boolean {
    return this.isLoadingChatThreads;
  }

  setLoadingChatThreads(loading: boolean): void {
    this.isLoadingChatThreads = loading;
  }

  isLoadingAvailablePatientsState(): boolean {
    return this.isLoadingAvailablePatients;
  }

  setLoadingAvailablePatients(loading: boolean): void {
    this.isLoadingAvailablePatients = loading;
  }

  // Clear all caches
  clearAllCaches(): void {
    this.chatThreadsCache = null;
    this.availablePatientsCache = null;
    this.messagesCache.clear();
    this.isLoadingChatThreads = false;
    this.isLoadingAvailablePatients = false;
  }

  // Utility methods
  private getChatKey(doctorId: string, patientId: string): string {
    return `${doctorId}-${patientId}`;
  }

  private isExpired(cacheEntry: CacheEntry<any>): boolean {
    return Date.now() - cacheEntry.timestamp > cacheEntry.ttl;
  }

  // Get cache statistics (for debugging)
  getCacheStats(): any {
    return {
      chatThreadsCached: !!this.chatThreadsCache && !this.isExpired(this.chatThreadsCache),
      availablePatientsCached: !!this.availablePatientsCache && !this.isExpired(this.availablePatientsCache),
      messagesCacheSize: this.messagesCache.size,
      messagesKeys: Array.from(this.messagesCache.keys())
    };
  }
}
