<div class="min-h-screen p-5">
  <div class="">
    <!-- Header -->
    <div
      class="bg-gradient-to-r from-blue-50 to-teal-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-5">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold dark:text-white text-gray-900">Patient Details</h1>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-200">
            View and manage patient information
          </p>
        </div>
        <button (click)="goBack()"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 back-button dark:bg-gray-800 dark:text-white dark:border dark:border-gray-700">
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to List
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex justify-center items-center py-12">
      <div class="flex flex-col items-center">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent loading-spinner">
        </div>
        <p class="mt-4 text-sm text-gray-500">Loading patient details...</p>
      </div>
    </div>

    <!-- Patient Details -->
    <div *ngIf="!isLoading && patient" class="space-y-6 fade-in">
      <!-- Patient Profile Header -->
      <div
        class="rounded-xl shadow-sm border overflow-hidden patient-profile transition-colors duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <div class="relative bg-gradient-to-r from-blue-500 to-teal-600 dark:from-blue-950 dark:to-teal-950 px-6 py-8">
          <div class="flex items-center">
            <!-- Patient Avatar -->
            <div class="relative profile-image-container">
              <div *ngIf="patient.profileImageUrl; else avatarFallback"
                class="w-24 h-24 rounded-full overflow-hidden border-4 border-white dark:border-gray-900 shadow-lg patient-avatar">
                <img [src]="getImageUrl(patient.profileImageUrl)" [alt]="patient.fullName"
                  class="w-full h-full object-cover" (error)="onImageError($event)">
              </div>
              <ng-template #avatarFallback>
                <div
                  class="w-24 h-24 rounded-full flex items-center justify-center border-4 border-white dark:border-gray-900 shadow-lg patient-avatar bg-white dark:bg-gray-900">
                  <span class="font-bold text-2xl text-blue-600 dark:text-blue-400">{{getPatientInitials()}}</span>
                </div>
              </ng-template>
            </div>

            <div class="ml-6 text-white dark:text-gray-100">
              <h2 class="text-2xl font-bold">{{patient.fullName || 'Unnamed Patient'}}</h2>
              <p class="text-blue-100 dark:text-blue-200 text-lg">{{patient.email || 'No email provided'}}</p>
              <div class="flex items-center mt-2">
                <span
                  class="badge badge-primary bg-white/20 dark:bg-gray-900/30 text-white dark:text-gray-200 border border-white/30 dark:border-gray-600/30">
                  Age: {{getPatientAge()}} years
                </span>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="absolute top-6 right-6 flex space-x-3">
            <button (click)="startChatWithPatient()"
              class="inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium text-white dark:text-gray-200 bg-green-500/80 dark:bg-green-600/80 hover:bg-green-600 dark:hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 action-button border-green-300 dark:border-green-500/50"
              title="Start Chat">
              <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Chat
            </button>
            <button *ngIf="canManagePatients" (click)="editPatient()"
              class="inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium text-white dark:text-gray-200 bg-white/20 dark:bg-gray-900/20 hover:bg-white/30 dark:hover:bg-gray-900/30 focus:outline-none focus:ring-2 focus:ring-white dark:focus:ring-gray-400 action-button border-white/30 dark:border-gray-600/30">
              <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit
            </button>
            <button *ngIf="isAdmin" (click)="deletePatient()"
              class="inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium text-white dark:text-red-200 bg-red-500/80 dark:bg-red-600/80 hover:bg-red-600 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 action-button border-red-300 dark:border-red-500/50">
              <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Delete
            </button>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Contact Information -->
        <div
          class="rounded-xl shadow-sm border overflow-hidden info-card transition-colors duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Contact Information</h3>
          </div>
          <div class="p-6 space-y-4">
            <!-- Email -->
            <div class="contact-item flex items-center">
              <div class="contact-icon icon-email mr-3 text-gray-500 dark:text-gray-400">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Email Address</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">{{patient.email || 'Not provided'}}</div>
              </div>
            </div>

            <!-- Phone -->
            <div class="contact-item flex items-center">
              <div class="contact-icon icon-phone mr-3 text-gray-500 dark:text-gray-400">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21L6.002 10l1.5 3 1.5 3 4.218-2.224a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.948V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Phone Number</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">{{patient.phoneNumber || 'Not provided'}}</div>
              </div>
            </div>

            <!-- Address -->
            <div class="contact-item flex items-center">
              <div class="contact-icon icon-address mr-3 text-gray-500 dark:text-gray-400">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Address</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">{{patient.address || 'Not provided'}}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Personal Information -->
        <div
          class="rounded-xl shadow-sm border overflow-hidden info-card transition-colors duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Personal Information</h3>
          </div>
          <div class="p-6 space-y-4">
            <!-- Date of Birth -->
            <div class="contact-item flex items-center">
              <div class="contact-icon icon-birthday mr-3 text-gray-500 dark:text-gray-400">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Date of Birth</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">{{formatDate(patient.dateOfBirth)}}</div>
              </div>
            </div>

            <!-- Age -->
            <div class="stat-item p-3 rounded-lg bg-gray-50 dark:bg-gray-900/50">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Current Age</span>
                <span class="text-lg font-bold text-blue-600 dark:text-blue-400">{{getPatientAge()}} years</span>
              </div>
            </div>

            <!-- Last Updated -->
            <div class="contact-item flex items-center">
              <div class="contact-icon icon-updated mr-3 text-gray-500 dark:text-gray-400">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Last Updated</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">{{formatDate(patient.updatedDate)}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Medical Information -->
      <div class="grid grid-cols-1 gap-6">
        <!-- Emergency Contact -->
        <div *ngIf="patient.emergencyContact"
          class="rounded-xl shadow-sm border overflow-hidden info-card transition-colors duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <svg class="w-5 h-5 mr-2 text-red-500 dark:text-red-400" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Emergency Contact
            </h3>
          </div>
          <div class="p-6">
            <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700/50 rounded-lg p-4">
              <p class="text-gray-900 dark:text-red-200 font-medium">{{ patient.emergencyContact }}</p>
            </div>
          </div>
        </div>

        <!-- Medical History -->
        <div *ngIf="patient.medicalHistory"
          class="rounded-xl shadow-sm border overflow-hidden info-card transition-colors duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <svg class="w-5 h-5 mr-2 text-blue-500 dark:text-blue-400" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                </path>
              </svg>
              Medical History
            </h3>
          </div>
          <div class="p-6">
            <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700/50 rounded-lg p-4">
              <p class="text-gray-900 dark:text-blue-200 whitespace-pre-wrap">{{ patient.medicalHistory }}</p>
            </div>
          </div>
        </div>

        <!-- Allergies -->
        <div *ngIf="patient.allergies"
          class="rounded-xl shadow-sm border overflow-hidden info-card transition-colors duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <svg class="w-5 h-5 mr-2 text-yellow-500 dark:text-yellow-400" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z">
                </path>
              </svg>
              Allergies & Adverse Reactions
            </h3>
          </div>
          <div class="p-6">
            <div
              class="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700/50 rounded-lg p-4">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z">
                  </path>
                </svg>
                <p class="text-yellow-800 dark:text-yellow-200 font-medium whitespace-pre-wrap">{{ patient.allergies }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="rounded-xl shadow-sm border overflow-hidden info-card transition-colors duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Patient Medical Images</h3>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">Total: {{allImageAttachments.length}} images</span>
            </div>
          </div>
        </div>

        <!-- Loading State for Images -->
        <div *ngIf="isLoadingImages" class="flex justify-center items-center py-12">
          <div class="flex flex-col items-center">
            <div
              class="animate-spin rounded-full h-8 w-8 border-4 border-blue-500 dark:border-blue-400 border-t-transparent">
            </div>
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading images...</p>
          </div>
        </div>

        <!-- Gallery Filters -->
        <div *ngIf="!isLoadingImages && allImageAttachments.length > 0"
          class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
          <div class="flex items-center space-x-4">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Filter by:</span>
            <div class="flex space-x-2">
              <button (click)="setImageGalleryFilter('all')"
                [class]="'px-3 py-1 text-xs rounded-full transition-colors ' +
                   (imageGalleryFilter === 'all' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600')">
                All Images
              </button>
              <button (click)="setImageGalleryFilter('byProject')"
                [class]="'px-3 py-1 text-xs rounded-full transition-colors ' +
                   (imageGalleryFilter === 'byProject' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600')">
                By Request
              </button>
            </div>
          </div>
        </div>

        <!-- Gallery Content -->
        <div class="p-6 relative">

          <!-- Floating Comparison Button -->
          <div *ngIf="!isComparisonMode && !isLoadingImages && getFilteredImages().length >= 2"
            class="fixed bottom-6 right-6 z-50">
            <button (click)="toggleComparisonMode()"
              class="group bg-gradient-to-r from-blue-600 to-teal-600 hover:from-blue-700 hover:to-teal-700 text-white p-4 rounded-full shadow-2xl hover:shadow-blue-500/25 transform hover:scale-110 transition-all duration-300 border-2 border-white/20">
              <svg class="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0v10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2z" />
              </svg>
              <!-- Tooltip -->
              <div
                class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                Compare Images
                <div
                  class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900">
                </div>
              </div>
            </button>
          </div>


          <!-- Image Comparison Call-to-Action -->
          <div *ngIf="!isComparisonMode && getFilteredImages().length >= 2"
            class="mb-4 p-4 bg-gradient-to-r from-blue-50 to-teal-50 dark:from-blue-900/20 dark:to-teal-900/20 rounded-xl border border-blue-200 dark:border-blue-700/50">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
                  <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0v10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2z" />
                  </svg>
                </div>
                <div>
                  <h6 class="font-semibold text-blue-900 dark:text-blue-100 mb-1">Compare Medical Images</h6>
                  <p class="text-sm text-blue-700 dark:text-blue-300">Select any 2 images for side-by-side comparison
                    analysis</p>
                </div>
              </div>
              <button (click)="toggleComparisonMode()"
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:scale-105">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM14 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM14 13a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1h-4a1 1 0 01-1-1v-6z" />
                </svg>
                Start Comparing
              </button>
            </div>
          </div>

          <!-- Quick Actions Bar -->
          <div class="mb-6 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-600 dark:text-gray-400">{{getFilteredImages().length}} images
                found</span>
              <div class="flex items-center space-x-2">
                <button (click)="selectAllImages()"
                  class="text-xs px-3 py-1 bg-blue-100 dark:bg-blue-900/50 hover:bg-blue-200 dark:hover:bg-blue-800/50 text-blue-700 dark:text-blue-200 rounded-full transition-colors">
                  Select All
                </button>
                <button (click)="downloadAllImages()"
                  class="text-xs px-3 py-1 bg-blue-100 dark:bg-blue-900/50 hover:bg-blue-200 dark:hover:bg-blue-800/50 text-blue-700 dark:text-blue-200 rounded-full transition-colors">
                  Download All
                </button>
                <!-- Compare Images Button -->
                <button (click)="toggleComparisonMode()"
                  [class]="isComparisonMode ? 'text-xs px-3 py-1 bg-blue-200 dark:bg-blue-800/50 text-blue-800 dark:text-blue-200 rounded-full transition-colors font-medium' : 'text-xs px-3 py-1 bg-blue-100 dark:bg-blue-900/50 hover:bg-blue-200 dark:hover:bg-blue-800/50 text-blue-700 dark:text-blue-200 rounded-full transition-colors'"
                  title="Compare Images - Select 2 images to compare side by side">
                  <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM14 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM14 13a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1h-4a1 1 0 01-1-1v-6z" />
                  </svg>
                  {{isComparisonMode ? 'Exit Compare' : 'Compare Images'}}
                </button>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button (click)="setGridView()"
                [class]="isGridView && !isComparisonMode ? 'view-toggle-button active p-2 rounded-lg transition-colors bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-200' : 'view-toggle-button p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors text-gray-600 dark:text-gray-400'"
                title="Grid View" [disabled]="isComparisonMode">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </button>
              <button (click)="setListView()"
                [class]="!isGridView && !isComparisonMode ? 'view-toggle-button active p-2 rounded-lg transition-colors bg-blue-100 dark:!bg-blue-900/50 text-blue-700 dark:text-blue-200' : 'view-toggle-button p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors text-gray-600 dark:text-gray-400'"
                title="List View" [disabled]="isComparisonMode">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
              </button>

              <!-- Comparison Mode Toggle -->
              <div class="border-l border-gray-300 dark:border-gray-600 pl-2 ml-2">
                <button (click)="toggleComparisonMode()"
                  [class]="isComparisonMode ? 'view-toggle-button active p-2 rounded-lg transition-colors bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-200' : 'view-toggle-button p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors text-gray-600 dark:text-gray-400'"
                  title="Comparison Mode - Select 2 images to compare">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM14 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM14 13a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1h-4a1 1 0 01-1-1v-6z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <!-- Show All Images or Recent Images -->

          <div *ngIf="!isLoadingImages && imageGalleryFilter !== 'byProject' && getFilteredImages().length > 0">


            <!-- Grid View -->
            <div *ngIf="isGridView" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              <div *ngFor="let image of getFilteredImages(); let i = index"
                [class]="'relative group cursor-pointer rounded-xl overflow-hidden bg-gradient-to-br from-gray-100 dark:from-gray-700 to-gray-200 dark:to-gray-600 aspect-square hover:shadow-2xl transition-all-smooth image-grid-item' +
                   (isComparisonMode && isImageSelectedForComparison(image) ? ' ring-4 ring-blue-500 dark:ring-blue-400 ring-opacity-75 scale-95' : '') +
                   (isComparisonMode && !canSelectMoreImages() && !isImageSelectedForComparison(image) ? ' opacity-50' : '')"
                (click)="isComparisonMode && isImageSelectedForComparison(image) ? deselectImageForComparison(image, $event) : isComparisonMode ? selectImageForComparison(image, $event) : openImageLightbox(i)">

                <!-- Comparison Mode Selection Indicator -->
                <div *ngIf="isComparisonMode" class="absolute top-2 right-2 z-20">
                  <div *ngIf="isImageSelectedForComparison(image)"
                    class="w-8 h-8 bg-blue-500 dark:bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                    {{getComparisonSelectionIndex(image)}}
                  </div>
                  <div *ngIf="!isImageSelectedForComparison(image) && canSelectMoreImages()"
                    class="w-8 h-8 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center shadow-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
                    <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                </div>

                <!-- Click to Deselect Indicator for Selected Images -->
                <div *ngIf="isComparisonMode && isImageSelectedForComparison(image)"
                  class="absolute bottom-2 left-2 z-20 bg-red-500/90 dark:bg-red-600/90 text-white px-2 py-1 rounded-lg text-xs font-medium shadow-lg backdrop-blur-sm">
                  <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Click to deselect
                </div>

                <!-- Loading indicator -->
                <div
                  class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-200 dark:from-gray-700 to-gray-300 dark:to-gray-600 z-10 image-placeholder"
                  #loadingIndicator>
                  <div
                    class="animate-spin rounded-full h-8 w-8 border-4 border-blue-500 dark:border-blue-400 border-t-transparent">
                  </div>
                </div>

                <!-- Image -->
                <img [src]="image.url" [alt]="image.name"
                  class="w-full h-full object-cover group-hover:scale-110 transition-all-smooth"
                  (load)="loadingIndicator.style.display='none'"
                  (error)="onImageError($event); loadingIndicator.style.display='none'"
                  style="background-color: #f3f4f6; min-height: 100px;" loading="lazy">

                <!-- Gradient Overlay -->
                <div
                  class="absolute inset-0 bg-gradient-to-t from-black dark:from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-60 transition-all duration-300">
                </div>

                <!-- Overlay with image info -->
                <div
                  class="absolute inset-0 flex items-end p-3 transform translate-y-full group-hover:translate-y-0 transition-all-smooth">
                  <div class="text-white dark:text-gray-200">
                    <p class="text-xs font-semibold truncate mb-1">{{image.name}}</p>
                    <p class="text-xs opacity-90">{{formatDateShort(image.uploadDate)}}</p>
                  </div>
                </div>

                <!-- Project indicator -->
                <div
                  class="absolute top-2 right-2 bg-gradient-to-r from-blue-500 dark:from-blue-600 to-blue-600 dark:to-blue-700 text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 shadow-lg">
                  #{{image.projectId}}
                </div>

                <!-- Action buttons on hover -->
                <div
                  class="absolute top-2 left-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <button (click)="openImagePreview(image); $event.stopPropagation()"
                    class="bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 p-1.5 rounded-full shadow-lg transform hover:scale-110 transition-all duration-200"
                    title="Preview Image">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button (click)="downloadImage(image); $event.stopPropagation()"
                    class="bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 p-1.5 rounded-full shadow-lg transform hover:scale-110 transition-all duration-200"
                    title="Download Image">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                    </svg>
                  </button>
                  <!-- File Info Button -->
                  <button (click)="openFileInfoDialog(image); $event.stopPropagation()"
                    class="bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 p-1.5 rounded-full shadow-lg transform hover:scale-110 transition-all duration-200"
                    title="File Information">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </button>
                  <!-- Quick Compare Button -->
                  <button (click)="quickCompareWithThis(image); $event.stopPropagation()"
                    class="bg-gradient-to-r from-blue-500 dark:from-blue-600 to-blue-600 dark:to-blue-700 hover:from-blue-600 dark:hover:from-blue-700 hover:to-blue-700 dark:hover:to-blue-800 text-white p-1.5 rounded-full shadow-lg transform hover:scale-110 transition-all duration-200"
                    title="Quick Compare - Select another image">
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM14 5a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM14 13a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1h-4a1 1 0 01-1-1v-6z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- List View -->
            <div *ngIf="!isGridView" class="space-y-4">
              <div *ngFor="let image of getFilteredImages(); let i = index"
                [class]="'image-list-item  cursor-pointer rounded-lg p-3 transition-colors  ' +
                   (isComparisonMode && isImageSelectedForComparison(image) ? ' ring-4 ring-blue-500 dark:ring-blue-400 ring-opacity-75 bg-blue-50 dark:!bg-blue-900/50' : '') +
                   (isComparisonMode && !canSelectMoreImages() && !isImageSelectedForComparison(image) ? ' opacity-50' : '')"
                (click)="isComparisonMode && isImageSelectedForComparison(image) ? deselectImageForComparison(image, $event) : isComparisonMode ? selectImageForComparison(image, $event) : openImageLightbox(i)">

                <div class="flex items-center">
                  <!-- Comparison Mode Selection Indicator -->
                  <div *ngIf="isComparisonMode" class="relative mr-3">
                    <div *ngIf="isImageSelectedForComparison(image)"
                      class="w-8 h-8 bg-blue-500 dark:bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                      {{getComparisonSelectionIndex(image)}}
                    </div>
                    <div *ngIf="!isImageSelectedForComparison(image) && canSelectMoreImages()"
                      class="w-8 h-8 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center shadow-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
                      <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <!-- Click to Deselect Indicator for Selected Images in List View -->
                    <div *ngIf="isImageSelectedForComparison(image)"
                      class="absolute -bottom-1 -right-1 bg-red-500/90 dark:bg-red-600/90 text-white px-1 py-0.5 rounded text-xs font-medium shadow-lg">
                      ✕
                    </div>
                  </div>
                  <!-- Thumbnail -->
                  <div class="thumbnail-container relative w-20 h-20 md:w-24 md:h-24 flex-shrink-0">
                    <div class="absolute inset-0 flex items-center justify-center z-10" #listLoadingIndicator>
                      <div
                        class="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 dark:border-blue-400 border-t-transparent">
                      </div>
                    </div>
                    <img [src]="image.url" [alt]="image.name" class="w-full h-full object-cover rounded-lg"
                      (load)="listLoadingIndicator.style.display='none'"
                      (error)="onImageError($event); listLoadingIndicator.style.display='none'" loading="lazy">
                  </div>

                  <!-- Content -->
                  <div class="content-section flex-1 min-w-0 ml-4">
                    <div class="flex items-start justify-between">
                      <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate mb-1">{{image.name}}
                        </h4>
                        <div class="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400">
                          <span class="flex items-center">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            {{formatDateShort(image.uploadDate)}}
                          </span>
                          <span class="flex items-center">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                            Project #{{image.projectId}}
                          </span>
                          <span *ngIf="image.size" class="flex items-center">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            {{image.size}}
                          </span>
                        </div>
                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 truncate">{{image.projectTitle}}</p>
                      </div>

                      <!-- Action Buttons -->
                      <div class="action-buttons flex items-center space-x-2">
                        <button (click)="openImagePreview(image); $event.stopPropagation()"
                          class="action-button p-2 bg-blue-50 dark:bg-blue-900/50 hover:bg-blue-100 dark:hover:bg-blue-800/50 text-blue-600 dark:text-blue-200 rounded-lg"
                          title="Preview">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                        <button (click)="downloadImage(image); $event.stopPropagation()"
                          class="action-button p-2 bg-blue-50 dark:bg-blue-900/50 hover:bg-blue-100 dark:hover:bg-blue-800/50 text-blue-600 dark:text-blue-200 rounded-lg"
                          title="Download">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                          </svg>
                        </button>
                        <button (click)="openFileInfoDialog(image); $event.stopPropagation()"
                          class="action-button p-2 bg-blue-50 dark:bg-blue-900/50 hover:bg-blue-100 dark:hover:bg-blue-800/50 text-blue-600 dark:text-blue-200 rounded-lg"
                          title="File Information">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>

          <!-- Show Images Grouped by Request/Project -->
          <div *ngIf="!isLoadingImages && imageGalleryFilter === 'byProject' && getFilteredImages().length > 0">
            <div class="space-y-6">
              <div *ngFor="let project of patientProjects"
                class="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                <!-- Request Header -->
                <div class="mb-4 pb-4 border-b border-gray-200 dark:border-gray-700">
                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100">{{project.subject}}</h4>
                      <div class="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <span class="flex items-center">
                          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          {{formatDate(project.createdDate)}}
                        </span>
                        <span class="flex items-center">
                          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          Request #{{project.id}}
                        </span>
                        <span class="px-2 py-1 text-xs rounded-full" [class]="project.status === 'Open' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200' :
                               project.status === 'Closed' ? 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200' :
                               'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200'">
                          {{project.status}}
                        </span>
                        <span class="text-gray-500 dark:text-gray-400">
                          {{project.imageAttachments.length}} image{{project.imageAttachments.length !== 1 ? 's' :
                          ''}}
                        </span>
                      </div>
                    </div>
                  </div>
                  <p *ngIf="project.message"
                    class="mt-3 text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
                    {{project.message}}
                  </p>
                </div>

                <!-- Request Images -->
                <div *ngIf="project.imageAttachments.length > 0"
                  class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                  <div *ngFor="let image of project.imageAttachments; let i = index"
                    [class]="'relative group cursor-pointer rounded-xl overflow-hidden bg-gradient-to-br from-gray-100 dark:from-gray-700 to-gray-200 dark:to-gray-600 aspect-square hover:shadow-2xl transition-all-smooth image-grid-item' +
                         (isComparisonMode && isImageSelectedForComparison(image) ? ' ring-4 ring-blue-500 dark:ring-blue-400 ring-opacity-75 scale-95' : '') +
                         (isComparisonMode && !canSelectMoreImages() && !isImageSelectedForComparison(image) ? ' opacity-50' : '')"
                    (click)="isComparisonMode && isImageSelectedForComparison(image) ? deselectImageForComparison(image, $event) : isComparisonMode ? selectImageForComparison(image, $event) : openImageLightbox(allImageAttachments.indexOf(image))">

                    <!-- Comparison Mode Selection Indicator -->
                    <div *ngIf="isComparisonMode" class="absolute top-2 right-2 z-20">
                      <div *ngIf="isImageSelectedForComparison(image)"
                        class="w-8 h-8 bg-blue-500 dark:bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                        {{getComparisonSelectionIndex(image)}}
                      </div>
                      <div *ngIf="!isImageSelectedForComparison(image) && canSelectMoreImages()"
                        class="w-8 h-8 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-full flex items-center justify-center shadow-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors">
                        <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor"
                          viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </div>
                    </div>

                    <!-- Loading indicator -->
                    <div
                      class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-200 dark:from-gray-700 to-gray-300 dark:to-gray-600 z-10 image-placeholder"
                      #projectLoadingIndicator>
                      <div
                        class="animate-spin rounded-full h-8 w-8 border-4 border-blue-500 dark:border-blue-400 border-t-transparent">
                      </div>
                    </div>

                    <!-- Image -->
                    <img [src]="image.url" [alt]="image.name"
                      class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                      (load)="projectLoadingIndicator.style.display='none'"
                      (error)="onImageError($event); projectLoadingIndicator.style.display='none'" loading="lazy">

                    <!-- Image overlay with info -->
                    <div
                      class="absolute inset-0 bg-gradient-to-t from-black/60 dark:from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div class="absolute bottom-2 left-2 right-2">
                        <p class="text-white dark:text-gray-200 text-xs font-medium truncate">{{image.name}}</p>
                        <p class="text-white/80 dark:text-gray-300 text-xs">{{formatDateShort(image.uploadDate)}}</p>
                      </div>
                    </div>

                    <!-- Quick action buttons -->
                    <div
                      class="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-1">
                      <button (click)="openImagePreview(image); $event.stopPropagation()"
                        class="p-1.5 bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg shadow-sm"
                        title="Preview">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      <button (click)="downloadImage(image); $event.stopPropagation()"
                        class="p-1.5 bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg shadow-sm"
                        title="Download">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                        </svg>
                      </button>
                      <button (click)="openFileInfoDialog(image); $event.stopPropagation()"
                        class="p-1.5 bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg shadow-sm"
                        title="File Information">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- No images in this request -->
                <div *ngIf="project.imageAttachments.length === 0"
                  class="text-center py-8 text-gray-500 dark:text-gray-400">
                  <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-2" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p class="text-sm">No images in this request</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div *ngIf="!isLoadingImages && allImageAttachments.length === 0" class="text-center py-12">
            <svg class="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Medical Images</h3>
            <p class="text-gray-500 dark:text-gray-400">This patient doesn't have any uploaded medical images yet.</p>
          </div>

          <!-- No Images in Current Filter -->
          <div *ngIf="!isLoadingImages && allImageAttachments.length > 0 && getFilteredImages().length === 0"
            class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <h4 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-1">No images found</h4>
            <p class="text-gray-500 dark:text-gray-400 text-sm">No images match the current filter criteria.</p>
          </div>
        </div>

      </div>

      <!-- Empty State -->
      <div *ngIf="!isLoading && !patient"
        class="rounded-xl shadow-sm border p-12 text-center transition-colors duration-200 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <div class="max-w-md mx-auto">
          <svg class="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-4" fill="none" stroke="currentColor"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Patient not found</h3>
          <p class="text-gray-500 dark:text-gray-400 mb-6">The requested patient could not be found or does not exist.
          </p>
          <button (click)="goBack()"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400">
            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Patient List
          </button>
        </div>
      </div>

      <!-- Enhanced Lightbox Modal -->
      <div *ngIf="isLightboxOpen"
        class="fixed inset-0 z-50 bg-gradient-to-br from-black dark:from-gray-900 via-gray-900 dark:via-gray-800 to-black dark:to-gray-900 bg-opacity-95 dark:bg-opacity-90 flex items-center justify-center backdrop-blur-sm"
        (click)="closeLightbox()" (keydown.escape)="closeLightbox()">

        <!-- Main Container -->
        <div class="relative max-w-7xl max-h-full w-full h-full flex flex-col p-4 animate-fadeIn">

          <!-- Top Controls Bar -->
          <div
            class="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black dark:from-gray-900 via-black/50 dark:via-gray-800/50 to-transparent p-6"
            style="height: 95vh;">
            <div class="flex items-center justify-between" style="max-width: 90%; min-width: 90%;;">
              <!-- Image Counter & Info -->
              <div class="flex items-center space-x-4">
                <div
                  class="bg-white/10 dark:bg-gray-700/50 backdrop-blur-md rounded-full px-4 py-2 border border-white/20 dark:border-gray-600/50">
                  <span class="text-white dark:text-gray-200 font-medium">
                    {{selectedImageIndex + 1}} of {{allImageAttachments.length}}
                  </span>
                </div>
                <div class="text-white dark:text-gray-200">
                  <h3 class="font-semibold text-lg">{{allImageAttachments[selectedImageIndex].name}}</h3>
                  <p class="text-gray-300 dark:text-gray-400 text-sm">
                    Project: {{allImageAttachments[selectedImageIndex].projectTitle}} •
                    {{formatDateShort(allImageAttachments[selectedImageIndex].uploadDate)}}
                  </p>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center space-x-3">
                <!-- Zoom Status -->
                <div *ngIf="zoomLevel !== 1"
                  class="bg-gradient-to-r from-emerald-500/20 dark:from-emerald-600/30 to-teal-600/20 dark:to-teal-700/30 backdrop-blur-md rounded-full px-3 py-1 border border-emerald-400/30 dark:border-emerald-500/40 shadow-lg">
                  <span class="text-white dark:text-gray-200 text-sm font-medium">
                    Zoom: {{(zoomLevel * 100).toFixed(0)}}%
                    <span *ngIf="zoomLevel > 1" class="text-emerald-300 dark:text-emerald-400 ml-1">(Click & drag to
                      pan)</span>
                  </span>
                </div>

                <!-- Zoom Controls -->
                <div
                  class="flex items-center space-x-1 bg-gradient-to-r from-blue-500/20 dark:from-blue-600/30 to-teal-600/20 dark:to-teal-700/30 backdrop-blur-md rounded-full border border-blue-400/30 dark:border-blue-500/40 p-1 shadow-lg">
                  <!-- Zoom Out -->
                  <button (click)="zoomOut(); $event.stopPropagation()" [disabled]="zoomLevel <= minZoom"
                    class="zoom-control bg-blue-500/20 dark:bg-blue-600/30 hover:bg-blue-400/40 dark:hover:bg-blue-500/50 text-white p-2 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group border border-blue-400/20 dark:border-blue-500/30 hover:border-blue-300/50 dark:hover:border-blue-400/50"
                    title="Zoom Out">
                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
                    </svg>
                  </button>

                  <!-- Zoom Level Indicator -->
                  <div
                    class="zoom-indicator bg-gradient-to-r from-blue-600/30 dark:from-blue-700/40 to-teal-600/30 dark:to-teal-700/40 text-white dark:text-gray-200 px-3 py-1 rounded-full text-xs font-medium min-w-[50px] text-center border border-blue-400/30 dark:border-blue-500/40 shadow-inner">
                    {{(zoomLevel * 100).toFixed(0)}}%
                  </div>

                  <!-- Zoom In -->
                  <button (click)="zoomIn(); $event.stopPropagation()" [disabled]="zoomLevel >= maxZoom"
                    class="zoom-control bg-blue-500/20 dark:bg-blue-600/30 hover:bg-blue-400/40 dark:hover:bg-blue-500/50 text-white p-2 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group border border-blue-400/20 dark:border-blue-500/30 hover:border-blue-300/50 dark:hover:border-blue-400/50"
                    title="Zoom In">
                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
                    </svg>
                  </button>

                  <!-- Reset Zoom -->
                  <button (click)="resetZoom(); $event.stopPropagation()"
                    [disabled]="zoomLevel === 1 && imageTransform.x === 0 && imageTransform.y === 0"
                    class="zoom-control bg-amber-500/20 dark:bg-amber-600/30 hover:bg-amber-400/40 dark:hover:bg-amber-500/50 text-white p-2 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group border border-amber-400/30 dark:border-amber-500/40 hover:border-amber-300/50 dark:hover:border-amber-400/50"
                    title="Reset Zoom & Position">
                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </button>
                </div>

                <!-- Preview Button -->
                <button (click)="openImagePreview(allImageAttachments[selectedImageIndex]); $event.stopPropagation()"
                  class="bg-gradient-to-r from-blue-500 dark:from-blue-600 to-blue-600 dark:to-blue-700 hover:from-blue-600 dark:hover:from-blue-700 hover:to-blue-700 dark:hover:to-blue-800 text-white px-4 py-2 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center space-x-2">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <span class="text-sm font-medium">Preview</span>
                </button>

                <!-- Download Button -->
                <button (click)="downloadImage(allImageAttachments[selectedImageIndex]); $event.stopPropagation()"
                  class="bg-gradient-to-r from-blue-500 dark:from-blue-600 to-blue-600 dark:to-blue-700 hover:from-blue-600 dark:hover:from-blue-700 hover:to-blue-700 dark:hover:to-blue-800 text-white px-4 py-2 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center space-x-2">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                  </svg>
                  <span class="text-sm font-medium">Download</span>
                </button>

                <!-- Close Button -->
                <button (click)="closeLightbox()"
                  class="bg-red-500/20 dark:bg-red-600/30 hover:bg-red-500/30 dark:hover:bg-red-600/40 backdrop-blur-md text-white p-3 rounded-full border border-red-400/30 dark:border-red-500/40 hover:border-red-400/50 dark:hover:border-red-500/50 transition-all duration-200 group">
                  <svg class="w-5 h-5 group-hover:rotate-90 transition-transform duration-200" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Image Container -->
            <div class="flex-1 flex items-center justify-center relative" (click)="$event.stopPropagation()"
              (mousemove)="onImageMouseMove($event)" (mouseup)="onImageMouseUp($event)"
              (mouseleave)="onImageMouseUp($event)">
              <!-- Navigation Buttons - Fixed Positioning -->
              <button *ngIf="allImageAttachments.length > 1" (click)="previousImage(); $event.stopPropagation()"
                class="lightbox-nav-btn fixed left-6 top-1/2 -translate-y-1/2 z-30 bg-white/10 dark:bg-gray-700/50 hover:bg-white/20 dark:hover:bg-gray-600/50 backdrop-blur-md text-white p-4 rounded-full border border-white/20 dark:border-gray-600/50 hover:border-white/30 dark:hover:border-gray-500/50 transition-all duration-200 hover:scale-110 group shadow-lg"
                title="Previous Image">
                <svg class="w-6 h-6 transition-transform duration-200" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>

              <button *ngIf="allImageAttachments.length > 1" (click)="nextImage(); $event.stopPropagation()"
                class="lightbox-nav-btn fixed right-6 top-1/2 -translate-y-1/2 z-30 bg-white/10 dark:bg-gray-700/50 hover:bg-white/20 dark:hover:bg-gray-600/50 backdrop-blur-md text-white p-4 rounded-full border border-white/20 dark:border-gray-600/50 hover:border-white/30 dark:hover:border-gray-500/50 transition-all duration-200 hover:scale-110 group shadow-lg"
                title="Next Image">
                <svg class="w-6 h-6 transition-transform duration-200" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>

              <!-- Main Image -->
              <div class="relative max-w-full max-h-full">
                <img *ngIf="allImageAttachments[selectedImageIndex]" [src]="allImageAttachments[selectedImageIndex].url"
                  [alt]="allImageAttachments[selectedImageIndex].name"
                  class="max-w-full max-h-full object-contain rounded-2xl shadow-2xl border border-white/10 dark:border-gray-600/20 animate-zoomIn cursor-move transition-transform duration-200 "
                  style="max-width: 90%; min-width: 90%; max-height: 90%; min-height: 90%;"
                  [style]="getImageTransformStyle()" (error)="onImageError($event)"
                  (mousedown)="onImageMouseDown($event)" (mousemove)="onImageMouseMove($event)"
                  (mouseup)="onImageMouseUp($event)" (wheel)="onImageWheel($event)"
                  (dragstart)="$event.preventDefault()">

                <!-- Loading Overlay for Image -->
                <div *ngIf="!allImageAttachments[selectedImageIndex]"
                  class="w-96 h-96 bg-gradient-to-br from-gray-800 dark:from-gray-900 to-gray-900 dark:to-gray-800 rounded-2xl flex items-center justify-center">
                  <div
                    class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 dark:border-blue-400 border-t-transparent">
                  </div>
                </div>
              </div>
            </div>

            <!-- Bottom Thumbnail Strip -->
            <div *ngIf="allImageAttachments.length > 1"
              class="absolute bottom-0 left-0 right-0 z-20 bg-gradient-to-t from-black dark:from-gray-900 via-black/50 dark:via-gray-800/50 to-transparent p-4 md:p-6">
              <div class="flex justify-center">
                <div
                  class="flex space-x-2 md:space-x-3 max-w-full overflow-x-auto scrollbar-hide bg-white/5 dark:bg-gray-700/50 backdrop-blur-md rounded-2xl p-2 md:p-3 border border-white/10 dark:border-gray-600/50">
                  <div *ngFor="let image of allImageAttachments; let i = index"
                    (click)="selectedImageIndex = i; $event.stopPropagation()"
                    [class]="'w-12 h-12 md:w-16 md:h-16 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200 transform hover:scale-110 ' +
                     (i === selectedImageIndex ? 'border-blue-400 dark:border-blue-300 shadow-lg shadow-blue-400/50 dark:shadow-blue-300/50' : 'border-white/20 dark:border-gray-600/50 hover:border-white/40 dark:hover:border-gray-500/50')">
                    <img [src]="image.url" [alt]="image.name" class="w-full h-full object-cover"
                      (error)="onImageError($event)">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Comparison Mode Controls -->
        <div *ngIf="isComparisonMode"
          class="mb-6 bg-gradient-to-r from-blue-50 via-teal-50 to-blue-50 rounded-2xl border border-blue-200 shadow-lg overflow-hidden">
          <!-- Header Section -->
          <div class="bg-gradient-to-r from-blue-600 to-teal-600 text-white px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                  </svg>
                </div>
                <div>
                  <h4 class="text-lg font-bold !text-gray-800">Medical Image Comparison Mode</h4>
                  <p class="text-blue-100 text-sm">
                    Compare previous and current medical reports side by side
                  </p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <div class="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                  <span class="text-sm font-semibold">
                    {{selectedImagesForComparison.length}}/{{maxComparisonImages}}
                  </span>
                  <span class="text-xs text-blue-100 ml-1">Selected</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Content Section -->
          <div class="p-6">
            <!-- Selection Display -->
            <div *ngIf="selectedImagesForComparison.length > 0" class="mb-6">
              <h5 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Selected Images for Comparison
              </h5>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div *ngFor="let image of selectedImagesForComparison; let i = index"
                  class="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
                  <div class="flex items-center p-4">
                    <!-- Number Badge -->
                    <div
                      [class]="'w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm mr-4 ' +
                             (i === 0 ? 'bg-gradient-to-r from-blue-500 to-blue-600' : 'bg-gradient-to-r from-blue-500 to-blue-600')">
                      {{i + 1}}
                    </div>

                    <!-- Image Thumbnail -->
                    <div class="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 mr-4 flex-shrink-0">
                      <img [src]="image.url" [alt]="image.name" class="w-full h-full object-cover">
                    </div>

                    <!-- Image Info -->
                    <div class="flex-1 min-w-0">
                      <h6 class="text-sm font-semibold text-gray-900 truncate mb-1">{{image.name}}</h6>
                      <div class="flex items-center space-x-3 text-xs text-gray-500">
                        <span class="flex items-center">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {{formatDateShort(image.uploadDate)}}
                        </span>
                        <span class="flex items-center">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                          </svg>
                          Project #{{image.projectId}}
                        </span>
                        <span *ngIf="image.size" class="flex items-center">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                          </svg>
                          {{image.size}}
                        </span>
                      </div>
                      <p class="text-xs text-gray-600 mt-1 truncate">{{image.projectTitle}}</p>
                    </div>

                    <!-- Remove Button -->
                    <button (click)="selectImageForComparison(image, $event)"
                      class="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <button *ngIf="selectedImagesForComparison.length === 2" (click)="openComparisonView()"
                  class="group relative px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 overflow-hidden">
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300">
                  </div>
                  <div class="relative flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0v10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2z" />
                    </svg>
                    Open Comparison Dialog
                  </div>
                </button>

                <button *ngIf="selectedImagesForComparison.length > 0" (click)="clearComparisonSelection()"
                  class="px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-300 flex items-center">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  Clear Selection
                </button>
              </div>

              <button (click)="toggleComparisonMode()"
                class="px-4 py-3 bg-red-100 hover:bg-red-200 text-red-700 rounded-xl font-medium transition-all duration-300 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
                Exit Comparison Mode
              </button>
            </div>

            <!-- Instructions -->
            <div *ngIf="selectedImagesForComparison.length === 0"
              class="mt-6 p-4 bg-gradient-to-r from-blue-50 to-teal-50 rounded-xl border border-blue-200">
              <div class="flex items-start space-x-3">
                <div class="p-2 bg-blue-100 rounded-lg">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h6 class="font-semibold text-blue-900 mb-1">How to Compare Medical Images</h6>
                  <ul class="text-sm text-blue-700 space-y-1">
                    <li>• Click on any image to select it for comparison</li>
                    <li>• Select exactly 2 images to enable comparison mode</li>
                    <li>• First selection will be labeled as "Previous Report"</li>

                    <li>• Second selection will be labeled as "Current Report"</li>
                    <li>• Use the comparison view to analyze changes over time</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Progress Indicator -->
            <div *ngIf="selectedImagesForComparison.length > 0 && selectedImagesForComparison.length < 2"
              class="mt-4 p-3 bg-blue-50 rounded-xl border border-yellow-200">
              <div class="flex items-center">
                <div class="flex-1">
                  <div class="flex items-center justify-between text-sm mb-2">
                    <span class="font-medium text-blue-800">Selection Progress</span>
                    <span class="text-blue-600">{{selectedImagesForComparison.length}}/2 images selected</span>
                  </div>
                  <div class="w-full bg-bkue-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      [style.width.%]="(selectedImagesForComparison.length / maxComparisonImages) * 100"></div>
                  </div>
                </div>
                <div class="ml-4 text-blue-600">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">

                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-blue-900 dark:text-blue-100 mb-2">Ready to Compare!</h3>
                <p class="text-sm text-blue-700 dark:text-blue-300 mb-4">
                  You have selected <span class="font-semibold">{{selectedImagesForComparison.length}} images</span> for
                  side-by-side comparison analysis
                </p>

                <!-- Selected Images Preview -->
                <div class="flex items-center justify-center space-x-4 mb-6">
                  <div *ngFor="let image of selectedImagesForComparison; let i = index"
                    class="flex items-center space-x-2 bg-white dark:bg-gray-800 rounded-lg p-2 shadow-md">
                    <div
                      class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {{i + 1}}
                    </div>
                    <span
                      class="text-sm font-medium text-gray-700 dark:text-gray-300 max-w-24 truncate">{{image.name}}</span>
                  </div>
                </div>
              </div>

              <!-- Large Prominent Button -->
              <button (click)="openComparisonView()"
                class="group bg-gradient-to-r from-blue-500 via-emerald-500 to-teal-500 hover:from-blue-600 hover:via-emerald-600 hover:to-teal-600 text-white px-6 py-4 rounded-2xl shadow-2xl hover:shadow-blue-500/25 transform hover:scale-105 transition-all duration-300 border-2 border-white/20 backdrop-blur-sm"
                style="position: relative !important; z-index: 1000 !important; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1); pointer-events: auto !important; isolation: isolate; will-change: transform; transform: translateZ(0);">
                <div
                  class="absolute inset-0 bg-gradient-to-r from-blue-700 via-emerald-700 to-teal-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                </div>
                <div class="relative flex items-center">
                  <svg class="w-6 h-6 mr-3 group-hover:rotate-12 transition-transform duration-300" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0v10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2z" />
                  </svg>
                  Open Comparison Dialog
                  <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </div>
              </button>

              <!-- Test Button for Debugging -->
              <button (click)="isComparisonViewOpen = true"
                class="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium">
                🔧 Test Open Dialog (Debug)
              </button>

              <!-- Secondary Actions -->
              <div class="mt-4 flex items-center justify-center space-x-3">
                <button (click)="clearComparisonSelection()"
                  class="px-4 py-2 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-all duration-200 border border-gray-200 dark:border-gray-600 shadow-sm">
                  <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Reset Selection
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Image Comparison View Modal -->
      <div *ngIf="isComparisonViewOpen"
        class="fixed inset-0 z-50 bg-gradient-to-br from-black/95 via-slate-900/95 to-black/95 dark:from-black/95 dark:via-slate-900/95 dark:to-black/95 backdrop-blur-xl flex items-center justify-center p-4 md:p-6"
        (click)="closeComparisonView()" (keydown.escape)="closeComparisonView()">

        <!-- Enhanced Comparison Container with Scrolling -->
        <div
          class="bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-slate-800 dark:via-slate-900 dark:to-slate-800 rounded-3xl w-full max-w-7xl max-h-[95vh] overflow-hidden shadow-2xl border border-gray-200/50 dark:border-gray-700/50 backdrop-blur-sm transform hover:scale-[1.01] transition-all duration-500 flex flex-col"
          (click)="$event.stopPropagation()">

          <!-- Enhanced Header with Modern Design -->
          <div
            class="bg-gradient-to-r from-blue-50  to-indigo-50 dark:from-gray-800 dark:to-gray-800 border-blue-100 border shadow-md rounded-lg dark:border-gray-700 text-white px-4 md:px-8 py-4 md:py-6 relative overflow-hidden comparison-modal-header">
            <!-- Background Pattern -->
            <!-- <div class="absolute inset-0 opacity-10">
                <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                  <defs>
                    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5" />
                    </pattern>
                  </defs>
                  <rect width="100" height="100" fill="url(#grid)" />
                </svg>
              </div> -->

            <!-- Animated Background Circles -->
            <div
              class="absolute top-0 left-0 w-32 h-32 bg-white/5 rounded-full -translate-x-16 -translate-y-16 animate-pulse">
            </div>
            <div
              class="absolute bottom-0 right-0 w-40 h-40 bg-white/5 rounded-full translate-x-20 translate-y-20 animate-pulse delay-1000">
            </div>

            <!-- Scroll Indicator -->
            <div
              class="absolute top-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-white/30 rounded-full mt-2 animate-pulse">
            </div>

            <div
              class="flex flex-col md:flex-row items-start md:items-center justify-between relative z-10 space-y-4 md:space-y-0">
              <div class="flex items-center space-x-3 md:space-x-4">
                <!-- <div
                    class="w-10 h-10 md:w-12 md:h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                    </svg>
                  </div> -->

                <div>
                  <h2 class="text-lg md:text-2xl font-bold mb-1 flex flex-col md:flex-row md:items-center">
                    <span class="dark:text-white text-gray-800">Medical Image Comparison</span>
                    <span
                      class="mt-1 md:mt-0 md:ml-3 text-gray-600 text-xs bg-gradient-to-r from-blue-300 to-teal-300 dark:text-blue-900 px-2 md:px-3 py-1 rounded-full font-semibold">
                      Professional Analysis
                    </span>
                  </h2>
                  <p class="dark:text-teal-100 text-gray-600 text-xs md:text-sm">Advanced side-by-side comparison for enhanced medical
                    diagnosis</p>
                </div>
              </div>

              <div class="flex flex-wrap items-center gap-2 md:gap-3">
                <!-- Zoom Sync Button -->
                <button (click)="syncComparisonZoom()"
                  class="flex items-center px-3 md:px-4 py-2 md:py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-lg md:rounded-xl backdrop-blur-sm transition-all duration-300 border border-indigo-400/30 shadow-lg transform hover:scale-105 text-white text-sm">
                  <svg class="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span class="hidden sm:inline">Sync Zoom</span>
                  <span class="sm:hidden">Sync</span>
                </button>

                <!-- Reset All Zoom Button -->
                <button (click)="resetAllComparisonZoom()"
                  class="flex items-center px-3 md:px-4 py-2 md:py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-lg md:rounded-xl backdrop-blur-sm transition-all duration-300 border border-blue-400/30 shadow-lg transform hover:scale-105 text-white text-sm">
                  <svg class="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span class="hidden sm:inline">Reset All</span>
                  <span class="sm:hidden">Reset</span>
                </button>

                <!-- Enhanced Action Buttons -->
                <button (click)="downloadComparisonImages()"
                  class="flex items-center px-3 md:px-5 py-2 md:py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-lg md:rounded-xl backdrop-blur-sm transition-all duration-300 border border-blue-400/30 shadow-lg transform hover:scale-105 text-sm">
                  <svg class="w-3 h-3 md:w-5 md:h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                  </svg>
                  <span class="hidden sm:inline">Export Report</span>
                  <span class="sm:hidden">Export</span>
                </button>

                <button (click)="clearComparisonSelection()"
                  class="flex items-center px-3 md:px-4 py-2 md:py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-lg md:rounded-xl backdrop-blur-sm transition-all duration-300 border border-blue-400/30 shadow-lg transform hover:scale-105 text-white text-sm">
                  <svg class="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <span class="hidden sm:inline">Clear</span>
                </button>

                <button (click)="closeComparisonView()"
                  class="flex items-center px-3 md:px-4 py-2 md:py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-lg md:rounded-xl backdrop-blur-sm transition-all duration-300 border border-blue-400/30 shadow-lg transform hover:scale-105 text-white text-sm">
                  <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Enhanced Content with Better Spacing and Design + Scrolling -->
          <div
            class="flex-1 overflow-y-auto overflow-x-hidden p-4 md:p-8 bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-slate-800 dark:via-slate-900 dark:to-slate-800 scrollbar-thin scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-200 dark:scrollbar-track-gray-800">



            <!-- Enhanced Image Comparison Grid -->
            <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-8 mb-6 md:mb-8 comparison-grid-responsive">

              <!-- Enhanced Left Image Card (Previous) -->
              <div *ngIf="selectedImagesForComparison[0]" class="group relative">
                <div
                  class="bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800 rounded-2xl overflow-hidden shadow-2xl border border-gray-200/50 dark:border-gray-700/50 transform hover:scale-[1.02] transition-all duration-500 hover:shadow-blue-500/20">

                  <!-- Enhanced Header with Gradient -->
                  <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-5 relative overflow-hidden">
                    <!-- Decorative Elements -->
                    <div class="absolute inset-0 bg-white/5 transform -skew-y-1"></div>
                    <div
                      class="absolute top-0 right-0 w-24 h-24 bg-white/5 rounded-full transform translate-x-12 -translate-y-12">
                    </div>

                    <div class="relative z-10 flex items-center justify-between">
                      <div class="flex items-center space-x-4">
                        <div
                          class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-lg">
                          <span class="text-xl font-bold">1</span>
                        </div>
                        <div>
                          <h3 class="font-bold text-lg">Previous Report</h3>
                          <p class="text-blue-100 text-sm flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Baseline Image
                          </p>
                        </div>
                      </div>
                      <div class="text-right">
                        <span
                          class="text-xs bg-white/20 px-3 py-1.5 rounded-full backdrop-blur-sm border border-white/20 font-medium">
                          {{formatDateShort(selectedImagesForComparison[0].uploadDate)}}
                        </span>
                        <p class="text-blue-100 text-sm mt-2 max-w-xs truncate font-medium">
                          {{selectedImagesForComparison[0].name}}</p>
                      </div>
                    </div>
                  </div>

                  <!-- Enhanced Image Container with Hover Effects -->
                  <div
                    class="relative aspect-square md:aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 group-hover:from-blue-50 group-hover:to-blue-100 dark:group-hover:from-blue-900/20 dark:group-hover:to-blue-800/20 transition-all duration-500 overflow-hidden min-h-[250px] md:min-h-[300px]">

                    <!-- Zoom Controls for Image 1 -->
                    <div
                      class="absolute top-4 right-4 z-20 flex flex-col space-y-2 opacity-90 hover:opacity-100 transition-all duration-200">
                      <!-- Zoom Level Indicator -->
                      <div *ngIf="comparisonZoom.image1.zoomLevel !== 1"
                        class="bg-gradient-to-r from-blue-500/20 to-teal-600/20 dark:from-blue-400/20 dark:to-teal-500/20 backdrop-blur-md rounded-full px-3 py-1 border border-blue-400/30 dark:border-blue-500/30 shadow-lg">
                        <span class="text-blue-900 dark:text-blue-100 text-xs font-bold">
                          {{(comparisonZoom.image1.zoomLevel * 100).toFixed(0)}}%
                        </span>
                      </div>

                      <!-- Zoom Controls -->
                      <div
                        class="flex flex-col space-y-1 bg-gradient-to-r from-blue-500/20 to-teal-600/20 dark:from-blue-400/20 dark:to-teal-500/20 backdrop-blur-md rounded-xl border border-blue-400/30 dark:border-blue-500/30 p-1 shadow-lg">
                        <!-- Zoom In -->
                        <button (click)="zoomInComparison(1); $event.stopPropagation()"
                          [disabled]="comparisonZoom.image1.zoomLevel >= maxZoom"
                          class="zoom-control bg-blue-500/20 hover:bg-blue-400/40 dark:bg-blue-400/20 dark:hover:bg-blue-300/40 text-blue-800 dark:text-blue-100 p-2 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group border border-blue-400/20 dark:border-blue-500/20 hover:border-blue-300/50 dark:hover:border-blue-400/50"
                          title="Zoom In">
                          <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
                          </svg>
                        </button>

                        <!-- Zoom Out -->
                        <button (click)="zoomOutComparison(1); $event.stopPropagation()"
                          [disabled]="comparisonZoom.image1.zoomLevel <= minZoom"
                          class="zoom-control bg-blue-500/20 hover:bg-blue-400/40 text-blue-800 p-2 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group border border-blue-400/20 hover:border-blue-300/50"
                          title="Zoom Out">
                          <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
                          </svg>
                        </button>

                        <!-- Reset Zoom -->
                        <button (click)="resetZoomComparison(1); $event.stopPropagation()"
                          [disabled]="comparisonZoom.image1.zoomLevel === 1 && comparisonZoom.image1.transform.x === 0 && comparisonZoom.image1.transform.y === 0"
                          class="zoom-control bg-amber-500/20 hover:bg-amber-400/40 text-amber-800 p-2 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group border border-amber-400/30 hover:border-amber-300/50"
                          title="Reset Zoom & Position">
                          <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <!-- Pan Instruction -->
                    <div *ngIf="comparisonZoom.image1.zoomLevel > 1"
                      class="absolute bottom-4 left-4 z-20 bg-gradient-to-r from-blue-500/20 to-teal-600/20 backdrop-blur-md rounded-lg px-3 py-2 border border-blue-400/30 shadow-lg">
                      <span class="text-blue-900 text-xs font-medium">Click & drag to pan</span>
                    </div>

                    <!-- Loading State -->
                    <div
                      class="absolute inset-0 flex items-center justify-center z-10 bg-gradient-to-br from-gray-200 to-gray-300"
                      #imageLoader1>
                      <div class="flex flex-col items-center space-y-3">
                        <div class="animate-spin rounded-full h-8 w-8 border-4 border-blue-500 border-t-transparent">
                        </div>
                        <span class="text-xs text-gray-600 font-medium">Loading image...</span>
                      </div>
                    </div>

                    <img [src]="selectedImagesForComparison[0].url" [alt]="selectedImagesForComparison[0].name"
                      class="w-full h-full object-contain p-6 transition-all duration-500 cursor-move"
                      [style]="getComparisonImageTransformStyle(1)" (load)="imageLoader1.style.display='none'"
                      (error)="onImageError($event); imageLoader1.style.display='none'"
                      (mousedown)="onComparisonImageMouseDown($event, 1)"
                      (mousemove)="onComparisonImageMouseMove($event, 1)"
                      (mouseup)="onComparisonImageMouseUp($event, 1)" (mouseleave)="onComparisonImageMouseUp($event, 1)"
                      (wheel)="onComparisonImageWheel($event, 1)" (dragstart)="$event.preventDefault()">

                    <!-- Enhanced Floating Action Buttons -->
                    <div
                      class="absolute bottom-6 right-6 flex space-x-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                      <button (click)="zoomInComparison(1); $event.stopPropagation()"
                        [disabled]="comparisonZoom.image1.zoomLevel >= maxZoom"
                        class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl shadow-xl transition-all duration-300 transform hover:scale-110 backdrop-blur-sm border border-blue-400/30 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Zoom In">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
                        </svg>
                      </button>
                      <button (click)="openImagePreview(selectedImagesForComparison[0])"
                        class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl shadow-xl transition-all duration-300 transform hover:scale-110 backdrop-blur-sm border border-blue-400/30">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      <button (click)="downloadImage(selectedImagesForComparison[0])"
                        class="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-xl shadow-xl transition-all duration-300 transform hover:scale-110 backdrop-blur-sm border border-blue-400/30">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                        </svg>
                      </button>
                    </div>

                    <!-- Quality Indicator Badge -->
                    <div
                      class="absolute top-6 left-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                      <div
                        class="bg-white/95 dark:bg-gray-900  backdrop-blur-sm rounded-xl px-4 py-2 shadow-xl border border-gray-200/50">
                        <div class="flex items-center space-x-2">
                          <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          <span class="text-xs font-semibold text-gray-700 dark:text-white">High Quality</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Enhanced Details Section -->
                  <div
                    class="p-6 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 border-t border-gray-200/50">
                    <div class="grid grid-cols-2 gap-6">
                      <div class="flex items-center space-x-3">
                        <div
                          class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center shadow-sm">
                          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                          </svg>
                        </div>
                        <div>
                          <span class="text-xs text-gray-500 uppercase tracking-wide font-semibold">Project ID</span>
                          <p class="font-bold text-gray-900 dark:text-white text-lg">
                            #{{selectedImagesForComparison[0].projectId}}</p>
                        </div>
                      </div>
                      <div class="flex items-center space-x-3">
                        <div
                          class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center shadow-sm">
                          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <span class="text-xs text-gray-500 uppercase tracking-wide font-semibold">Upload Date</span>
                          <p class="font-bold text-gray-900 dark:text-white text-sm">
                            {{formatDate(selectedImagesForComparison[0].uploadDate)}}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Enhanced Right Image Card (Current) -->
              <div *ngIf="selectedImagesForComparison[1]" class="group relative">
                <div
                  class="bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800 rounded-2xl overflow-hidden shadow-2xl border border-gray-200/50 dark:border-gray-700/50 transform hover:scale-[1.02] transition-all duration-500 hover:shadow-blue-500/20">

                  <!-- Enhanced Header with Gradient -->
                  <div
                    class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-5 relative overflow-hidden">
                    <!-- Decorative Elements -->
                    <div class="absolute inset-0 bg-white/5 transform skew-y-1"></div>
                    <div
                      class="absolute top-0 left-0 w-24 h-24 bg-white/5 rounded-full transform -translate-x-12 -translate-y-12">
                    </div>

                    <div class="relative z-10 flex items-center justify-between">
                      <div class="flex items-center space-x-4">
                        <div
                          class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-lg">
                          <span class="text-xl font-bold">2</span>
                        </div>
                        <div>
                          <h3 class="font-bold text-lg">Current Report</h3>
                          <p class="text-blue-100 text-sm flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7" />
                            </svg>
                            Latest Image
                          </p>
                        </div>
                      </div>
                      <div class="text-right">
                        <span
                          class="text-xs bg-white/20 px-3 py-1.5 rounded-full backdrop-blur-sm border border-white/20 font-medium">
                          {{formatDateShort(selectedImagesForComparison[1].uploadDate)}}
                        </span>
                        <p class="text-blue-100 text-sm mt-2 max-w-xs truncate font-medium">
                          {{selectedImagesForComparison[1].name}}</p>
                      </div>
                    </div>
                  </div>

                  <!-- Enhanced Image Container with Hover Effects -->
                  <div
                    class="relative aspect-square md:aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 group-hover:from-blue-50 group-hover:to-blue-100 dark:group-hover:from-blue-900/20 dark:group-hover:to-blue-800/20 transition-all duration-500 overflow-hidden min-h-[250px] md:min-h-[300px]">

                    <!-- Zoom Controls for Image 2 -->
                    <div
                      class="absolute top-4 right-4 z-20 flex flex-col space-y-2 opacity-90 hover:opacity-100 transition-all duration-200">
                      <!-- Zoom Level Indicator -->
                      <div *ngIf="comparisonZoom.image2.zoomLevel !== 1"
                        class="bg-gradient-to-r from-blue-500/20 to-emerald-600/20 dark:from-blue-400/20 dark:to-emerald-500/20 backdrop-blur-md rounded-full px-3 py-1 border border-blue-400/30 dark:border-blue-500/30 shadow-lg">
                        <span class="text-blue-900 dark:text-blue-100 text-xs font-bold">
                          {{(comparisonZoom.image2.zoomLevel * 100).toFixed(0)}}%
                        </span>
                      </div>

                      <!-- Zoom Controls -->
                      <div
                        class="flex flex-col space-y-1 bg-gradient-to-r from-blue-500/20 to-emerald-600/20 dark:from-blue-400/20 dark:to-emerald-500/20 backdrop-blur-md rounded-xl border border-blue-400/30 dark:border-blue-500/30 p-1 shadow-lg">
                        <!-- Zoom In -->
                        <button (click)="zoomInComparison(2); $event.stopPropagation()"
                          [disabled]="comparisonZoom.image2.zoomLevel >= maxZoom"
                          class="zoom-control bg-blue-500/20 hover:bg-blue-400/40 dark:bg-blue-400/20 dark:hover:bg-blue-300/40 text-blue-800 dark:text-blue-100 p-2 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group border border-blue-400/20 dark:border-blue-500/20 hover:border-blue-300/50 dark:hover:border-blue-400/50"
                          title="Zoom In">
                          <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
                          </svg>
                        </button>

                        <!-- Zoom Out -->
                        <button (click)="zoomOutComparison(2); $event.stopPropagation()"
                          [disabled]="comparisonZoom.image2.zoomLevel <= minZoom"
                          class="zoom-control bg-blue-500/20 hover:bg-blue-400/40 dark:bg-blue-400/20 dark:hover:bg-blue-300/40 text-blue-800 dark:text-blue-100 p-2 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group border border-blue-400/20 dark:border-blue-500/20 hover:border-blue-300/50 dark:hover:border-blue-400/50"
                          title="Zoom Out">
                          <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
                          </svg>
                        </button>

                        <!-- Reset Zoom -->
                        <button (click)="resetZoomComparison(2); $event.stopPropagation()"
                          [disabled]="comparisonZoom.image2.zoomLevel === 1 && comparisonZoom.image2.transform.x === 0 && comparisonZoom.image2.transform.y === 0"
                          class="zoom-control bg-amber-500/20 hover:bg-amber-400/40 text-amber-800 p-2 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group border border-amber-400/30 hover:border-amber-300/50"
                          title="Reset Zoom & Position">
                          <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <!-- Pan Instruction -->
                    <div *ngIf="comparisonZoom.image2.zoomLevel > 1"
                      class="absolute bottom-4 left-4 z-20 bg-gradient-to-r from-blue-500/20 to-emerald-600/20 backdrop-blur-md rounded-lg px-3 py-2 border border-blue-400/30 shadow-lg">
                      <span class="text-blue-900 text-xs font-medium">Click & drag to pan</span>
                    </div>

                    <!-- Loading State -->
                    <div
                      class="absolute inset-0 flex items-center justify-center z-10 bg-gradient-to-br from-gray-200 to-gray-300"
                      #imageLoader2>
                      <div class="flex flex-col items-center space-y-3">
                        <div class="animate-spin rounded-full h-8 w-8 border-4 border-blue-500 border-t-transparent">
                        </div>
                        <span class="text-xs text-gray-600 font-medium">Loading image...</span>
                      </div>
                    </div>

                    <img [src]="selectedImagesForComparison[1].url" [alt]="selectedImagesForComparison[1].name"
                      class="w-full h-full object-contain p-6 transition-all duration-500 cursor-move"
                      [style]="getComparisonImageTransformStyle(2)" (load)="imageLoader2.style.display='none'"
                      (error)="onImageError($event); imageLoader2.style.display='none'"
                      (mousedown)="onComparisonImageMouseDown($event, 2)"
                      (mousemove)="onComparisonImageMouseMove($event, 2)"
                      (mouseup)="onComparisonImageMouseUp($event, 2)" (mouseleave)="onComparisonImageMouseUp($event, 2)"
                      (wheel)="onComparisonImageWheel($event, 2)" (dragstart)="$event.preventDefault()">

                    <!-- Enhanced Floating Action Buttons -->
                    <div
                      class="absolute bottom-6 right-6 flex space-x-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                      <button (click)="zoomInComparison(2); $event.stopPropagation()"
                        [disabled]="comparisonZoom.image2.zoomLevel >= maxZoom"
                        class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl shadow-xl transition-all duration-300 transform hover:scale-110 backdrop-blur-sm border border-blue-400/30 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Zoom In">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
                        </svg>
                      </button>
                      <button (click)="openImagePreview(selectedImagesForComparison[1])"
                        class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl shadow-xl transition-all duration-300 transform hover:scale-110 backdrop-blur-sm border border-blue-400/30">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                      <button (click)="downloadImage(selectedImagesForComparison[1])"
                        class="p-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl shadow-xl transition-all duration-300 transform hover:scale-110 backdrop-blur-sm border border-blue-400/30">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                        </svg>
                      </button>
                    </div>

                    <!-- Quality Indicator Badge -->
                    <div
                      class="absolute top-6 left-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                      <div
                        class="bg-white/95 dark:bg-gray-900 dark:border-gray-700 backdrop-blur-sm rounded-xl px-4 py-2 shadow-xl border border-gray-200/50">
                        <div class="flex items-center space-x-2">
                          <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          <span class="text-xs font-semibold text-gray-700">High Quality</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Enhanced Details Section -->
                  <div
                    class="p-6 bg-gradient-to-r from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 border-t border-gray-200/50">
                    <div class="grid grid-cols-2 gap-6">
                      <div class="flex items-center space-x-3">
                        <div
                          class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center shadow-sm">
                          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                          </svg>
                        </div>
                        <div>
                          <span class="text-xs text-gray-500 uppercase tracking-wide font-semibold">Project ID</span>
                          <p class="font-bold text-gray-900   dark:text-white text-lg">
                            #{{selectedImagesForComparison[1].projectId}}</p>
                        </div>
                      </div>
                      <div class="flex items-center space-x-3">
                        <div
                          class="w-10 h-10 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center shadow-sm">
                          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <div>
                          <span class="text-xs text-gray-500 uppercase tracking-wide font-semibold">Upload Date</span>
                          <p class="font-bold text-gray-900  dark:text-white text-sm">
                            {{formatDate(selectedImagesForComparison[1].uploadDate)}}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>



          </div>
        </div>

      </div>
    </div>
  </div>
</div>

<!-- Floating "Ready to Compare" Button - Always Visible (Outside all containers) -->
<div *ngIf="isComparisonMode && selectedImagesForComparison.length === 2" class="comparison-floating-button"
  style="filter: drop-shadow(0 0 20px rgba(34, 197, 94, 0.4));">
  <button (click)="openComparisonView()"
    class="ready-to-compare-button group bg-gradient-to-r from-blue-500  to-teal-500 hover:from-blue-600  hover:to-teal-600 text-white px-6 py-4 rounded-2xl shadow-2xl border-2 border-white/20 backdrop-blur-sm"
    style="box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);"
    [attr.aria-label]="'Compare ' + selectedImagesForComparison.length + ' selected images'">
    <div class="flex items-center space-x-3">
      <div class="relative">
        <svg class="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor"
          viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0v10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2z" />
        </svg>
        <!-- Pulse indicator -->
        <div class="absolute -top-1 -right-1 w-3 h-3 bg-blue-900 rounded-full animate-pulse"></div>
      </div>
      <div class="text-left">
        <div class="font-bold text-sm">Ready to Compare!</div>
        <div class="text-xs opacity-90">Click to open dialog</div>
      </div>
    </div>
    <!-- Tooltip -->
    <div
      class="absolute bottom-full left-0 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none"
      style="z-index: 9999999 !important;">
      Open Comparison Dialog
      <div
        class="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900">
      </div>
    </div>
  </button>
</div>
