/* EditorJS Styles */
.codex-editor {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
  padding: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.codex-editor:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.ce-block__content {
  max-width: 100% !important;
  margin: 0;
}

.ce-toolbar__content {
  max-width: 100% !important;
}

.ce-toolbar__actions {
  right: 0;
}

/* Style for the placeholder */
.ce-paragraph[data-placeholder]:empty::before {
  color: #9ca3af;
}

/* Style for toolbar buttons */
.ce-toolbar__plus,
.ce-toolbar__settings-btn {
  color: #4b5563;
  background: #f3f4f6;
}

.ce-toolbar__plus:hover,
.ce-toolbar__settings-btn:hover {
  background-color: #e5e7eb;
}

/* Inline toolbar style */
.ce-inline-toolbar {
  border: 1px solid #e5e7eb;
  box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);
}

.ce-inline-tool {
  color: #4b5563;
}

.ce-inline-tool:hover {
  background: #e5e7eb;
}

.ce-inline-tool--active {
  color: #2563eb;
}

/* Block tunes */
.ce-block-settings {
  border: 1px solid #e5e7eb;
  box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);
}

.ce-block-settings__item:hover {
  background: #e5e7eb;
}

/* List styles */
.cdx-list {
  padding-left: 30px !important;
  margin: 0;
}

.cdx-list__item {
  padding: 5px 0;
}

/* Header styles */
.ce-header {
  padding: 0.5em 0;
}
