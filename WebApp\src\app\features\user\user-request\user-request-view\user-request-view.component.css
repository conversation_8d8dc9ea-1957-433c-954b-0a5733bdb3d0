/* Card animations */
.rounded-xl {
  animation: fadeIn 0.4s ease-out forwards;
}

/* Animation for staggered card appearance */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add staggered delay to cards */
.rounded-xl:nth-child(1) {
  animation-delay: 0.1s;
}

.rounded-xl:nth-child(2) {
  animation-delay: 0.2s;
}

.rounded-xl:nth-child(3) {
  animation-delay: 0.3s;
}

/* Timeline styling */
.border-l {
  position: relative;
}

.border-l::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -1px;
  width: 1px;
  height: 100%;
  background: linear-gradient(to bottom, #e5e7eb 70%, transparent);
}

/* Card hover effects */
.bg-white {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}


/* Patient information card styles */
.rounded-lg {
  animation: fadeIn 0.4s ease-out forwards;
  transition: all 0.3s ease;
}

/* Patient information content transition */
.rounded-lg .grid {
  transition: opacity 0.3s ease;
}

/* Status badge animation */
.rounded-full {
  transition: all 0.3s ease;
}

/* Modal dialog animations */
.animate-fadeIn {
  animation: modalFadeIn 0.3s ease-out forwards;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-scaleUp {
  animation: scaleUp 0.3s ease-out forwards;
}

@keyframes scaleUp {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Form field focus effects */
input:focus, select:focus, textarea:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  transition: box-shadow 0.2s ease;
}

/* Button hover animation */
button {
  transition: all 0.2s ease;
}

/* Modal backdrop blur animation */
.backdrop-blur-sm {
  animation: blurIn 0.3s forwards;
}

@keyframes blurIn {
  from {
    backdrop-filter: blur(0);
  }
  to {
    backdrop-filter: blur(4px);
  }
}

/* Chat button pulse animation */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Chat message animation */
.mb-4:last-child {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Chat panel shadow effect */
[class*="translate-x-0"] {
  box-shadow: -5px 0 25px rgba(0, 0, 0, 0.1);
}

/* Chat message scroll area styling */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(241, 245, 249, 0.5);
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 10px;
}

/* Message bubble animations */
.bg-gradient-to-r, .bg-white.border {
  animation: messageFadeIn 0.3s ease-out forwards;
  transform-origin: top left;
}

@keyframes messageFadeIn {
  from {
    opacity: 0;
    transform: translateY(5px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Chat notification badge pulse */
.w-5.h-5.rounded-full {
  animation: badgePulse 1.5s infinite;
}

@keyframes badgePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* Fix chat button position */
#fixed-chat-button-container {
  position: fixed !important;
  right: 1rem !important;
  bottom: 1rem !important;
  z-index: 9999 !important;
  pointer-events: none !important;
  width: 3.5rem !important; /* 56px - same as w-14 */
  height: 3.5rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

#fixed-chat-button-container button {
  pointer-events: auto !important;
  width: 100% !important;
  height: 100% !important;
}

/* Fix chat panel position */
.chat-panel {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  height: 100vh !important; /* Force height to viewport height */
  max-height: 100vh !important;
  overflow: hidden !important;
}

/* Make sure chat content is scrollable while header and input stay fixed */
.chat-panel .h-full {
  height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
}

.chat-panel .overflow-y-auto {
  flex-grow: 1 !important;
  overflow-y: auto !important;
  position: relative !important;
  height: auto !important; /* This allows the content to scroll */
}

/* Chat panel container styling */
.chat-panel-container {
  height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
  width: 100% !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Chat messages container specific styling */
.chat-messages-container {
  overflow-y: auto !important;
  flex: 1 1 auto !important;
  min-height: 0 !important; /* This is important for flexbox scrolling */
}

/* Add styles to fix position of headers and footers */
.sticky {
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.bottom-0 {
  bottom: 0 !important;
}

/* Fix z-index issues */
.z-high {
  z-index: 1000 !important;
}

/* Ensure body doesn't overflow when chat is open */
body.chat-open {
  overflow: hidden !important;
}

/* Image loading animation */
.image-loading.hidden {
  display: none;
}

.image-loading {
  background-color: rgba(245, 247, 250, 0.8);
  z-index: 10;
  transition: opacity 0.3s ease;
}

/* Add a pulse animation for the loading indicator */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Loading Spinner */
.spinner {
  display: inline-block;
  position: relative;
  width: 80px;
  height: 13px;
}

.spinner > div {
  width: 13px;
  height: 13px;
  background-color: #4f46e5;
  border-radius: 50%;
  display: inline-block;
  animation: bounce 1.4s infinite ease-in-out both;
}

.spinner .bounce1 {
  animation-delay: -0.32s;
}

.spinner .bounce2 {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}

/* Animation for page transitions */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-in forwards;
}

/* Snackbar styles - Updated with !important for better visibility */
:host ::ng-deep .success-snackbar {
  --mdc-snackbar-container-color: #10b981 !important;
  --mdc-snackbar-supporting-text-color: #ffffff !important;
  --mat-snack-bar-button-color: #ffffff !important;
}

:host ::ng-deep .error-snackbar {
  --mdc-snackbar-container-color: #ef4444 !important;
  --mdc-snackbar-supporting-text-color: #ffffff !important;
  --mat-snack-bar-button-color: #ffffff !important;
}

:host ::ng-deep .success-snackbar .mat-mdc-snack-bar-container {
  background-color: #10b981 !important;
  color: #ffffff !important;
  z-index: 9999 !important;
}

:host ::ng-deep .error-snackbar .mat-mdc-snack-bar-container {
  background-color: #ef4444 !important;
  color: #ffffff !important;
  z-index: 9999 !important;
}

:host ::ng-deep .success-snackbar .mat-mdc-button {
  color: #ffffff !important;
}

:host ::ng-deep .error-snackbar .mat-mdc-button {
  color: #ffffff !important;
}

/* Global snackbar container positioning */
:host ::ng-deep .mat-mdc-snack-bar-container {
  min-width: 300px !important;
  max-width: 500px !important;
}

/* Ensure snackbar is always visible above other elements */
:host ::ng-deep .cdk-overlay-container {
  z-index: 10000 !important;
}

:host ::ng-deep .cdk-global-overlay-wrapper {
  z-index: 10000 !important;
}
