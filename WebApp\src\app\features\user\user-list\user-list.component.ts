import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';
// import { UserService } from '../../../services/user.service';
import { AuthService } from '../../../../shared/services/auth.service';
import { RoleService } from '../../../services/role.service';
import { Role } from '../../../models/user.model';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';
import {
  UserAccountServiceProxy,
  UserDto,
} from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '../../user/user-request/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ServiceProxyModule,
    MatDialogModule,
    LoadingSpinnerComponent
],
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.css'],
})
export class UserListComponent implements OnInit {
  allUsers: UserDto[] = [];
  filteredUsers: UserDto[] = [];
  displayedUsers: UserDto[] = []; // Users to show on current page
  allRoles: Role[] = [];
  searchQuery = '';
  statusFilter = '';
  roleFilter = '';
  selectedRoleFilters: string[] = []; // Array for multi-role filtering (legacy - kept for compatibility)
  isLoading = true;
  currentUserId: string | null = null;

  // Pagination
  currentPage = 1;
  pageSize = 5;
  totalUsers = 0;
  totalPages = 1;
  pageSizeOptions = [5, 10, 15, 20, 50];
  paginationRange: number[] = [];

  Math = Math

  // Role management
  showRoleSelector = false;
  selectedUserEmail: string | null = null;
  selectedUser: UserDto | null = null;
  availableRoles: string[] = ['Admin', 'User', 'Doctor']; // Default available roles
  roleToAdd: string = '';
  selectedRolesToAdd: string[] = [];

  // Role management status
  isAddingRole = false;
  isRemovingRole = false;
  isDeletingUser = false;
  operationSuccess = false;
  operationError = false;
  operationMessage = '';

  // UI controls
  popoverPosition = { top: '0px', left: '0px' };
  multiRoleFilterEnabled = false; // Legacy - kept for compatibility
  showDropdown = false;

  // Search debouncing
  private searchSubject = new Subject<string>();

  constructor(
    private authService: AuthService,
    private roleService: RoleService,
    private userAccountService: UserAccountServiceProxy,
    private dialog: MatDialog
  ) {
    // Setup search debouncing
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(() => this.applyFilters());
  }

  ngOnInit(): void {
    console.log('UserListComponent ngOnInit called');
    this.currentUserId = this.authService.getUser()?.id || null;
    console.log('Current user ID:', this.currentUserId);

    this.loadData();



  }

  private loadData(): void {
    this.isLoading = true;
    console.log('Starting to load data...');
    console.log('UserAccountService:', this.userAccountService);

    // Check if service is available
    if (!this.userAccountService) {
      console.error('UserAccountService is not available');
      this.isLoading = false;
      this.allUsers = [];
      this.filteredUsers = [];
      this.updatePagination();
      return;
    }

    // Load users first, then roles
    this.userAccountService.getAll().subscribe({
      next: (users) => {
        console.log('Users loaded:', users);
        this.allUsers = users || [];
        this.filteredUsers = this.allUsers;
        this.extractUniqueRolesFromUsers();
        this.updatePagination();
        this.loadRoles();
      },
      error: (error) => {
        console.error('Error loading users:', error);
        console.log('API failed, setting empty user list');

        // Set empty arrays when API fails
        this.allUsers = [];
        this.filteredUsers = [];
        this.extractUniqueRolesFromUsers();
        this.updatePagination();
        this.isLoading = false;
        this.loadRoles(); // Still try to load roles
      }
    });
  }

  private loadRoles(): void {
    this.roleService.getAllRoles().subscribe({
      next: (roles) => {
        console.log('Roles loaded:', roles);
        this.allRoles = roles || [];
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        console.log('Loading fallback roles...');


        this.isLoading = false;
      }
    });
  }

  private extractUniqueRolesFromUsers(): void {
    const uniqueRoles = new Set<string>();
    this.allUsers.forEach(user => user.roles?.forEach(role => uniqueRoles.add(role)));
    if (uniqueRoles.size > 0) this.availableRoles = Array.from(uniqueRoles);
  }




  private updatePagination(): void {
    this.totalUsers = this.filteredUsers.length;
    this.totalPages = Math.ceil(this.totalUsers / this.pageSize) || 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;
    this.calculatePaginationRange();
    this.updateDisplayedUsers();
  }

  private calculatePaginationRange(): void {
    const maxPages = 5, half = Math.floor(maxPages / 2);
    let start = Math.max(1, this.currentPage - half);
    let end = Math.min(this.totalPages, start + maxPages - 1);
    if (end === this.totalPages) start = Math.max(1, end - maxPages + 1);
    this.paginationRange = Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  private updateDisplayedUsers(): void {
    const start = (this.currentPage - 1) * this.pageSize;
    this.displayedUsers = this.filteredUsers.slice(start, start + this.pageSize);
  }

  goToPage = (page: number): void => {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.updateDisplayedUsers();
      document.querySelector('.bg-white.rounded-lg.shadow-sm.border')?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  previousPage = (): void => { if (this.currentPage > 1) this.goToPage(this.currentPage - 1); };
  nextPage = (): void => { if (this.currentPage < this.totalPages) this.goToPage(this.currentPage + 1); };

  onPageSizeChange = (event: Event): void => {
    this.pageSize = Number((event.target as HTMLSelectElement).value);
    this.currentPage = 1;
    this.updatePagination();
  }

  onSearch = (): void => this.searchSubject.next(this.searchQuery);

  applyFilters(): void {
    const query = this.searchQuery.toLowerCase().trim();
    this.filteredUsers = this.allUsers.filter(user => {
      const matchesSearch = !query ||
        user.name?.toLowerCase().includes(query) ||
        user.email?.toLowerCase().includes(query) ||
        user.skills?.toLowerCase().includes(query);

      const matchesRole = !this.roleFilter || user.roles?.includes(this.roleFilter);

      return matchesSearch && matchesRole;
    });

    this.currentPage = 1;
    this.updatePagination();
  }

  private toggleArrayItem = (array: string[], item: string): void => {
    const index = array.indexOf(item);
    index === -1 ? array.push(item) : array.splice(index, 1);
  }

  toggleRoleInFilter = (role: string): void => {
    this.toggleArrayItem(this.selectedRoleFilters, role);
    this.applyFilters();
  }

  toggleMultiRoleFilter = (): void => {
    this.multiRoleFilterEnabled = !this.multiRoleFilterEnabled;
    this.roleFilter = '';
    this.selectedRoleFilters = [];
    this.showDropdown = false;
    this.applyFilters();
  }

  selectRole = (role: string): void => {
    this.roleFilter = role;
    this.showDropdown = false;
    this.applyFilters();
  }

  openRoleSelector = (user: UserDto, event?: MouseEvent): void => {
    this.closeRoleSelector();
    this.selectedUserEmail = user.email || null;
    this.selectedUser = user;
    this.selectedRolesToAdd = [];
    this.showRoleSelector = true;

    if (event) {
      const rect = (event.target as HTMLElement).getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const popoverHeight = 150;
      this.popoverPosition = spaceBelow < popoverHeight
        ? { top: `${rect.top + window.scrollY - popoverHeight - 5}px`, left: `${rect.left + window.scrollX - 150}px` }
        : { top: `${rect.bottom + window.scrollY + 5}px`, left: `${rect.left + window.scrollX - 150}px` };
    }

    this.operationSuccess = this.operationError = false;
    this.operationMessage = '';
  }

  closeRoleSelector = (): void => {
    this.showRoleSelector = false;
    this.selectedUserEmail = this.selectedUser = null;
    this.selectedRolesToAdd = [];
    this.isAddingRole = this.isRemovingRole = false;
  }

  getAvailableRolesToAdd = (): string[] =>
    this.availableRoles.filter(role => !this.selectedUser?.roles?.includes(role));

  toggleRoleSelection = (role: string): void =>
    this.toggleArrayItem(this.selectedRolesToAdd, role);

  // Add selected roles to user
  addRolesToUser(): void {
    if (!this.selectedUserEmail || this.selectedRolesToAdd.length === 0) return;

    this.isAddingRole = true;
    this.operationSuccess = false;
    this.operationError = false;

    // Process roles one by one
    const addRolePromises = this.selectedRolesToAdd.map((role) => {
      return new Promise<void>((resolve, reject) => {
        this.userAccountService
          .assignRole(this.selectedUserEmail as string, role)
          .subscribe({
            next: () => {
              resolve();
            },
            error: (error) => {
              console.error(`Error adding role ${role}:`, error);
              reject(error);
            },
          });
      });
    });

    // Wait for all role assignments to complete
    Promise.all(addRolePromises)
      .then(() => {
        // Update the user's roles in the UI
        if (this.selectedUser && this.selectedUser.roles) {
          this.selectedRolesToAdd.forEach((role) => {
            if (!this.selectedUser?.roles?.includes(role)) {
              this.selectedUser?.roles?.push(role);
            }
          });
        }

        this.operationSuccess = true;
        this.operationMessage = 'Roles added successfully!';
        this.selectedRolesToAdd = [];
        this.isAddingRole = false;

        // Close role selector after a short delay
        setTimeout(() => this.closeRoleSelector(), 1500);
      })
      .catch(() => {
        this.operationError = true;
        this.operationMessage = 'Failed to add roles. Please try again.';
        this.isAddingRole = false;
      });
  }

  // Remove role from user
  removeRoleFromUser(user: UserDto, role: string): void {
    if (!user.email || !role || user.roles?.length === 1) return;

    this.isRemovingRole = true;

    this.userAccountService.removeRole(user.email, role).subscribe({
      next: () => {
        // Update the user's roles in the UI
        if (user.roles) {
          const index = user.roles.indexOf(role);
          if (index !== -1) {
            user.roles.splice(index, 1);
          }
        }

        this.isRemovingRole = false;
      },
      error: (error) => {
        console.error('Error removing role:', error);
        this.isRemovingRole = false;
      },
    });
  }

  // Check if user has a specific role
  hasRole(user: UserDto, roleName: string): boolean {
    if (!user.roles) return false;
    return user.roles.some(
      (role) => role.toLowerCase() === roleName.toLowerCase()
    );
  }

  // Open delete confirmation dialog
  openDeleteConfirm(user: UserDto, event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();

    if (!user || !user.email) return;

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete User',
        message: 'Are you sure you want to delete this user?',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger',
        itemName: `${user.name || ''} (${user.email})`,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === true) {
        this.deleteUser(user.email!);
      }
    });
  }

  // Delete a user
  deleteUser(userEmail: string): void {
    if (!userEmail) return;

    this.isDeletingUser = true;
    this.operationSuccess = false;
    this.operationError = false;

    this.userAccountService.deleteUser(userEmail).subscribe({
      next: () => {
        // Remove the user from the lists
        this.allUsers = this.allUsers.filter((u) => u.email !== userEmail);
        this.filteredUsers = this.filteredUsers.filter(
          (u) => u.email !== userEmail
        );

        // Update pagination to reflect the change in user count
        this.updatePagination();

        // Show success toast or notification here if needed
        console.log('User deleted successfully');
        this.isDeletingUser = false;
      },
      error: (error) => {
        console.error('Error deleting user:', error);
        // Show error notification here if needed

        this.isDeletingUser = false;

        // Show error dialog
        this.dialog.open(ConfirmationDialogComponent, {
          width: '400px',
          data: {
            title: 'Error',
            message: 'Failed to delete user. Please try again.',
            confirmText: 'OK',
            type: 'danger',
          },
        });
      },
    });
  }

  private roleColorMap = {
    admin: 'bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-950 dark:text-blue-200',
    user: 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-950 dark:text-green-200',
    doctor: 'bg-purple-100 text-purple-800 hover:bg-purple-200 dark:bg-purple-950 dark:text-purple-200'
  };

  getRoleColorClass = (role: string): string =>
    this.roleColorMap[role.toLowerCase() as keyof typeof this.roleColorMap] || 'bg-gray-100 text-gray-800 hover:bg-gray-200';

  getRoleDescription = (role: string): string => {
    const descriptions: { [key: string]: string } = {
      'Admin': 'Full system access and user management',
      'User': 'Standard user with basic permissions',
      'Doctor': 'Medical professional with clinical access'
    };
    return descriptions[role] || 'Role with specific permissions';
  };

  getSkillsString = (skills: string | undefined | null): string => skills || 'No skills listed';


  // Add this property if you don't have it already
tableView: boolean = true; // or false, depending on your view toggle logic

getPageSizeOptions(): number[] {
  return this.tableView ? [5, 10, 15, 20] : [6, 12, 24, 48];
}
}
