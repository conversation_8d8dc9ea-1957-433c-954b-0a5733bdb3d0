<div class="max-w-7xl mx-auto p-6 dark:bg-gray-900">
  <!-- Header with gradient background -->
  <div
    class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 overflow-hidden">
    <div class="px-6 py-4 border-b border-blue-100 dark:border-gray-700 flex items-center justify-between">
      <div class="flex items-center">
        <div class="bg-blue-500 p-2 rounded-lg mr-3">
          <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <div>
          <h1 class="text-xl font-bold text-gray-800 dark:text-white">{{ pageTitle }}</h1>
          <p class="text-gray-600 dark:text-gray-300 text-sm">
            {{ isEditing ? 'Update appointment details and schedule' : 'Schedule a new appointment with patient
            information' }}
          </p>
        </div>
      </div>
      <button (click)="goBack()"
        class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to List
      </button>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="flex justify-center items-center p-12">
      <div class="flex flex-col items-center">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">{{ isEditing ? 'Loading appointment details...' : 'Preparing form...' }}
        </p>
      </div>
    </div>

    <div *ngIf="!isLoading" class="p-6">
      <form #appointmentForm="ngForm" (ngSubmit)="saveAppointment()" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Left Column - Basic Info -->
          <div class="lg:col-span-2 space-y-6">
            <div>
              <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b pb-2 mb-4">Patient Information</h2>

              <!-- Patient Email -->
              <div class="mb-4">
                <label for="patientEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Patient Email</label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                  <input type="email" id="patientEmail" name="patientEmail" [(ngModel)]="appointment.patientEmail"
                    [readonly]="!isAdmin" required
                    class="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    [class.bg-gray-50]="!isAdmin" [class.dark:bg-gray-900]="!isAdmin" placeholder="Enter patient email address">
                </div>
                <p *ngIf="!isAdmin" class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Email is set to your account and cannot be changed
                </p>
              </div>

              <!-- Status (Admin Only) -->
              <div *ngIf="isAdmin" class="mb-4">
                <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Status</label>
                <select id="status" name="status" [(ngModel)]="appointment.status"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option *ngFor="let status of statusOptions" [value]="status">{{status}}</option>
                </select>
              </div>
            </div>

            <div>
              <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b pb-2 mb-4">Appointment Details</h2>

              <!-- Scheduled Time -->
              <div class="mb-4">
                <label for="scheduledTime" class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Scheduled Date &
                  Time</label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                      </path>
                    </svg>
                  </div>
                  <input type="datetime-local" id="scheduledTime" name="scheduledTime"
                    [value]="getDateTimeLocalValue(appointment.scheduledTime)" (change)="onScheduledTimeChange($event)"
                    [min]="minDate" [max]="maxDate" required
                    class="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Select a date and time between now and one year from today
                </p>
              </div>

              <!-- Message -->
              <div class="mb-4">
                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">Appointment Message</label>
                <textarea id="message" name="message" [(ngModel)]="appointment.message" rows="4"
                  placeholder="Describe your symptoms, concerns, or any additional information about this appointment..."
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Provide any relevant details that will help prepare for your appointment
                </p>
              </div>
            </div>
          </div>

          <!-- Right Column - Image Upload -->
          <div>
            <h2 class="text-lg font-medium text-gray-900 dark:text-white border-b pb-2 mb-4">Appointment Image</h2>

            <!-- Image Upload Card -->
            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
              <!-- File input -->
              <div class="flex flex-col mb-4">
                <input #fileInput type="file" accept="image/*" class="hidden" (change)="onFileSelected($event)" />
              <button type="button" (click)="triggerFileInput()"
                  class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors mb-2"
                  [disabled]="isUploading">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2v12a2 2 0 002 2z" />
                  </svg>
                  {{ isUploading ? 'Uploading...' : 'Choose Image' }}
                </button>
              </div>

              <!-- Upload error message -->
              <div *ngIf="uploadError" class="text-red-500 dark:text-red-400 text-sm mb-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 p-2 rounded">
                <div class="flex items-center">
                  <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd" />
                  </svg>
                  {{ uploadError }}
                </div>
              </div>

              <!-- Image preview -->
              <div *ngIf="imagePreview" class="mt-2 mb-4">
                <div class="flex justify-between items-center mb-2">
                  <span class="text-sm font-medium text-gray-700 dark:text-gray-200">Preview:</span>
                  <button type="button" (click)="removeImage()"
                    class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-500 flex items-center text-sm font-medium">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Remove
                  </button>
                </div>

                <div class="border rounded-lg overflow-hidden w-full relative bg-white dark:bg-gray-900">
                  <img [src]="imagePreview" class="w-full h-auto max-h-64 object-contain" alt="Appointment preview" />
                </div>
              </div>

              <div *ngIf="!imagePreview" class="bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 text-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 dark:text-gray-600 mb-2" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2v12a2 2 0 002 2z" />
                </svg>
                <p class="text-sm text-gray-500 dark:text-gray-400">No image selected</p>
                <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">Click "Choose Image" to upload</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-8">
          <div class="flex justify-end space-x-4">
          <button type="button" (click)="goBack()"
            class="px-6 py-2 border border-gray-300 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
              Cancel
            </button>
          <button type="submit" [disabled]="!isFormValid() || isLoading"
            class="px-6 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors disabled:bg-blue-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <div class="flex items-center">
                <svg *ngIf="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                {{ isEditing ? 'Update' : 'Schedule' }} Appointment
              </div>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
