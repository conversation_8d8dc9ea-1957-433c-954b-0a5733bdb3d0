/**
 * Custom YouTube Embed Tool for EditorJS
 */
export class YouTubeEmbedTool {
  static get toolbox() {
    return {
      title: 'YouTube',
      icon: '<svg width="17" height="15" viewBox="0 0 17 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.6866 2.39062C16.6866 1.07031 15.6163 0 14.2959 0H2.70411C1.38379 0 0.313477 1.07031 0.313477 2.39062V12.6094C0.313477 13.9297 1.38379 15 2.70411 15H14.2959C15.6163 15 16.6866 13.9297 16.6866 12.6094V2.39062ZM6.64551 11.1328V3.86719L11.3819 7.5L6.64551 11.1328Z" fill="currentColor"/></svg>'
    };
  }

  static get isReadOnlySupported() {
    return true;
  }

  constructor({ data, api, readOnly }: any) {
    this.api = api;
    this.readOnly = readOnly;
    this.data = {
      url: data.url || '',
      title: data.title || '',
      description: data.description || ''
    };
    this.wrapper = null;
  }

  render() {
    this.wrapper = document.createElement('div');
    this.wrapper.classList.add('youtube-embed-tool');

    this.updateContent();
    this.addEventListeners();

    return this.wrapper;
  }

  updateContent() {
    if (!this.wrapper) return;

    if (this.data.url) {
      this.wrapper.innerHTML = this.createEmbedHTML();
    } else {
      this.wrapper.innerHTML = this.createInputHTML();
    }
  }

  addEventListeners() {
    if (!this.wrapper) return;

    // Handle input events
    const input = this.wrapper.querySelector('.youtube-url-input') as HTMLInputElement;
    const button = this.wrapper.querySelector('.youtube-embed-btn') as HTMLButtonElement;
    const editBtn = this.wrapper.querySelector('.youtube-edit-btn') as HTMLButtonElement;

    if (input && button) {
      button.addEventListener('click', () => {
        const url = input.value.trim();
        if (url && this.extractVideoId(url)) {
          this.data.url = url;
          this.updateContent();
          this.addEventListeners(); // Re-add listeners after content update
        } else {
          alert('Please enter a valid YouTube URL');
        }
      });

      input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          button.click();
        }
      });
    }

    if (editBtn) {
      editBtn.addEventListener('click', () => {
        this.data.url = '';
        this.updateContent();
        this.addEventListeners(); // Re-add listeners after content update
      });
    }
  }

  createInputHTML() {
    return `
      <div class="youtube-input-container border border-dashed border-gray-300 dark:border-gray-600 p-6 text-center rounded-xl bg-gradient-to-br from-red-50 to-orange-50 dark:from-gray-800 dark:to-gray-900 shadow-sm hover:shadow-md transition-all duration-300">
        <div class="flex items-center justify-center mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center mr-3 shadow-sm">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z" fill="white"/>
              <polygon points="9.75,15.02 15.5,11.75 9.75,8.48" fill="#dc2626"/>
            </svg>
          </div>
          <div>
            <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100 mb-1">YouTube Video</h3>
            <p class="text-xs text-gray-600 dark:text-gray-400">Embed YouTube videos and shorts easily</p>
          </div>
        </div>

        <input
          type="text"
          placeholder="Paste YouTube URL here (videos and shorts)..."
          class="youtube-url-input w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg mb-4 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400 focus:border-red-500 dark:focus:border-red-400 transition-all duration-200"
        />

        <button
          class="youtube-embed-btn bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
        >
          Embed Video
        </button>
      </div>
    `;
  }

  createEmbedHTML() {
    const videoId = this.extractVideoId(this.data.url);
    if (!videoId) {
      return this.createInputHTML();
    }

    return `
      <div class="youtube-embed-wrapper relative w-full my-4 rounded-xl overflow-hidden shadow-lg bg-white dark:bg-gray-800 hover:shadow-xl transition-all duration-300 hover:-translate-y-0.5">
        <div class="bg-gradient-to-r from-red-600 to-red-700 px-4 py-3 flex items-center justify-between">
          <div class="flex items-center">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2">
              <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z" fill="white"/>
              <polygon points="9.75,15.02 15.5,11.75 9.75,8.48" fill="#dc2626"/>
            </svg>
            <span class="text-white font-semibold text-sm">YouTube Video</span>
          </div>
          <span class="text-red-100 text-xs font-mono bg-red-800 bg-opacity-30 px-2 py-1 rounded">youtube.com</span>
        </div>

        <div class="relative w-full bg-gray-50 dark:bg-gray-900" style="padding-bottom: 56.25%; height: 0;">
          <iframe
            width="100%"
            height="100%"
            src="https://www.youtube.com/embed/${videoId}"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            class="absolute top-0 left-0 w-full h-full"
          ></iframe>
        </div>

        ${this.data.title ? `<div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
          <p class="text-gray-700 dark:text-gray-300 font-medium text-sm mb-0">${this.data.title}</p>
        </div>` : ''}

        ${!this.readOnly ? `<button class="youtube-edit-btn absolute top-4 right-4 bg-gray-800 dark:bg-gray-700 bg-opacity-80 hover:bg-opacity-100 text-white border-0 px-2.5 py-1.5 rounded-md cursor-pointer text-xs font-medium transition-all duration-200 backdrop-blur-sm z-10">Edit</button>` : ''}
      </div>
    `;
  }

  extractVideoId(url: string): string | null {
    // Handle various YouTube URL formats including YouTube Shorts
    const patterns = [
      // Regular YouTube URLs
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/,
      // YouTube Shorts URLs
      /(?:youtube\.com\/shorts\/|www\.youtube\.com\/shorts\/)([^&\n?#]+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return null;
  }

  save() {
    return this.data;
  }

  validate(savedData: any) {
    return savedData.url && savedData.url.trim() !== '';
  }

  static get sanitize() {
    return {
      url: {},
      title: {},
      description: {}
    };
  }

  onPaste(event: any) {
    // Handle paste events for YouTube URLs
    const text = event.clipboardData?.getData('text');
    if (text && this.extractVideoId(text)) {
      this.data.url = text;
      return true;
    }
    return false;
  }

  private api: any;
  private readOnly: boolean;
  private wrapper: HTMLElement | null;
  private data: {
    url: string;
    title: string;
    description: string;
  };
}

// Make the tool available for import
export default YouTubeEmbedTool;
