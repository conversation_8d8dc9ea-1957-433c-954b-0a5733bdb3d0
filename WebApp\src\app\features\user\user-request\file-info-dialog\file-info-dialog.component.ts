import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

export interface FileInfoDialogData {
  file: any; // RequestFileDto
}

@Component({
  selector: 'app-file-info-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div class="max-w-4xl max-h-screen overflow-y-auto bg-white dark:bg-gray-900 rounded-xl shadow-2xl transition-colors duration-200">
      <!-- Header -->
      <div class="flex items-start p-6 border-b border-gray-200 dark:border-gray-700 gap-4 bg-gray-50 dark:bg-gray-800 transition-colors duration-200">
        <div class="flex-shrink-0 p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
          <mat-icon class="text-2xl w-8 h-8 text-blue-600 dark:text-blue-400">{{ getFileIcon() }}</mat-icon>
        </div>
        <div class="flex-1 min-w-0">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100 break-words leading-tight mb-1">
            {{ data.file.originalFileName || 'Unknown File' }}
          </h2>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ formatFileSize(data.file.fileSize) }} • {{ formatDate(data.file.createdDate) }}
          </p>
        </div>
        <button mat-icon-button (click)="close()"
                class="flex-shrink-0 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200"
                aria-label="Close dialog">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <!-- Image Preview -->
      <div *ngIf="isImageFile()" class="p-6 text-center bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 transition-colors duration-200">
        <div class="relative inline-block">
          <img
            [src]="data.file.blobUrl"
            [alt]="data.file.originalFileName"
            class="max-w-full max-h-96 rounded-lg shadow-lg transition-transform duration-200 hover:scale-105"
            (error)="onImageError($event)"
            (load)="onImageLoad()"
          >
          <div *ngIf="isImageLoading"
               class="absolute inset-0 flex items-center justify-center bg-white/90 dark:bg-gray-900/90 rounded-lg">
            <mat-icon class="animate-spin text-blue-600 dark:text-blue-400 text-2xl">autorenew</mat-icon>
          </div>
        </div>
      </div>

      <!-- File Details -->
      <div class="p-6 bg-white dark:bg-gray-900 transition-colors duration-200">
        <div class="mb-6">
          <div class="flex items-center gap-2 mb-4">
            <div class="w-1 h-4 bg-blue-600 dark:bg-blue-400 rounded"></div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">File Information</h3>
          </div>
          <div class="grid gap-3">
            <div class="flex gap-3 py-2 border-b border-gray-100 dark:border-gray-700 transition-colors duration-200">
              <span class="font-medium text-gray-700 dark:text-gray-300 min-w-32 flex-shrink-0">Type:</span>
              <span class="text-gray-900 dark:text-gray-100 flex-1 break-words">{{ data.file.mimeType || 'Unknown' }}</span>
            </div>
            <div *ngIf="data.file.width && data.file.height"
                 class="flex gap-3 py-2 border-b border-gray-100 dark:border-gray-700 transition-colors duration-200">
              <span class="font-medium text-gray-700 dark:text-gray-300 min-w-32 flex-shrink-0">Dimensions:</span>
              <span class="text-gray-900 dark:text-gray-100 flex-1 break-words">{{ data.file.width }} × {{ data.file.height }}px</span>
            </div>
            <div *ngIf="data.file.uploadedBy"
                 class="flex gap-3 py-2 border-b border-gray-100 dark:border-gray-700 transition-colors duration-200">
              <span class="font-medium text-gray-700 dark:text-gray-300 min-w-32 flex-shrink-0">Uploaded by:</span>
              <span class="text-gray-900 dark:text-gray-100 flex-1 break-words">{{ data.file.uploadedBy }}</span>
            </div>
            <div *ngIf="data.file.description"
                 class="flex gap-3 py-2 transition-colors duration-200">
              <span class="font-medium text-gray-700 dark:text-gray-300 min-w-32 flex-shrink-0">Description:</span>
              <span class="text-gray-900 dark:text-gray-100 flex-1 break-words">{{ data.file.description }}</span>
            </div>
          </div>
        </div>

        <!-- AI Analysis Section -->
        <div *ngIf="data.file.aiAnalysis" class="mb-6">
          <div class="flex items-center gap-2 mb-4">
            <div class="w-1 h-4 bg-green-600 dark:bg-green-400 rounded"></div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">AI Medical Analysis</h3>
          </div>
          <div class="bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500 dark:border-green-400 p-5 rounded-r-lg transition-colors duration-200">
            <p class="text-gray-900 dark:text-gray-100 leading-relaxed whitespace-pre-wrap">{{ formatAIAnalysis(data.file.aiAnalysis) }}</p>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 transition-colors duration-200">
        <button mat-button (click)="downloadFile()"
                class="flex items-center gap-2 px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
          <mat-icon>download</mat-icon>
          Download
        </button>
        <button mat-raised-button color="primary" (click)="close()"
                class="px-6 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white rounded-lg transition-colors duration-200">
          Close
        </button>
      </div>
    </div>
  `,
  styles: [`
    /* Custom animations for loading spinner */
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .animate-spin {
      animation: spin 1s linear infinite;
    }

    /* Smooth hover transitions for image */
    .hover\\:scale-105:hover {
      transform: scale(1.05);
    }

    /* Ensure proper Material button styling works with Tailwind */
    .mat-mdc-button, .mat-mdc-raised-button {
      font-family: inherit !important;
    }
  `]
})
export class FileInfoDialogComponent {
  isImageLoading = true;

  constructor(
    public dialogRef: MatDialogRef<FileInfoDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: FileInfoDialogData
  ) { }

  close(): void {
    this.dialogRef.close();
  }

  isImageFile(): boolean {
    return this.data.file.mimeType && this.data.file.mimeType.startsWith('image/');
  }

  getFileIcon(): string {
    const mimeType = this.data.file.mimeType?.toLowerCase() || '';

    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.includes('pdf')) return 'picture_as_pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'description';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'table_chart';
    if (mimeType.includes('video/')) return 'video_file';
    if (mimeType.includes('audio/')) return 'audio_file';
    if (mimeType.includes('text/')) return 'text_snippet';
    return 'insert_drive_file';
  }

  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDate(date: string | Date): string {
    if (!date) return 'Unknown date';
    const d = new Date(date);
    return d.toLocaleDateString() + ' at ' + d.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
  }

  formatAIAnalysis(analysis: string): string {
    if (!analysis) return 'No AI analysis available';

    if (analysis.includes('temporarily unavailable') || analysis.includes('not configured')) {
      return 'AI analysis is temporarily unavailable. The system is being configured.';
    }

    if (analysis.includes('Could not generate description')) {
      return 'Analysis pending - please try uploading the image again.';
    }

    let cleaned = analysis
      .replace(/Analysis Date:.*?\n/gi, '')
      .replace(/Medical Image Analysis Context:.*?documentation purposes only\s*/gi, '')
      .replace(/Patient identifier:.*?\n/gi, '')
      .replace(/Image Analysis:\s*/gi, '')
      .replace(/\r\n/g, '\n')
      .replace(/\n+/g, '\n')
      .trim();

    return cleaned || 'Analysis pending';
  }

  downloadFile(): void {
    if (this.data.file.blobUrl) {
      const link = document.createElement('a');
      link.href = this.data.file.blobUrl;
      link.download = this.data.file.originalFileName || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  onImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = '/assets/images/document_placeholder.jpg';
    imgElement.alt = 'Image could not be loaded';
    this.isImageLoading = false;
  }

  onImageLoad(): void {
    this.isImageLoading = false;
  }
}
