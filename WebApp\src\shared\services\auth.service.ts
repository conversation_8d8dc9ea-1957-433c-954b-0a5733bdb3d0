import { Injectable, Injector } from '@angular/core';
import { Router } from '@angular/router';
import { DateTime } from 'luxon';
import { BehaviorSubject, Observable } from 'rxjs';
import { ChatCacheService } from '../../app/services/chat-cache.service';
import { GlobalMessageNotificationService } from '../../app/services/global-message-notification.service';
import { OnlineStatusService } from '../../app/services/online-status.service';

export interface AuthTokenData {
  token: string;
  expiresAt: DateTime;
  refreshToken?: string;
}

export interface AuthUser {
  id: string;
  email: string;
  firstName?: string;
  role: string[];
  [key: string]: any;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private userSubject = new BehaviorSubject<AuthUser | null>(null);
  user$: Observable<AuthUser | null> = this.userSubject.asObservable();

  private readonly TOKEN_KEY = 'jwt_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly TOKEN_EXPIRY_KEY = 'token_expiry';

  isUserLoggedIn = false;
  modelName: any = null;
  private tokenExpirationTimer: any;

  constructor(private router: Router, private injector: Injector) {
    // Initialize immediately but with better error handling
    this.initializeAuth();
  }

  /**
   * Initialize authentication with proper error handling
   */
  private initializeAuth(): void {
    try {
      // Check if we have a token first
      const token = this.getToken();
      if (token) {
        console.log('AuthService: Token found during initialization, loading user');
        this.loadUser();
      } else {
        console.log('AuthService: No token found during initialization');
        this.userSubject.next(null);
        this.isUserLoggedIn = false;
      }

      this.getModel();
      this.setupTokenRefreshTimer();
      this.startPeriodicTokenValidation();
    } catch (error) {
      console.error('AuthService: Error during initialization:', error);
      this.userSubject.next(null);
      this.isUserLoggedIn = false;
    }
  }

  // Token Management

  /**
   * Save JWT token with security settings
   * @param token JWT token string
   * @param expiryDate Token expiration date
   */
  setToken(token: string, expiryDate: DateTime): void {
    try {
      // Store token in a secure cookie with enhanced security
      const isSecure = window.location.protocol === 'https:';
      const cookieValue = `${this.TOKEN_KEY}=${token}; expires=${expiryDate.toJSDate().toUTCString()}; path=/; SameSite=Lax${isSecure ? '; Secure' : ''}`;
      document.cookie = cookieValue;

      console.log('AuthService: Token saved to cookie', {
        tokenLength: token.length,
        expires: expiryDate.toJSDate().toUTCString(),
        isSecure
      });

      // Also store expiration date in local storage for refresh timer
      localStorage.setItem(this.TOKEN_EXPIRY_KEY, expiryDate.toISO() || '');

      // Clear any existing timer and set up a new one
      this.clearTokenTimer();
      this.setupTokenRefreshTimer();

      this.loadUser(); // Reload user data from the token
    } catch (error) {
      console.error('AuthService: Error setting token:', error);
    }
  }

  /**
   * Get JWT token from cookies
   */
  getToken(): string | null {
    try {
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, ...valueParts] = cookie.trim().split('=');
        if (name === this.TOKEN_KEY) {
          const value = valueParts.join('='); // Handle tokens with = in them
          if (value && value.length > 0) {
            return value;
          }
        }
      }
      return null;
    } catch (error) {
      console.error('AuthService: Error reading token from cookies:', error);
      return null;
    }
  }

  /**
   * Check if token exists in cookies (without validation)
   */
  hasTokenInCookie(): boolean {
    const token = this.getToken();
    const exists = token !== null && token.length > 0;
    return exists;
  }

  /**
   * Check if we're in a hard refresh scenario
   */
  private isHardRefresh(): boolean {
    // Check if we have a token but no user loaded yet
    return this.hasTokenInCookie() && !this.isUserLoggedIn && !this.userSubject.value;
  }

  /**
   * Check if user is authenticated (has valid token)
   */
  isAuthenticated(): boolean {
    const token = this.getToken();
    if (!token) {
      return false;
    }

    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return false;
      }

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      // Check if token is actually expired (no buffer for authentication check)
      return payload['exp'] && payload['exp'] > currentTime;
    } catch (error) {
      return false;
    }
  }

  /**
   * Sets the refresh token
   */
  setRefreshToken(refreshToken: string): void {
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  /**
   * Gets the refresh token
   */
  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  /**
   * Decode JWT token to get user info and claims
   */
  decodeToken(token: string): AuthUser | null {
    try {
      // Split the token and get the payload part (second segment)
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  /**
   * Check if token is valid and not expired
   */
  isTokenValid(): boolean {
    const token = this.getToken();
    if (!token) {
      return false;
    }

    try {
      // Check if token has proper JWT format
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.log('AuthService: Invalid token format');
        return false;
      }

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);

      // Add 2 minute buffer to account for clock skew and network delays
      const bufferTime = 120; // 2 minutes
      const isValid = payload['exp'] && (payload['exp'] - bufferTime) > currentTime;

      if (!isValid) {
        console.log('AuthService: Token expired or close to expiry', {
          exp: payload['exp'],
          current: currentTime,
          timeUntilExpiry: payload['exp'] - currentTime,
          bufferTime: bufferTime
        });
      }

      return isValid;
    } catch (error) {
      console.error('AuthService: Error validating token:', error);
      return false;
    }
  }

  /**
   * Load user from token
   */
  loadUser(): void {
    try {
      const token = this.getToken();

      if (!token) {
        console.log('AuthService: No token found during loadUser - setting as not logged in');
        this.userSubject.next(null);
        this.isUserLoggedIn = false;
        return;
      }

      console.log('AuthService: Loading user from token...');

      // Decode token without aggressive validation during initialization
      const decodedToken = this.decodeToken(token);
      if (!decodedToken) {
        console.log('AuthService: Failed to decode token during loadUser');
        // Don't immediately clear tokens, just set as not logged in
        this.userSubject.next(null);
        this.isUserLoggedIn = false;
        return;
      }

      // Check if token is expired (but be more lenient during initialization)
      const payload = decodedToken;
      const currentTime = Math.floor(Date.now() / 1000);

      // During loadUser, only check if token is really expired (no buffer)
      if (payload['exp'] && payload['exp'] <= currentTime) {
        console.log('AuthService: Token is actually expired during loadUser');
        this.userSubject.next(null);
        this.isUserLoggedIn = false;
        this.clearTokens();
        return;
      }

      // Extract user information from token payload
      const userData: AuthUser = {
        id: decodedToken['sub'] || decodedToken['email'] || decodedToken['id'],
        email: decodedToken['email'] || decodedToken['unique_name'],
        firstName: decodedToken['name'] || decodedToken['given_name'] || decodedToken['firstName'],
        role: this.extractRoles(decodedToken)
      };

      console.log('AuthService: User loaded successfully', userData);
      this.userSubject.next(userData);
      this.isUserLoggedIn = true;
    } catch (error) {
      console.error('AuthService: Error during loadUser:', error);
      // Don't clear tokens on error, just set as not logged in
      this.userSubject.next(null);
      this.isUserLoggedIn = false;
    }
  }

  /**
   * Extract roles from token payload (handles different token formats)
   */
  private extractRoles(decodedToken: any): string[] {
    // Try different possible role claim names
    const roleClaims = [
      'role', 'roles', 'http://schemas.microsoft.com/ws/2008/06/identity/claims/role',
      'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/role'
    ];

    for (const claim of roleClaims) {
      if (decodedToken[claim]) {
        const roles = decodedToken[claim];
        if (Array.isArray(roles)) {
          return roles;
        } else if (typeof roles === 'string') {
          return [roles];
        }
      }
    }

    return [];
  }

  /**
   * Setup token expiration timer
   */
  private setupTokenRefreshTimer(): void {
    // Get token expiry from localStorage
    const expiryTimeString = localStorage.getItem(this.TOKEN_EXPIRY_KEY);
    if (!expiryTimeString) return;

    const expiryTime = DateTime.fromISO(expiryTimeString);
    const now = DateTime.now();

    // If token is expired, logout immediately
    if (expiryTime <= now) {
      this.logout();
      return;
    }

    // Set timer to refresh token 5 minutes before expiration
    // Calculate milliseconds until refresh time (5 minutes before expiration)
    const refreshTime = expiryTime.minus({ minutes: 5 });
    const refreshDuration = refreshTime.diff(now).milliseconds;

    if (refreshDuration > 0) {
      this.tokenExpirationTimer = setTimeout(() => {
        this.attemptTokenRefresh();
      }, refreshDuration);
    } else {
      // If less than 5 minutes remaining, try to refresh immediately
      this.attemptTokenRefresh();
    }
  }

  /**
   * Attempt to refresh the token
   */
  private async attemptTokenRefresh(): Promise<void> {
    console.log('Token is about to expire, attempting refresh...');

    // Since there's no refresh token endpoint in the backend,
    // we'll show a warning to the user and logout gracefully
    const user = this.getUser();
    if (user) {
      console.log('Session will expire soon. Please save your work and login again.');

      // Give user 2 minutes warning before logout
      setTimeout(() => {
        console.log('Session expired. Logging out...');
        this.forceLogout('Session expired');
      }, 2 * 60 * 1000); // 2 minutes
    } else {
      this.logout();
    }
  }

  /**
   * Force logout without confirmation (used for token expiration, etc.)
   */
  forceLogout(reason: string = 'Session expired'): void {
    console.log(`AuthService: Force logout requested - ${reason}`);

    // Check if we really need to logout - be more conservative
    const token = this.getToken();
    if (token && this.isTokenValid()) {
      console.log('AuthService: Token is still valid, not forcing logout');
      return;
    }

    console.log('AuthService: Proceeding with force logout');

    try {
      // First, explicitly disconnect from SignalR
      this.handleOnlineStatusDisconnect();

      // Clear authentication tokens
      this.clearTokens();

      // Clear token refresh timer
      this.clearTokenTimer();

      // Reset user state
      this.userSubject.next(null);
      this.isUserLoggedIn = false;

      // Clear model preference
      this.modelName = null;

      // Clear session-related data
      this.clearSessionData();

      console.log('AuthService: Force logout completed, navigating to login');

      // Navigate to login page with reason
      this.router.navigate(['/auth/login'], {
        queryParams: {
          redirected: 'forced',
          reason: reason
        }
      });

    } catch (error) {
      console.error('AuthService: Error during force logout:', error);
      // Even if there's an error, clear what we can and navigate to login
      this.userSubject.next(null);
      this.isUserLoggedIn = false;
      this.router.navigate(['/auth/login']);
    }
  }

  /**
   * Clear token expiration timer
   */
  private clearTokenTimer(): void {
    if (this.tokenExpirationTimer) {
      clearTimeout(this.tokenExpirationTimer);
      this.tokenExpirationTimer = null;
    }
  }

  /**
   * Start periodic token validation (every 5 minutes - less aggressive)
   */
  private startPeriodicTokenValidation(): void {
    setInterval(() => {
      if (this.isUserLoggedIn) {
        const token = this.getToken();
        if (!token) {
          console.log('AuthService: Periodic validation - no token found');
          this.forceLogout('No token found');
        } else if (!this.isTokenValid()) {
          console.log('AuthService: Periodic validation - token expired');
          this.forceLogout('Token expired');
        } else {
          console.log('AuthService: Periodic validation - token is valid');
        }
      }
    }, 300000); // Check every 5 minutes (less aggressive)
  }

  /**
   * Handle invalid token situation
   */
  private handleInvalidToken(): void {
    console.log('AuthService: Handling invalid token');
    this.userSubject.next(null);
    this.isUserLoggedIn = false;
    // Only clear tokens if we're sure they're invalid
    // Don't clear during initialization to avoid losing valid tokens
    const token = this.getToken();
    if (token) {
      try {
        const parts = token.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]));
          const currentTime = Math.floor(Date.now() / 1000);
          // Only clear if token is actually expired
          if (payload['exp'] && payload['exp'] <= currentTime) {
            console.log('AuthService: Token is expired, clearing tokens');
            this.clearTokens();
          } else {
            console.log('AuthService: Token appears valid, not clearing');
          }
        } else {
          console.log('AuthService: Invalid token format, clearing tokens');
          this.clearTokens();
        }
      } catch (error) {
        console.log('AuthService: Error parsing token, clearing tokens');
        this.clearTokens();
      }
    }
  }

  /**
   * Clear all authentication tokens
   */
  private clearTokens(): void {
    try {
      console.log('AuthService: Clearing authentication tokens');

      // Clear cookie token with same security settings
      const isSecure = window.location.protocol === 'https:';
      document.cookie = `${this.TOKEN_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax${isSecure ? '; Secure' : ''}`;

      // Clear localStorage items
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
      localStorage.removeItem(this.TOKEN_EXPIRY_KEY);

      console.log('AuthService: Tokens cleared successfully');
    } catch (error) {
      console.error('AuthService: Error clearing tokens:', error);
    }
  }

  /**
   * Save complete user session data
   */
  saveUserSession(userData: AuthUser, tokenData: AuthTokenData): void {
    this.setToken(tokenData.token, tokenData.expiresAt);

    if (tokenData.refreshToken) {
      this.setRefreshToken(tokenData.refreshToken);
    }

    // Set a flag to indicate user has just logged in
    sessionStorage.setItem('just_logged_in', 'true');

    // Set app download banner flag if the user has the User role
    if (userData.role && userData.role.includes('User') && !userData.role.includes('Admin')) {
      sessionStorage.setItem('show_app_download_banner', 'true');
    }

    this.userSubject.next(userData);
    this.isUserLoggedIn = true;
  }

  /**
   * Get current user
   */
  getUser(): AuthUser | null {
    return this.userSubject.value;
  }

  /**
   * Logout user and clear all auth data
   */
  logout(): void {
    console.log('AuthService: Logging out user');

    try {
      // First, explicitly disconnect from SignalR
      this.handleOnlineStatusDisconnect();

      // Clear authentication tokens
      this.clearTokens();

      // Clear token refresh timer
      this.clearTokenTimer();

      // Reset user state
      this.userSubject.next(null);
      this.isUserLoggedIn = false;

      // Clear model preference
      this.modelName = null;

      // Clear session-related data
      this.clearSessionData();

      // Clear chat cache
      this.injector.get(ChatCacheService).clearAllCaches();

      // Clear unread message counts
      this.injector.get(GlobalMessageNotificationService).resetAllUnreadCounts();

      // Clear online status
      this.injector.get(OnlineStatusService).handleLogout();

      console.log('AuthService: Logout completed, navigating to login');

      // Navigate to login page
      this.router.navigate(['/auth/login'], {
        queryParams: { redirected: 'logout' }
      });

    } catch (error) {
      console.error('AuthService: Error during logout:', error);
      // Even if there's an error, clear what we can and navigate to login
      this.userSubject.next(null);
      this.isUserLoggedIn = false;
      this.router.navigate(['/auth/login']);
    }
  }

  /**
   * Get user roles
   */
  getUserRoles(): string[] {
    const user = this.getUser();
    return user?.role || [];
  }

  /**
   * Check if app download banner should be shown (user is logged in with User role)
   */
  shouldShowAppDownloadBanner(): boolean {
    if (!this.isUserLoggedIn) {
      return false;
    }

    const userRoles = this.getUserRoles();
    const isUser = userRoles.includes('User') && !userRoles.includes('Admin');
    const bannerDismissed = localStorage.getItem('app_download_banner_dismissed') === 'true';

    return isUser && !bannerDismissed;
  }

  /**
   * Check if user has admin role
   */
  isAdmin(): boolean {
    const roles = this.getUserRoles();
    return roles.includes('Admin');
  }

  /**
   * Check if user has only the User role and no other roles
   */
  isOnlyUserRole(): boolean {
    const roles = this.getUserRoles();
    return roles.length === 1 && roles.includes('User');
  }

  /**
   * Check if user has the Doctor role
   */
  hasDoctorRole(): boolean {
    return this.hasRole('Doctor');
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    const roles = this.getUserRoles();
    return roles.includes(role);
  }

  /**
   * Set model preference
   */
  setModel(model: any): void {
    localStorage.setItem('model', JSON.stringify(model));
    this.modelName = model;
  }

  /**
   * Get model preference
   */
  getModel(): any | null {
    const model = localStorage.getItem('model');
    this.modelName = model ? JSON.parse(model) : 'Select Model';
    return this.modelName;
  }

  /**
   * Handle SignalR disconnect during logout
   * Uses event system to avoid circular dependency
   */
  private handleOnlineStatusDisconnect(): void {
    try {
      console.log('AuthService: Triggering logout event for OnlineStatusService');
      // Emit a custom event that OnlineStatusService can listen to
      const logoutEvent = new CustomEvent('auth-logout', {
        detail: { reason: 'user-initiated' }
      });
      window.dispatchEvent(logoutEvent);
    } catch (error) {
      console.warn('AuthService: Could not trigger logout event:', error);
    }
  }

  /**
   * Clear session-related data during logout
   */
  private clearSessionData(): void {
    try {
      // Clear session storage items
      sessionStorage.removeItem('just_logged_in');
      sessionStorage.removeItem('show_app_download_banner');
      sessionStorage.removeItem('user_preferences');
      sessionStorage.removeItem('sidebar_state');

      // Clear localStorage items related to user session
      localStorage.removeItem('app_download_banner_dismissed');
      localStorage.removeItem('model');

      // Clear any rate limiting data for this user
      const userEmail = this.userSubject.value?.email;
      if (userEmail) {
        const rateKey = `login_attempts_${this.hashString(userEmail)}`;
        localStorage.removeItem(rateKey);
      }

    } catch (error) {
      console.warn('AuthService: Error clearing session data:', error);
    }
  }

  /**
   * Simple hash function for rate limiting keys
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }
}
