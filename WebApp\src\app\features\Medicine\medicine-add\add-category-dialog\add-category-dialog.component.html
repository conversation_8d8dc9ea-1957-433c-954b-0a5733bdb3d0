<div class="dialog-container p-6 bg-white dark:bg-gray-800">
  <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
    <svg class="w-5 h-5 mr-2 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
    </svg>
    Add New Category
  </h2>
  
  <div class="mb-6">
    <label for="categoryName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
      Category Name
    </label>
    <input
      type="text"
      id="categoryName"
      [(ngModel)]="categoryName"
      (keyup.enter)="onSave()"
      (input)="showError = false"
      placeholder="Enter category name (e.g., Heart Medicine, Diabetes Care)"
      class="category-input w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"
      [class.border-red-500]="showError"
      [class.success-border]="!showError && categoryName.length > 0"
      maxlength="50"
      autocomplete="off"
    >
    <div *ngIf="showError" class="text-red-500 dark:text-red-400 text-sm mt-1 flex items-center animate-pulse">
      <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
      </svg>
      {{ errorMessage }}
    </div>
    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center">
      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      Enter a unique category name ({{ categoryName.length }}/50 characters)
    </div>
  </div>

  <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
    <button
      type="button"
      (click)="onCancel()"
      class="dialog-button px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
    >
      Cancel
    </button>
    <button
      type="button"
      (click)="onSave()"
      [disabled]="!categoryName.trim() || isSubmitting"
      class="dialog-button px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 rounded-md transition-colors disabled:bg-blue-300 dark:disabled:bg-blue-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
      [class.success-pulse]="!showError && categoryName.length > 0"
    >
      <span *ngIf="isSubmitting" class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Adding Category...
      </span>
      <span *ngIf="!isSubmitting" class="flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add Category
      </span>
    </button>
  </div>
</div>
