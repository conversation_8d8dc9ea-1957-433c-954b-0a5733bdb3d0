<div class="chat-detail-container">
  <!-- Modern Chat <PERSON>er -->
  <div class="modern-chat-header bg-white dark:!bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
    <div class="header-left">
      <button class="back-btn text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 p-2 rounded-full transition-colors duration-200 md:hidden" (click)="goBack()">
        <mat-icon class="text-xl">arrow_back</mat-icon>
      </button>
      <div class="user-info">
        <div class="avatar-container">
          <div class="avatar-circle bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold">
            <span class="avatar-text">{{ getPatientInitials() }}</span>
          </div>
          <!-- Online status dot -->
          <div class="online-dot" [class.bg-green-500]="isUserOnline" [class.bg-gray-400]="!isUserOnline"></div>
        </div>
        <div class="user-details">
          <h3 class="user-name text-gray-900 dark:text-white font-semibold">{{ getPatientName() }}</h3>
          <span class="last-seen text-gray-500 dark:text-gray-400 flex items-center gap-1.5 text-sm">
            <span class="online-indicator w-2 h-2 rounded-full" [class.bg-green-500]="isUserOnline" [class.bg-gray-400]="!isUserOnline">
            </span>
            {{ isUserOnline ? 'Online' : 'Offline' }}
          </span>
        </div>
      </div>
    </div>
    <div class="header-actions">
      <button class="modern-action-btn text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 p-2 rounded-full transition-colors duration-200 shadow-sm"
        title="View Patient Details"
        (click)="navigateToPatientDetails()"
        [disabled]="!patientId">
        <mat-icon class="text-xl">person</mat-icon>
      </button>
      <button class="modern-action-btn text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 p-2 rounded-full transition-colors duration-200 shadow-sm"
        title="Start Video call"
        [disabled]="!canStartVideoCall() || isInVideoCall()"
        (click)="startVideoCall()"
        [class.text-green-500]="isInVideoCall()"
        [class.bg-green-100]="isInVideoCall()"
        [class.dark:bg-green-900]="isInVideoCall()">
        <mat-icon class="text-xl">videocam</mat-icon>
      </button>
    </div>
  </div>

  <!-- Messages Container -->
  <div class="messages-container bg-gray-50 dark:!bg-gray-700 flex-1 overflow-y-auto" #messagesContainer>
    <!-- Loading State -->
    <div *ngIf="loading" class="loading-container flex flex-col items-center justify-center h-full p-8 text-center">
      <div class="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
      <p class="text-gray-600 dark:text-gray-300 text-sm">Loading messages...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="error && !loading" class="error-container flex flex-col items-center justify-center h-full p-8 text-center">
      <mat-icon class="text-red-500 text-4xl mb-4">error_outline</mat-icon>
      <p class="text-gray-600 dark:text-gray-300 mb-4">{{ error }}</p>
      <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200" (click)="refreshChat()">Try Again</button>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && !error && messages.length === 0" class="empty-container flex flex-col items-center justify-center h-full p-8 text-center">
      <mat-icon class="text-blue-500 text-5xl mb-4">local_hospital</mat-icon>
      <h4 class="text-gray-800 dark:text-white font-semibold text-lg mb-2">Start your conversation</h4>
      <p class="text-gray-600 dark:text-gray-300 text-sm">Send a message to your patient. They'll receive it and respond as soon as possible.</p>
    </div>

    <!-- Modern Messages List -->
    <div *ngIf="!loading && !error && messages.length > 0" class="modern-messages-list p-4 md:p-6">
      <div *ngFor="let message of messages; trackBy: trackByMessageId" class="modern-message-wrapper mb-4"
        [class.sent]="isCurrentUserMessage(message)" [class.received]="!isCurrentUserMessage(message)">

        <!-- Received Message (Patient) -->
        <div *ngIf="!isCurrentUserMessage(message)" class="message-row received flex items-end gap-2 md:gap-3 mb-1">
          <div class="message-avatar flex-shrink-0">
            <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-xs md:text-sm">
              {{ getPatientInitials() }}
            </div>
          </div>
          <div class="message-bubble bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-2xl rounded-bl-sm max-w-[75%] md:max-w-[65%] p-3 md:p-4 shadow-sm">
            <div class="message-text text-gray-900 dark:text-white text-sm md:text-base leading-relaxed">{{ message.message }}</div>
            <div class="message-meta flex justify-end mt-2">
              <span class="message-time text-gray-500 dark:text-gray-400 text-xs">{{ formatMessageTime(message.createdDate | localTime) }}</span>
            </div>
          </div>
        </div>

        <!-- Sent Message (Doctor) -->
        <div *ngIf="isCurrentUserMessage(message)" class="message-row sent flex items-end gap-2 md:gap-3 mb-1 justify-end">
          <div class="message-bubble bg-blue-500 dark:bg-blue-600 rounded-2xl rounded-br-sm max-w-[75%] md:max-w-[65%] p-3 md:p-4 shadow-sm">
            <div class="message-text text-white text-sm md:text-base leading-relaxed">{{ message.message }}</div>
            <div class="message-meta flex justify-end items-center gap-1 mt-2">
              <span class="message-time text-blue-100 text-xs">{{ formatMessageTime(message.createdDate | localTime) }}</span>
              <div class="message-status">
                <mat-icon class="text-blue-100 text-sm">done_all</mat-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modern Typing Indicator -->
    <div *ngIf="otherUserTyping" class="modern-typing-indicator flex items-end gap-2 md:gap-3 p-4 md:p-6 pb-2">
      <div class="typing-avatar flex-shrink-0">
        <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-xs md:text-sm">
          {{ getPatientInitials() }}
        </div>
      </div>
      <div class="typing-bubble bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-2xl rounded-bl-sm p-3 md:p-4 shadow-sm">
        <div class="typing-dots flex gap-1">
          <span class="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse"></span>
          <span class="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse" style="animation-delay: 0.2s"></span>
          <span class="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-pulse" style="animation-delay: 0.4s"></span>
        </div>
      </div>
    </div>
  </div>

  <!-- General Error Notification -->
  <div class="fixed bottom-20 left-4 right-4 z-50" *ngIf="connectionError && !isVideoCallVisible">
    <div class="bg-red-500 text-white p-4 rounded-lg shadow-lg flex items-center gap-3 animate-slide-up">
      <mat-icon class="text-red-100">warning</mat-icon>
      <span class="flex-1 text-sm font-medium">{{ connectionError }}</span>
    </div>
  </div>

  <!-- Modern Message Input -->
  <div class="modern-input-container bg-white dark:!bg-gray-900 border-t border-gray-200 dark:border-gray-700 p-3 md:p-4">
    <div class="input-row flex items-end gap-2 md:gap-3">
      <div class="input-field-wrapper flex-1 bg-gray-100 dark:!bg-gray-700 rounded-2xl flex items-center px-3 md:px-4 py-2 md:py-3 min-h-[44px] border-none">
        <textarea
          [(ngModel)]="newMessage"
          placeholder="Message..."
          class="modern-message-input flex-1 !bg-transparent border-none outline-none resize-none text-gray-900 dark:!text-white text-sm md:text-base leading-relaxed max-h-24 overflow-y-auto"
          rows="1"
          [disabled]="sending"
          (keypress)="onKeyPress($event)"
          (input)="onMessageInput()"
          #messageInput></textarea>
      </div>
      <button
        class="modern-send-btn bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-full p-3 md:p-3.5 transition-all duration-200 shadow-lg hover:shadow-xl disabled:shadow-none flex-shrink-0 min-w-[44px] min-h-[44px] flex items-center justify-center"
        (click)="sendMessage()"
        [disabled]="!newMessage.trim() || sending"
        [class.scale-105]="newMessage.trim() && !sending">
        <mat-icon *ngIf="!sending" class="text-lg md:text-xl">send</mat-icon>
        <mat-icon *ngIf="sending" class="text-lg md:text-xl animate-spin">autorenew</mat-icon>
      </button>
    </div>
  </div>

  <!-- Video Call Integration - Temporarily disabled -->
  <!-- <app-video-call-integration *ngIf="canStartVideoCall()" [doctor]="getDoctorParticipant()"
    [patient]="getPatientParticipant()" [currentUserId]="doctorId" [canStartCall]="canStartVideoCall()"
    (callStarted)="onVideoCallStarted($event)" (callEnded)="onVideoCallEnded()" (callError)="onVideoCallError($event)">
  </app-video-call-integration> -->

  <!-- Incoming Call Invitation -->
  <div class="incoming-call-overlay" *ngIf="isIncomingCallVisible">
    <div class="incoming-call-container">
      <div class="incoming-call-header">
        <h3>Incoming Video Call</h3>
      </div>

      <div class="caller-info">
        <div class="caller-avatar">
          <i class="fas fa-user-circle"></i>
        </div>
        <div class="caller-name">{{ videoCallParticipantName }}</div>
        <div class="call-type">Video Call</div>
      </div>

      <div class="call-actions">
        <button class="decline-btn" (click)="declineVideoCall()" title="Decline Call">
          <i class="fas fa-phone-slash"></i>
          <span>Decline</span>
        </button>

        <button class="accept-btn" (click)="acceptVideoCall()" title="Accept Call">
          <i class="fas fa-video"></i>
          <span>Accept</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Video Call Window -->
  <div class="video-call-overlay" *ngIf="isVideoCallVisible">
    <div class="video-call-container">
      <!-- Header -->
      <div class="video-call-header">
        <div class="call-title">
          <h3>Video Call with {{ videoCallParticipantName }}</h3>
          <div class="call-status-badge" [class.connected]="isParticipantJoined">
            <i class="fas fa-circle"></i>
            <span>{{ isParticipantJoined ? 'Connected' : 'Connecting...' }}</span>
          </div>
        </div>
      </div>

      <!-- Video Area -->
      <div class="video-area">
        <!-- Remote Video (Other Person) -->
        <div class="remote-video-container">
          <!-- Connection Error Alert - Show at any time when there's an error -->
          <div class="connection-error-alert" *ngIf="connectionError">
            <i class="fas fa-exclamation-triangle"></i>
            <span>{{ connectionError }}</span>
          </div>

          <!-- Waiting State -->
          <div class="waiting-message" *ngIf="!isParticipantJoined && !connectionError">
            <i class="fas fa-user-circle"></i>
            <p>Waiting for {{ videoCallParticipantName }} to join...</p>
            <small>Make sure to allow camera and microphone access</small>
          </div>

          <!-- Connected State -->
          <div *ngIf="isParticipantJoined && !connectionError" class="connected-state">
            <!-- Remote Video Stream -->
            <div class="simulated-remote-video">
              <!-- Actual Remote Video Element -->
              <video id="remoteVideo" class="remote-video" autoplay playsinline></video>

              <!-- Fallback Avatar (shown when no video stream) -->
              <div class="video-simulation video-call-avatar">
                <div class="avatar-large">
                  <i class="fas fa-user"></i>
                </div>
                <div class="video-info">
                  <h4>{{ videoCallParticipantName }}</h4>
                  <p>📹 Video call active</p>
                  <small>Connecting video stream...</small>
                </div>
              </div>
            </div>

            <!-- Participant Name Badge -->
            <div class="participant-name-badge">{{ videoCallParticipantName }}</div>

            <!-- Connection Status -->
            <div class="connection-status" [class.connected]="isWebRTCConnected">
              <i class="fas fa-circle" [class.connected-dot]="isWebRTCConnected"></i>
              <span>{{ getConnectionStatusText() }}</span>
            </div>
          </div>
        </div>

        <!-- Local Video (You) -->
        <div class="local-video-container">
          <video #localVideo class="local-video" autoplay playsinline muted></video>
          <div class="local-video-overlay" *ngIf="!isVideoEnabled">
            <i class="fas fa-user"></i>
          </div>
          <div class="local-label">You</div>
        </div>
      </div>

      <!-- Controls -->
      <div class="video-controls">
        <div class="controls-left">
          <div class="call-timer">{{ callDuration }}</div>
        </div>

        <div class="controls-center">
          <button class="control-btn mic-btn" [class.muted]="isMuted" (click)="toggleMute()">
            <i class="fas" [class.fa-microphone]="!isMuted" [class.fa-microphone-slash]="isMuted"></i>
          </button>

          <button class="control-btn camera-btn" [class.disabled]="!isVideoEnabled" (click)="toggleVideo()">
            <i class="fas" [class.fa-video]="isVideoEnabled" [class.fa-video-slash]="!isVideoEnabled"></i>
          </button>

          <button class="control-btn end-call-btn" (click)="endVideoCall()">
            <i class="fas fa-phone-slash"></i>
          </button>
        </div>

        <div class="controls-right">
          <div class="call-id-short">{{ currentVideoCallId.substring(0, 6) }}...</div>
        </div>
      </div>
    </div>
  </div>
</div>
