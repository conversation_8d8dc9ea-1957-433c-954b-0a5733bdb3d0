<div class="h-full bg-transparent !relative flex flex-col chat-list-container">
  <!-- Loading State -->
  <div *ngIf="loading" class="flex-1 flex flex-col items-center justify-center p-8 text-center">
    <div class="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
    <p class="text-gray-600 dark:text-gray-300 text-sm">Loading conversations...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="flex-1 flex flex-col items-center justify-center p-8 text-center">
    <mat-icon class="text-red-500 text-4xl mb-4">error_outline</mat-icon>
    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">{{ error }}</p>
    <button
      class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
      (click)="refreshChats()">Try Again</button>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && !error && chatThreads.length === 0"
    class="flex-1 flex flex-col items-center justify-center p-8 text-center">
    <mat-icon class="text-gray-400 dark:text-gray-500 text-5xl mb-4">local_hospital</mat-icon>
    <h3 class="text-gray-800 dark:text-white font-semibold text-lg mb-2">No conversations yet</h3>
    <p class="text-gray-600 dark:text-gray-300 text-sm mb-6">Patient messages will appear here when they contact you.</p>
    <button
      class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-xl font-medium transition-colors duration-200 flex items-center gap-2"
      (click)="startNewChat()">
      <mat-icon class="text-lg">add</mat-icon> Start New Chat
    </button>
  </div>

  <!-- Chat Threads List -->
  <div *ngIf="!loading && !error && chatThreads.length > 0" class="flex-1 overflow-y-auto mt-0">
    <div *ngFor="let thread of chatThreads; trackBy: trackByThreadId"
      class="flex items-center p-4 md:p-4 sm:p-5 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 active:bg-blue-50 dark:active:bg-blue-900/20 cursor-pointer transition-all duration-200 border-b border-gray-100 dark:border-gray-700 relative"
      [class.bg-blue-50]="hasUnreadMessages(thread)"
      [class.dark:bg-blue-900]="hasUnreadMessages(thread)"
      [class.bg-blue-100]="isThreadSelected(thread)"
      [class.dark:bg-blue-800]="isThreadSelected(thread)"
      [class.border-l-4]="isThreadSelected(thread)"
      [class.border-blue-500]="isThreadSelected(thread)"
      [class.opacity-70]="isThreadLoading(thread)"
      [class.pointer-events-none]="isThreadLoading(thread)"
      (click)="openChat(thread)">

      <!-- Avatar -->
      <div class="relative mr-3 md:mr-3 sm:mr-4 flex-shrink-0">
        <div
          class="w-12 h-12 md:w-12 md:h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm md:text-sm sm:text-base shadow-lg">
          {{ getThreadPatientInitials(thread) }}
        </div>
        <!-- Online status dot on avatar -->
        <div class="absolute bottom-0.5 right-0.5 w-3 h-3 md:w-3 md:h-3 sm:w-4 sm:h-4 border-2 border-white dark:border-gray-800 rounded-full transition-colors duration-300 shadow-sm"
          [class.bg-green-500]="isPatientOnlineById(thread.patientId)"
          [class.bg-gray-400]="!isPatientOnlineById(thread.patientId)">
        </div>
      </div>

      <!-- Content -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center justify-between mb-1 md:mb-1 sm:mb-2">
          <div class="flex items-center gap-2 min-w-0 flex-1">
            <h4 class="font-semibold text-gray-900 dark:text-white text-sm md:text-sm sm:text-base truncate">{{
              getPatientName(thread) }}
            </h4>
          </div>
          <span class="text-xs md:text-xs sm:text-sm text-gray-500 dark:text-gray-400 ml-2 flex-shrink-0">{{
            formatLastMessageDate(thread.lastMessageDate | localTime)
            }}</span>
        </div>

        <div class="flex items-center justify-between">
          <p class="text-sm md:text-sm sm:text-base text-gray-600 dark:text-gray-300 truncate flex-1 leading-relaxed">{{
            getLastMessagePreview(thread.lastMessage) }}</p>
          <div class="flex items-center gap-2 ml-2">
            <!-- Loading spinner when thread is being opened -->
            <div *ngIf="isThreadLoading(thread)"
              class="w-4 h-4 md:w-4 md:h-4 sm:w-5 sm:h-5 border border-blue-500 border-t-transparent rounded-full animate-spin">
            </div>

            <!-- Normal indicators when not loading -->
            <ng-container *ngIf="!isThreadLoading(thread)">
              <span *ngIf="hasUnreadMessages(thread)"
                class="bg-blue-500 text-white text-xs md:text-xs sm:text-sm rounded-full px-2 py-1 md:px-2 md:py-1 sm:px-3 sm:py-1 min-w-[20px] md:min-w-[20px] sm:min-w-[24px] text-center font-medium">
                {{ thread.unreadCount }}
              </span>
              <mat-icon *ngIf="!hasUnreadMessages(thread)"
                class="text-blue-500 text-sm md:text-sm sm:text-base">done_all</mat-icon>
              <mat-icon *ngIf="hasUnreadMessages(thread)"
                class="text-gray-400 dark:text-gray-500 text-sm md:text-sm sm:text-base">done</mat-icon>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Floating Action Button for New Chat (when there are existing chats) -->
  <div *ngIf="!loading && !error && chatThreads.length > 0" class="floating-action-btn">
    <button class="fab-btn" (click)="startNewChat()" title="Start new chat with patient">
      <i class="fas fa-plus"></i>
    </button>
  </div>

  <!-- New Chat Modal -->
  <div *ngIf="showNewChatModal"
    class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-end md:items-center justify-center z-50 p-0 md:p-4"
    (click)="closeNewChatModal()">
    <div
      class="bg-white dark:bg-gray-800 w-full md:max-w-lg md:w-full rounded-t-3xl md:rounded-2xl overflow-hidden shadow-2xl transform transition-all duration-300 ease-out"
      [class.animate-slide-up]="showNewChatModal" (click)="$event.stopPropagation()">

      <!-- Header -->
      <div
        class="bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-blue-900 dark:to-indigo-900 text-white p-6 md:p-6 sm:p-8 relative">
        <!-- Mobile drag indicator -->
        <div class="w-12 h-1 bg-white/30 rounded-full mx-auto md:hidden"></div>

        <div class="flex items-center justify-between mb-4">
          <div class="flex-1">
            <h3 class="text-xl md:text-xl sm:text-2xl font-bold">Select a Patient</h3>
            <p class="text-white/80 text-sm md:text-sm sm:text-base mt-1">Choose a patient to start chatting with
            </p>
          </div>
          <button
            class="text-white/80 hover:text-white p-2 md:p-2 sm:p-3 rounded-full hover:bg-white/10 transition-all duration-200 ml-4"
            (click)="closeNewChatModal()">
            <mat-icon class="text-2xl md:text-2xl sm:text-3xl">close</mat-icon>
          </button>
        </div>

        <!-- Search Input -->
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <!-- <mat-icon class="dark:!text-white/60 !text-gray-950 text-lg">search</mat-icon> -->
          </div>
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="onSearchTermChange($any($event.target).value)"
            placeholder="Search by patient name or email..."
            class="w-full pl-2 pr-10 py-3 text-gray-800 dark:bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl dark:text-white dark:placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 transition-all duration-200">
          <button
            *ngIf="searchTerm"
            (click)="clearSearch()"
            class="absolute inset-y-0 right-0 pr-3 flex items-center  text-gray-800 hover:text-gray-950 dark:text-white/60 darK:hover:text-white transition-colors duration-200">
            <mat-icon class="text-lg">clear</mat-icon>
          </button>
        </div>
      </div>

      <!-- Content -->
      <div class="max-h-[60vh] md:max-h-96 dark:bg-gray-800 overflow-y-auto">
        <!-- No patients state (when no patients available at all) -->
        <div *ngIf="availablePatients.length === 0" class="text-center py-12 md:py-12 sm:py-16 px-6 md:px-6 sm:px-8">
          <div
            class="w-16 h-16 md:w-16 md:h-16 sm:w-20 sm:h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4 md:mb-4 sm:mb-6">
            <mat-icon class="text-gray-400 text-3xl md:text-3xl sm:text-4xl">groups</mat-icon>
          </div>
          <h4 class="text-gray-800 dark:text-white font-semibold text-lg md:text-lg sm:text-xl mb-2">No patients available</h4>
          <p class="text-gray-600 dark:text-gray-300 text-sm md:text-sm sm:text-base">Please try again later or contact support</p>
        </div>

        <!-- No search results state (when search returns no results) -->
        <div *ngIf="availablePatients.length > 0 && filteredPatients.length === 0 && searchTerm" class="text-center py-12 md:py-12 sm:py-16 px-6 md:px-6 sm:px-8">
          <div
            class="w-16 h-16 md:w-16 md:h-16 sm:w-20 sm:h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4 md:mb-4 sm:mb-6">
            <mat-icon class="text-gray-400 text-3xl md:text-3xl sm:text-4xl">search_off</mat-icon>
          </div>
          <h4 class="text-gray-800 dark:text-white font-semibold text-lg md:text-lg sm:text-xl mb-2">No patients found</h4>
          <p class="text-gray-600 dark:text-gray-300 text-sm md:text-sm sm:text-base mb-4">No patients match your search criteria</p>
          <button
            (click)="clearSearch()"
            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
            Clear Search
          </button>
        </div>

        <!-- Search results info -->
        <div *ngIf="searchTerm && filteredPatients.length > 0" class="px-4 md:px-4 sm:px-6 pt-4 pb-2">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Found {{ filteredPatients.length }} patient{{ filteredPatients.length !== 1 ? 's' : '' }}
            <span *ngIf="filteredPatients.length !== availablePatients.length">
              out of {{ availablePatients.length }}
            </span>
          </p>
        </div>

        <!-- Patients list -->
        <div *ngIf="filteredPatients.length > 0" class="p-4 md:p-4 sm:p-6">
          <div *ngFor="let patient of filteredPatients; let i = index"
            class="flex items-center p-4 md:p-4 sm:p-6 rounded-2xl md:rounded-2xl sm:rounded-3xl hover:bg-blue-50 active:bg-blue-100 dark:hover:bg-blue-900/20 dark:active:bg-blue-900/30 cursor-pointer transition-all duration-200 border border-transparent hover:border-blue-100 dark:hover:border-blue-800 hover:shadow-sm group mb-3 md:mb-3 sm:mb-4 last:mb-0"
            (click)="startChatWithPatient(patient)">

            <!-- Patient Avatar -->
            <div class="relative mr-4 md:mr-4 sm:mr-6 flex-shrink-0">
              <div
                class="w-14 h-14 md:w-14 md:h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl md:rounded-2xl sm:rounded-3xl flex items-center justify-center text-white font-bold text-lg md:text-lg sm:text-xl shadow-lg">
                {{ getPatientInitials(patient) }}
              </div>
              <!-- Online status dot on avatar -->
              <div class="absolute bottom-0.5 right-0.5 w-3 h-3 md:w-3 md:h-3 sm:w-4 sm:h-4 border-2 border-white dark:border-gray-800 rounded-full transition-colors duration-300 shadow-sm"
                [class.bg-green-500]="isPatientOnline(patient.email || '')"
                [class.bg-gray-400]="!isPatientOnline(patient.email || '')">
              </div>
            </div>

            <!-- Patient Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between mb-1 md:mb-1 sm:mb-2">
                <div class="flex items-center gap-2 min-w-0 flex-1">
                  <h4
                    class="font-bold text-gray-900 dark:text-white text-base md:text-base sm:text-lg group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200 leading-tight">
                    {{ patient.fullName }}
                  </h4>
                </div>
                <mat-icon
                  class="text-blue-500 text-xl md:text-xl sm:text-2xl ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0">
                  arrow_forward
                </mat-icon>
              </div>

              <p class="text-gray-600 dark:text-gray-300 font-medium text-sm md:text-sm sm:text-base mb-2 md:mb-2 sm:mb-3">{{ patient.email }}</p>

              <div class="flex items-center gap-3">
                <span *ngIf="patient.dateOfBirth"
                  class="text-xs md:text-xs sm:text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 md:px-2 md:py-1 sm:px-3 sm:py-1 rounded-full">
                  Age: {{ calculateAge(patient.dateOfBirth) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
