import { Injectable } from '@angular/core';
import { Observable, of, throwError, BehaviorSubject } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { Role, Permission, CreateRoleRequest, UpdateRoleRequest } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class RoleService {
  private rolesSubject = new BehaviorSubject<Role[]>([]);
  public roles$ = this.rolesSubject.asObservable();

  private permissionsSubject = new BehaviorSubject<Permission[]>([]);
  public permissions$ = this.permissionsSubject.asObservable();

  constructor() {
    this.loadMockData();
  }

  private loadMockData(): void {
    // Mock permissions
    const mockPermissions: Permission[] = [
      { id: '1', name: 'Manage Users', description: 'Can create, read, update, and delete users', resource: 'user', action: 'manage', isActive: true },
      { id: '2', name: 'View Users', description: 'Can view user information', resource: 'user', action: 'read', isActive: true },
      { id: '3', name: 'Update Users', description: 'Can update user information', resource: 'user', action: 'update', isActive: true },
      { id: '4', name: 'Delete Users', description: 'Can delete users', resource: 'user', action: 'delete', isActive: true },
      { id: '5', name: 'Manage Roles', description: 'Can create, read, update, and delete roles', resource: 'role', action: 'manage', isActive: true },
      { id: '6', name: 'View Roles', description: 'Can view role information', resource: 'role', action: 'read', isActive: true },
      { id: '7', name: 'Update Roles', description: 'Can update role information', resource: 'role', action: 'update', isActive: true },
      { id: '8', name: 'Delete Roles', description: 'Can delete roles', resource: 'role', action: 'delete', isActive: true },
      { id: '9', name: 'View Dashboard', description: 'Can access dashboard', resource: 'dashboard', action: 'read', isActive: true },
      { id: '10', name: 'Manage Settings', description: 'Can manage system settings', resource: 'settings', action: 'manage', isActive: true }
    ];

    // Mock roles
    const mockRoles: Role[] = [
      {
        id: '1',
        name: 'Admin',
        description: 'System Administrator with full access',
        permissions: mockPermissions.filter(p => ['1', '5', '9', '10'].includes(p.id)),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '2',
        name: 'Manager',
        description: 'Team Manager with user management capabilities',
        permissions: mockPermissions.filter(p => ['2', '3', '6', '9'].includes(p.id)),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '3',
        name: 'User',
        description: 'Regular user with basic access',
        permissions: mockPermissions.filter(p => ['9'].includes(p.id)),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: '4',
        name: 'Support',
        description: 'Support staff with limited user access',
        permissions: mockPermissions.filter(p => ['2', '9'].includes(p.id)),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    this.permissionsSubject.next(mockPermissions);
    this.rolesSubject.next(mockRoles);
  }

  getAllRoles(): Observable<Role[]> {
    return this.roles$;
  }

  getRoleById(id: string): Observable<Role | null> {
    return this.roles$.pipe(
      map(roles => roles.find(role => role.id === id) || null)
    );
  }

  getAllPermissions(): Observable<Permission[]> {
    return this.permissions$;
  }

  createRole(roleData: CreateRoleRequest): Observable<Role> {
    return new Observable(observer => {
      setTimeout(() => {
        const permissions = this.permissionsSubject.value;
        const selectedPermissions = permissions.filter(p => roleData.permissionIds.includes(p.id));
        
        const newRole: Role = {
          id: Math.random().toString(36).substr(2, 9),
          name: roleData.name,
          description: roleData.description,
          permissions: selectedPermissions,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const roles = this.rolesSubject.value;
        
        // Check if role name already exists
        if (roles.some(role => role.name.toLowerCase() === roleData.name.toLowerCase())) {
          observer.error({ message: 'Role name already exists' });
          return;
        }

        roles.push(newRole);
        this.rolesSubject.next([...roles]);
        
        observer.next(newRole);
        observer.complete();
      }, 500);
    });
  }

  updateRole(id: string, roleData: UpdateRoleRequest): Observable<Role> {
    return new Observable(observer => {
      setTimeout(() => {
        const roles = this.rolesSubject.value;
        const roleIndex = roles.findIndex(role => role.id === id);
        
        if (roleIndex === -1) {
          observer.error({ message: 'Role not found' });
          return;
        }

        const permissions = this.permissionsSubject.value;
        const selectedPermissions = roleData.permissionIds 
          ? permissions.filter(p => roleData.permissionIds!.includes(p.id))
          : roles[roleIndex].permissions;

        const updatedRole: Role = {
          ...roles[roleIndex],
          name: roleData.name || roles[roleIndex].name,
          description: roleData.description || roles[roleIndex].description,
          permissions: selectedPermissions,
          isActive: roleData.isActive !== undefined ? roleData.isActive : roles[roleIndex].isActive,
          updatedAt: new Date()
        };

        // Check if role name already exists (excluding current role)
        if (roleData.name && roles.some(role => 
          role.id !== id && role.name.toLowerCase() === roleData.name!.toLowerCase()
        )) {
          observer.error({ message: 'Role name already exists' });
          return;
        }

        roles[roleIndex] = updatedRole;
        this.rolesSubject.next([...roles]);
        
        observer.next(updatedRole);
        observer.complete();
      }, 500);
    });
  }

  deleteRole(id: string): Observable<boolean> {
    return new Observable(observer => {
      setTimeout(() => {
        const roles = this.rolesSubject.value;
        const roleToDelete = roles.find(role => role.id === id);
        
        if (!roleToDelete) {
          observer.error({ message: 'Role not found' });
          return;
        }

        // Prevent deletion of system roles
        if (['Admin', 'User'].includes(roleToDelete.name)) {
          observer.error({ message: 'Cannot delete system roles' });
          return;
        }

        const filteredRoles = roles.filter(role => role.id !== id);
        this.rolesSubject.next(filteredRoles);
        
        observer.next(true);
        observer.complete();
      }, 500);
    });
  }

  toggleRoleStatus(id: string): Observable<Role> {
    return new Observable(observer => {
      setTimeout(() => {
        const roles = this.rolesSubject.value;
        const roleIndex = roles.findIndex(role => role.id === id);
        
        if (roleIndex === -1) {
          observer.error({ message: 'Role not found' });
          return;
        }

        const updatedRole = {
          ...roles[roleIndex],
          isActive: !roles[roleIndex].isActive,
          updatedAt: new Date()
        };

        roles[roleIndex] = updatedRole;
        this.rolesSubject.next([...roles]);
        
        observer.next(updatedRole);
        observer.complete();
      }, 500);
    });
  }

  getPermissionsByResource(resource: string): Observable<Permission[]> {
    return this.permissions$.pipe(
      map(permissions => permissions.filter(p => p.resource === resource))
    );
  }

  searchRoles(query: string): Observable<Role[]> {
    return this.roles$.pipe(
      map(roles => 
        roles.filter(role => 
          role.name.toLowerCase().includes(query.toLowerCase()) ||
          role.description.toLowerCase().includes(query.toLowerCase())
        )
      )
    );
  }
}
