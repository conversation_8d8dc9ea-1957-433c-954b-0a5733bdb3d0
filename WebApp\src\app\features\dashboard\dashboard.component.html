<div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen transition-colors duration-200">
    <!-- Loading State -->
    <div *ngIf="isLoading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"></div>
    </div>

    <!-- Dashboard Content -->
    <div *ngIf="!isLoading">
        <!-- Welcome Header -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 overflow-hidden p-4 transition-colors duration-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2 text-gray-900 dark:text-white">Welcome back, {{ currentUser?.firstName }}!</h1>
                    <p class="text-blue-600 dark:text-blue-400" *ngIf="isAdmin()">Here's your admin dashboard overview.</p>
                    <p class="text-blue-600 dark:text-blue-400" *ngIf="isDoctor()">Here's your doctor dashboard overview.</p>
                    <p class="text-blue-600 dark:text-blue-400" *ngIf="isPatient()">Here's your personal health dashboard.</p>
                </div>
                <div class="hidden md:block">
                    <div
                        class="w-20 h-20 bg-white dark:bg-gray-700 bg-opacity-20 dark:bg-opacity-30 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-2xl font-bold transition-colors duration-200">
                        {{ getUserInitials() }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Dashboard -->
        <div *ngIf="isAdmin()">
            <!-- Admin Management Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Patients -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToPatients()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Patients</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ patientStats.totalPatients }}</p>
                            <p class="text-xs text-green-600 dark:text-green-400">+{{ patientStats.newPatients }} this month</p>
                        </div>
                    </div>
                </div>

                <!-- Today's Appointments -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToAppointments()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Today's Appointments</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ appointmentStats.todayAppointments }}</p>
                            <p class="text-xs text-blue-600 dark:text-blue-400">{{ appointmentStats.pendingAppointments }} pending</p>
                        </div>
                    </div>
                </div>

                <!-- New Requests -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToRequests()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">New Requests</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ requestStats.newRequests }}</p>
                            <p class="text-xs text-orange-600 dark:text-orange-400">{{ requestStats.assignedRequests }} assigned</p>
                        </div>
                    </div>
                </div>

                <!-- Medicine Alerts (Admin Only) -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToMedicines()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Medicine Alerts</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ medicineStats.lowStockMedicines +
                                medicineStats.expiringMedicines }}</p>
                            <p class="text-xs text-red-600 dark:text-red-400">{{ medicineStats.lowStockMedicines }} low stock, {{
                                medicineStats.expiringMedicines }} expiring</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Overview Cards -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- Appointment Overview -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Appointments</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Appointments</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ appointmentStats.totalAppointments }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                            <span class="font-semibold text-green-600 dark:text-green-400">{{ appointmentStats.completedAppointments
                                }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Pending</span>
                            <span class="font-semibold text-orange-600 dark:text-orange-400">{{ appointmentStats.pendingAppointments
                                }}</span>
                        </div>
                    </div>
                </div>

                <!-- Medicine Overview -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Medicine Inventory</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Medicines</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ medicineStats.totalMedicines }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Low Stock</span>
                            <span class="font-semibold text-red-600 dark:text-red-400">{{ medicineStats.lowStockMedicines }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Inventory Value</span>
                            <span class="font-semibold text-green-600 dark:text-green-400">${{ medicineStats.totalInventoryValue | number:
                                '1.2-2' }}</span>
                        </div>
                    </div>
                </div>

                <!-- Order Overview -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Order Statistics</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Orders</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ orderStats.totalOrders }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Pending Orders</span>
                            <span class="font-semibold text-orange-600 dark:text-orange-400">{{ orderStats.pendingOrders }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Revenue</span>
                            <span class="font-semibold text-green-600 dark:text-green-400">${{ orderStats.totalRevenue | number: '1.2-2'
                                }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Doctor Dashboard -->
        <div *ngIf="isDoctor()">
            <!-- Doctor Management Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Total Patients -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToPatients()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">My Patients</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ patientStats.totalPatients }}</p>
                            <p class="text-xs text-green-600 dark:text-green-400">+{{ patientStats.newPatients }} this month</p>
                        </div>
                    </div>
                </div>

                <!-- Today's Appointments -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToAppointments()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Today's Appointments</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ appointmentStats.todayAppointments }}</p>
                            <p class="text-xs text-blue-600 dark:text-blue-400">{{ appointmentStats.pendingAppointments }} pending</p>
                        </div>
                    </div>
                </div>

                <!-- Patient Requests -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToRequests()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Patient Requests</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ requestStats.newRequests }}</p>
                            <p class="text-xs text-orange-600 dark:text-orange-400">{{ requestStats.assignedRequests }} assigned to me</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Doctor Overview Cards -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- My Appointments -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">My Appointments</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Appointments</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ appointmentStats.totalAppointments }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                            <span class="font-semibold text-green-600 dark:text-green-400">{{ appointmentStats.completedAppointments
                                }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Pending</span>
                            <span class="font-semibold text-orange-600 dark:text-orange-400">{{ appointmentStats.pendingAppointments
                                }}</span>
                        </div>
                    </div>
                </div>

                <!-- Patient Care Summary -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Patient Care Summary</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Active Patients</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ patientStats.totalPatients }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">New This Month</span>
                            <span class="font-semibold text-green-600 dark:text-green-400">{{ patientStats.newPatients }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Pending Requests</span>
                            <span class="font-semibold text-orange-600 dark:text-orange-400">{{ requestStats.newRequests }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Patient Dashboard -->
        <div *ngIf="isPatient()">
            <!-- Patient Personal Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- My Requests -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToRequests()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">My Requests</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ personalStats.myRequests }}</p>
                            <p class="text-xs text-green-600 dark:text-green-400">{{ personalStats.completedRequests }} completed</p>
                        </div>
                    </div>
                </div>

                <!-- My Appointments -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToAppointments()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">My Appointments</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ personalStats.myAppointments }}</p>
                            <p class="text-xs text-blue-600 dark:text-blue-400">{{ appointmentStats.todayAppointments }} today</p>
                        </div>
                    </div>
                </div>

                <!-- My Orders -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-xl transition-all duration-200 cursor-pointer"
                    (click)="navigateToOrders()">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">My Orders</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ personalStats.myOrders }}</p>
                            <p class="text-xs text-orange-600 dark:text-orange-400">{{ orderStats.pendingOrders }} pending</p>
                        </div>
                    </div>
                </div>

                <!-- Health Summary -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Health Status</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">Good</p>
                            <p class="text-xs text-green-600 dark:text-green-400">All up to date</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Patient Overview Cards -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- My Health Summary -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">My Health Summary</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Appointments</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ appointmentStats.totalAppointments }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                            <span class="font-semibold text-green-600 dark:text-green-400">{{ appointmentStats.completedAppointments
                                }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Upcoming</span>
                            <span class="font-semibold text-orange-600 dark:text-orange-400">{{ appointmentStats.pendingAppointments
                                }}</span>
                        </div>
                    </div>
                </div>

                <!-- My Order History -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">My Order History</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Orders</span>
                            <span class="font-semibold text-gray-900 dark:text-white">{{ orderStats.totalOrders }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Pending</span>
                            <span class="font-semibold text-orange-600 dark:text-orange-400">{{ orderStats.pendingOrders }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Spent</span>
                            <span class="font-semibold text-green-600 dark:text-green-400">${{ orderStats.totalRevenue | number: '1.2-2'
                                }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- Role-based Quick Actions -->
        <div class="mt-6 flex flex-wrap gap-3">
            <!-- Admin Actions -->
            <ng-container *ngIf="isAdmin()">
                <button (click)="navigateToPatients()"
                    class="px-4 py-2 bg-gray-100 dark:!bg-blue-950 dark:text-blue-200 text-blue-600 shadow-xl border-blue-500 border rounded-lg hover:bg-blue-200 dark:hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    Manage Patients
                </button>
                <button (click)="navigateToAppointments()"
                    class="px-4 py-2 bg-gray-100 dark:!bg-green-950  dark:text-green-200 text-green-600 border border-green-600 shadow-xl rounded-lg hover:bg-green-200 dark:hover:bg-green-600 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50">
                    Manage Appointments
                </button>
                <button (click)="navigateToMedicines()"
                    class="px-4 py-2 bg-gray-100 dark:bg-orange-950 dark:text-orange-200 text-orange-600 border border-orange-600 shadow-xl rounded-lg hover:bg-orange-200 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50">
                    Medicine Management
                </button>
                <button (click)="navigateToOrders()"
                    class="px-4 py-2 bg-gray-100 dark:bg-purple-950 dark:text-purple-200 text-purple-600 border border-purple-600 shadow-xl rounded-lg hover:bg-purple-200  transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50">
                    Order Management
                </button>
            </ng-container>

            <!-- Doctor Actions -->
            <ng-container *ngIf="isDoctor()">
                <button (click)="navigateToPatients()"
                    class="px-4 py-2 bg-gray-100 dark:!bg-blue-950 dark:text-blue-200 text-blue-600 shadow-xl border-blue-500 border rounded-lg hover:bg-blue-200 dark:hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    View My Patients
                </button>
                <button (click)="navigateToAppointments()"
                    class="px-4 py-2 bg-gray-100 dark:!bg-green-950  dark:text-green-200 text-green-600 border border-green-600 shadow-xl rounded-lg hover:bg-green-200 dark:hover:bg-green-600 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50">
                    My Appointments
                </button>
                <button (click)="navigateToRequests()"
                    class="px-4 py-2 bg-gray-100 dark:bg-purple-950 dark:text-purple-200 text-purple-600 border border-purple-600 shadow-xl rounded-lg hover:bg-purple-200  transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50">
                    Patient Requests
                </button>
                <button (click)="navigateToMedicines()"
                    class="px-4 py-2 bg-gray-100 dark:bg-orange-950 dark:text-orange-200 text-orange-600 border border-orange-600 shadow-xl rounded-lg hover:bg-orange-200 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50">
                    Medicine Catalog
                </button>
            </ng-container>

            <!-- Patient Actions -->
            <ng-container *ngIf="isPatient()">
                <button (click)="navigateToAppointments()"
                    class="px-4 py-2 bg-green-600 dark:bg-green-700 text-white rounded-lg hover:bg-green-700 dark:hover:bg-green-600 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50">
                    Book Appointment
                </button>
                <button (click)="navigateToRequests()"
                    class="px-4 py-2 bg-purple-600 dark:bg-purple-700 text-white rounded-lg hover:bg-purple-700 dark:hover:bg-purple-600 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50">
                    Submit Request
                </button>
                <button (click)="navigateToMedicines()"
                    class="px-4 py-2 bg-orange-600 dark:bg-orange-700 text-white rounded-lg hover:bg-orange-700 dark:hover:bg-orange-600 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50">
                    Browse Medicines
                </button>
                <button (click)="navigateToOrders()"
                    class="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    My Orders
                </button>
            </ng-container>
        </div>
    </div>
</div>
