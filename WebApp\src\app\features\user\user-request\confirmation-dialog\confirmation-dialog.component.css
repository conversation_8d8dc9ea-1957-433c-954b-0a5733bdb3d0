/* Dialog Container Styling */
:host ::ng-deep .mat-mdc-dialog-container {
  padding: 0;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.dialog-header {
  display: flex;
  align-items: center;
  padding: 1.25rem 1.5rem;
  color: white;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.dialog-icon {
  margin-right: 0.75rem;
}

.dialog-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.dialog-content {
  padding: 1.5rem;
  background-color: #ffffff;
}

.dialog-message {
  font-size: 1rem;
  color: #4b5563;
  margin-bottom: 1rem;
}

.item-highlight {
  background-color: #f3f4f6;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-top: 0.75rem;
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}

.item-label {
  font-weight: 600;
  color: #6b7280;
  font-size: 0.875rem;
}

.item-name {
  font-size: 0.95rem;
  color: #1f2937;
  font-weight: 500;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

/* Button Styling */
.btn {
  padding: 0.625rem 1.25rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-cancel {
  background: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-cancel:hover {
  background: #f8fafc;
  color: #334155;
}

.btn-warning {
  background: #f59e0b;
  color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.btn-warning:hover {
  background: #d97706;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.btn-danger {
  background: #ef4444;
  color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.btn-danger:hover {
  background: #dc2626;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.btn-info {
  background: #3b82f6;
  color: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.btn-info:hover {
  background: #2563eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

/* Animation - Shake effect for danger dialogs */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

:host.shake ::ng-deep .mat-dialog-container {
  animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
}

/* Fade-in animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

:host ::ng-deep .mat-dialog-container {
  animation: fadeIn 0.3s ease-out forwards;
}
