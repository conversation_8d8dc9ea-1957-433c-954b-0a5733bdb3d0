<div class=" p-5 bg-white dark:bg-gray-900 transition-colors duration-300"> <!-- <PERSON> Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-lg shadow-sm border border-blue-100 dark:border-gray-700 mb-5 p-5">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Order Details</h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
          View and manage order information
        </p>
      </div>
      <button (click)="goBack()"
        class="back-btn"
        aria-label="Go back to orders">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Orders
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="bg-white dark:bg-gray-800 rounded-lg shadow p-12 flex justify-center items-center">
    <div class="flex flex-col items-center">
      <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
      <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">Loading order details...</p>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && errorMessage" class="bg-white dark:bg-gray-800 rounded-lg shadow p-12">
    <div class="text-center">
      <svg class="mx-auto h-12 w-12 text-red-500 dark:text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
      </svg>
      <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">{{ errorMessage }}</h3>
      <div class="mt-4">
        <button (click)="goBack()"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400">
          Go Back to Orders
        </button>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && !errorMessage && order" class="fade-in">
    <!-- Merged Order and Medicine Information Card -->
    <div
      class="bg-white dark:!bg-gray-800 rounded-lg shadow overflow-hidden border border-gray-100 dark:border-gray-700 hover:shadow-lg dark:hover:!shadow-blue-900 transition-shadow duration-300 ">

      <!-- Content Sections -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 p-6">
        <!-- Left Column - Order Information -->
        <div>
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Order Information
          </h3>

          <!-- Current Status Section -->
          <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg mb-4 border border-gray-200 dark:border-gray-700">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Current Status
            </p>

            <div *ngIf="!isEditingStatus" class="mt-1 flex items-center">
              <span
                [class]="'inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium shadow-sm ' + getStatusButtonClass(order.status)">
                {{ order.status || 'Unknown' }}
              </span>


            </div>

            <!-- Quick Status Update -->
            <div class="mt-4">
              <p class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">Quick Status Update:</p>
              <div class="flex flex-wrap gap-2">
                <button *ngFor="let status of availableStatuses"
                  [disabled]="status === order.status || isUpdatingStatus || isStatusDisabled(status)"
                  (click)="orderDatausUpdate(order.id, status)" [class]="'flex items-center text-xs px-3 py-1.5 rounded-md border shadow-sm transition-all ' +
                  (status === order.status ?
                    'bg-gray-100 dark:bg-gray-900 text-gray-500 dark:text-gray-400 border-gray-300 dark:border-gray-700 cursor-not-allowed opacity-60' :
                    isStatusDisabled(status) ?
                      'bg-gray-100 dark:bg-gray-900 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-700 cursor-not-allowed opacity-50' :
                      getStatusButtonClass(status) + ' hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-800 dark:hover:text-blue-300')" [attr.aria-label]="'Update status to ' + status"
                  [attr.aria-pressed]="status === order.status"
                  [attr.aria-disabled]="status === order.status || isUpdatingStatus || isStatusDisabled(status)"
                  [attr.title]="isStatusDisabled(status) ? 'Cannot update to ' + status + ' from current status' : ''">
                  <svg *ngIf="status === 'Pending'" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <svg *ngIf="status === 'Processing'" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <svg *ngIf="status === 'Shipped'" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
                  </svg>
                  <svg *ngIf="status === 'Delivered'" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                  <svg *ngIf="status === 'Cancelled'" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  {{ status }}
                </button>
              </div>
            </div>


          </div>

          <!-- Order Info Grid -->
          <div class="grid grid-cols-1 gap-4">
            <!-- Order Date -->
            <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Order Date
              </p>
              <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ formatDate(order.orderDate) }}</p>
            </div>

            <!-- Patient Email -->
            <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Patient Email
              </p>
              <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ order.patientEmail }}</p>
            </div>

            <!-- Patient Name -->
            <div *ngIf="order.patientName" class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Patient Name
              </p>
              <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ order.patientName }}</p>
            </div>

            <!-- Patient Phone -->
            <div *ngIf="order.patientPhone" class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                Patient Phone
              </p>
              <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ order.patientPhone }}</p>
            </div>

            <!-- Delivery Address -->
            <div *ngIf="order.deliveryAddress" class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Delivery Address
              </p>
              <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ order.deliveryAddress }}</p>
            </div>

            <!-- Special Request/Notes -->
            <div *ngIf="order.notes" class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                  viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Special Request
              </p>
              <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ order.notes }}</p>
            </div>
          </div>
        </div>

        <!-- Right Column - Medicine Information -->
        <div>
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
            </svg>
            Medicine Information
          </h3>

          <!-- Loading Medicine Details -->
          <div *ngIf="medicines.length === 0 && isLoading"
            class="bg-gray-50 dark:bg-gray-900 p-6 rounded-lg flex justify-center items-center border border-gray-200 dark:border-gray-700">
            <div class="animate-pulse flex flex-col items-center w-full">
              <div class="rounded-md bg-gray-200 h-6 w-3/4 mb-4"></div>
              <div class="rounded-md bg-gray-200 h-4 w-1/2 mb-3"></div>
              <div class="rounded-md bg-gray-200 h-4 w-2/3 mb-3"></div>
              <div class="rounded-md bg-gray-200 h-4 w-1/2 mb-3"></div>
              <div class="rounded-md bg-gray-200 h-4 w-3/4 mb-3"></div>
            </div>
          </div>

          <!-- No Medicine Details -->
          <div *ngIf="medicines.length === 0 && !isLoading" class="bg-gray-50 dark:bg-gray-900 p-6 rounded-lg text-center border border-gray-200 dark:border-gray-700">
            <p class="text-gray-500 dark:text-gray-400">No medicine details available for this order.</p>
          </div>

          <!-- Medicine Details -->
          <div *ngIf="medicines.length > 0" class="space-y-6 fade-in">
            <div *ngFor="let medicine of medicines; let i = index" class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800">
              <h4 *ngIf="medicines.length > 1" class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Medicine {{ i + 1 }}</h4>
              <div class="grid grid-cols-1 gap-4">
                <!-- Medicine Name -->
                <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                      viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Name
                  </p>
                  <p class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-medium">{{ medicine.name }}</p>
                </div>

                <!-- Medicine Category -->
                <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                      viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    Category
                  </p>
                  <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ medicine.category || 'Not specified' }}</p>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <!-- Medicine Price -->
                  <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Price
                    </p>
                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100 font-medium">${{ medicine.price !== undefined ?
                      medicine.price.toFixed(2) : 'Not specified' }}</p>
                  </div>

                  <!-- Stock Quantity -->
                  <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                      In Stock
                    </p>
                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ medicine.stockQuantity || 0 }} units</p>
                  </div>
                </div>

                <!-- Expiry Date -->
                <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                      viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Expiry Date
                  </p>
                  <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ formatDate(medicine.expiryDate) }}</p>
                </div>

                <!-- Medicine Description -->
                <div *ngIf="medicine.description" class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none"
                      viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Description
                  </p>
                  <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">{{ medicine.description }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Status Workflow Indicator -->
          <div class="mt-6 mb-2 px-6">
            <p class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-3">Order Status Workflow:</p>
            <div class="relative">
              <div class="overflow-hidden h-2 mb-1 text-xs flex rounded bg-gray-200 dark:bg-gray-700">
                <div [style]="'width:' + getStatusProgressPercentage() + '%'" [class]="getStatusProgressColorClass()">
                </div>
              </div>
              <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                <div class="w-1/4 text-center" [class.font-semibold]="order.status === 'Pending'"
                  [class.text-amber-600]="order.status === 'Pending'">
                  Pending
                </div>
                <div class="w-1/4 text-center" [class.font-semibold]="order.status === 'Processing'"
                  [class.text-blue-600]="order.status === 'Processing'">
                  Processing
                </div>
                <div class="w-1/4 text-center" [class.font-semibold]="order.status === 'Shipped'"
                  [class.text-purple-600]="order.status === 'Shipped'">
                  Shipped
                </div>
                <div class="w-1/4 text-center" [class.font-semibold]="order.status === 'Delivered'"
                  [class.text-green-600]="order.status === 'Delivered'">
                  Delivered
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
