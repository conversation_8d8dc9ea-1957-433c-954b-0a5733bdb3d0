import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService, Theme } from '../../services/theme.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-theme-toggle',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="theme-toggle-container">
      <!-- Theme Toggle Button -->
      <button
        (click)="toggleDropdown()"
        class="theme-toggle-button flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
        [attr.aria-expanded]="showDropdown"
        aria-haspopup="true">

        <!-- Theme Icon -->
        <div class="theme-icon mr-3 h-4 w-4 text-gray-400 dark:text-gray-500">
          <!-- Light Mode Icon -->
          <svg *ngIf="currentTheme === 'light'"
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
          </svg>

          <!-- Dark Mode Icon -->
          <svg *ngIf="currentTheme === 'dark'"
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
          </svg>

          <!-- Auto Mode Icon -->
          <svg *ngIf="currentTheme === 'auto'"
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
        </div>

        <span class="flex-1 text-left">Theme</span>
        <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">{{ getThemeDisplayName(currentTheme) }}</span>

        <!-- Dropdown Arrow -->
        <svg class="h-4 w-4 text-gray-400 dark:text-gray-500 transition-transform duration-200"
             [class.rotate-180]="showDropdown"
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
        </svg>
      </button>

      <!-- Theme Options Dropdown -->
      <div *ngIf="showDropdown"
           class="theme-dropdown absolute left-0 right-0 mt-1 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 py-1 z-50">

        <button *ngFor="let theme of availableThemes"
                (click)="selectTheme(theme)"
                class="theme-option flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                [class.bg-blue-50]="theme === currentTheme && !isDarkMode"
                [class.dark:bg-blue-900]="theme === currentTheme && isDarkMode"
                [class.text-blue-700]="theme === currentTheme && !isDarkMode"
                [class.dark:text-blue-300]="theme === currentTheme && isDarkMode">

          <!-- Theme Option Icon -->
          <div class="theme-option-icon mr-3 h-4 w-4">
            <!-- Light Icon -->
            <svg *ngIf="theme === 'light'"
                 fill="none" stroke="currentColor" viewBox="0 0 24 24"
                 class="text-yellow-500">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
            </svg>

            <!-- Dark Icon -->
            <svg *ngIf="theme === 'dark'"
                 fill="none" stroke="currentColor" viewBox="0 0 24 24"
                 class="text-blue-400">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
            </svg>

            <!-- Auto Icon -->
            <svg *ngIf="theme === 'auto'"
                 fill="none" stroke="currentColor" viewBox="0 0 24 24"
                 class="text-gray-500 dark:text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
          </div>

          <span class="flex-1 text-left">{{ getThemeDisplayName(theme) }}</span>

          <!-- Check Icon for Selected Theme -->
          <svg *ngIf="theme === currentTheme"
               class="h-4 w-4 text-blue-600 dark:text-blue-400"
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
          </svg>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .theme-toggle-container {
      position: relative;
    }

    .theme-toggle-button {
      border: none;
      background: none;
      cursor: pointer;
    }

    .theme-toggle-button:focus {
      outline: none;
    }

    .theme-dropdown {
      animation: fadeIn 0.15s ease-out;
    }

    .theme-option {
      border: none;
      background: none;
      cursor: pointer;
    }

    .theme-option:focus {
      outline: none;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(-5px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .theme-icon,
    .theme-option-icon {
      flex-shrink: 0;
    }
  `]
})
export class ThemeToggleComponent implements OnInit, OnDestroy {
  currentTheme: Theme = 'light';
  availableThemes: Theme[] = ['light', 'dark', 'auto'];
  showDropdown = false;
  isDarkMode = false;

  private themeSubscription?: Subscription;

  constructor(private themeService: ThemeService) {}

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeSubscription = this.themeService.currentTheme$.subscribe(theme => {
      this.currentTheme = theme;
      this.isDarkMode = this.themeService.isDarkMode();
    });

    // Get available themes
    this.availableThemes = this.themeService.getAvailableThemes();
  }

  ngOnDestroy(): void {
    if (this.themeSubscription) {
      this.themeSubscription.unsubscribe();
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = target.closest('.theme-toggle-container');
    if (!dropdown) {
      this.showDropdown = false;
    }
  }

  toggleDropdown(): void {
    this.showDropdown = !this.showDropdown;
  }

  selectTheme(theme: Theme): void {
    this.themeService.setTheme(theme);
    this.showDropdown = false;
  }

  getThemeDisplayName(theme: Theme): string {
    return this.themeService.getThemeDisplayName(theme);
  }
}
