/**
 * Simple Link Embed Tool for EditorJS
 * Allows users to embed any URL as an iframe or link
 */
export class LinkEmbedTool {
  static get toolbox() {
    return {
      title: 'Embed Link',
      icon: '<svg width="17" height="15" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 1L13 7H20L14 11L16 18L10 14L4 18L6 11L0 7H7L10 1Z" fill="currentColor"/></svg>'
    };
  }

  static get isReadOnlySupported() {
    return true;
  }

  constructor({ data, api, readOnly }: any) {
    this.api = api;
    this.readOnly = readOnly;
    this.data = {
      url: data.url || '',
      title: data.title || '',
      embedType: data.embedType || 'iframe' // 'iframe' or 'link'
    };
    this.wrapper = null;
  }

  render() {
    this.wrapper = document.createElement('div');
    this.wrapper.classList.add('link-embed-tool');

    this.updateContent();
    this.addEventListeners();

    return this.wrapper;
  }

  updateContent() {
    if (!this.wrapper) return;

    if (this.data.url) {
      this.wrapper.innerHTML = this.createEmbedHTML();
    } else {
      this.wrapper.innerHTML = this.createInputHTML();
    }
  }

  addEventListeners() {
    if (!this.wrapper) return;

    const input = this.wrapper.querySelector('.link-url-input') as HTMLInputElement;
    const button = this.wrapper.querySelector('.link-embed-btn') as HTMLButtonElement;
    const editBtn = this.wrapper.querySelector('.link-edit-btn') as HTMLButtonElement;
    const typeSelect = this.wrapper.querySelector('.embed-type-select') as HTMLSelectElement;

    if (input && button) {
      button.addEventListener('click', () => {
        const url = input.value.trim();
        if (url && this.isValidUrl(url)) {
          this.data.url = url;
          if (typeSelect) {
            this.data.embedType = typeSelect.value;
          }
          this.updateContent();
          this.addEventListeners();
        } else {
          alert('Please enter a valid URL');
        }
      });

      input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          button.click();
        }
      });
    }

    if (editBtn) {
      editBtn.addEventListener('click', () => {
        this.data.url = '';
        this.updateContent();
        this.addEventListeners();
      });
    }
  }

  createInputHTML() {
    return `
      <div class="link-input-container border border-dashed border-gray-300 dark:border-gray-600 p-6 text-center rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 shadow-sm hover:shadow-md transition-all duration-300">
        <div class="flex items-center justify-center mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-sm">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div>
            <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100 mb-1">Embed Link</h3>
            <p class="text-xs text-gray-600 dark:text-gray-400">Add any URL to embed or link</p>
          </div>
        </div>

        <input
          type="text"
          placeholder="Paste any URL here (YouTube, Vimeo, website, etc.)"
          class="link-url-input w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg mb-4 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200"
        />

        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">Embed Type:</label>
          <select class="embed-type-select px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm min-w-40 cursor-pointer focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400">
            <option value="iframe">Embed as iframe</option>
            <option value="link">Show as link</option>
          </select>
        </div>

        <button
          class="link-embed-btn bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
        >
          Embed Content
        </button>
      </div>
    `;
  }

  createEmbedHTML() {
    if (!this.data.url) {
      return this.createInputHTML();
    }

    if (this.data.embedType === 'link') {
      const domain = new URL(this.data.url).hostname.replace('www.', '');
      return `
        <div class="link-embed-wrapper border border-gray-200 dark:border-gray-700 p-5 rounded-xl my-4 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-sm hover:shadow-md transition-all duration-300 relative hover:-translate-y-0.5">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-sm">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div>
              <h4 class="text-gray-900 dark:text-gray-100 font-semibold text-base mb-1">${this.data.title || 'External Link'}</h4>
              <p class="text-gray-500 dark:text-gray-400 text-sm font-medium">${domain}</p>
            </div>
          </div>
          <a href="${this.data.url}" target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-white font-medium text-sm bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 px-4 py-2.5 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5 no-underline">
            Visit Link
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="ml-2">
              <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </a>
          ${!this.readOnly ? `<button class="link-edit-btn absolute top-3 right-3 bg-gray-800 dark:bg-gray-700 bg-opacity-80 hover:bg-opacity-100 text-white border-0 px-2.5 py-1.5 rounded-md cursor-pointer text-xs font-medium transition-all duration-200 backdrop-blur-sm">Edit</button>` : ''}
        </div>
      `;
    } else {
      // iframe embed
      const domain = new URL(this.data.url).hostname.replace('www.', '');
      return `
        <div class="link-embed-wrapper relative w-full my-4 rounded-xl overflow-hidden shadow-lg bg-white dark:bg-gray-800 hover:shadow-xl transition-all duration-300">
          <div class="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 px-4 py-3 flex items-center justify-between border-b border-gray-200 dark:border-gray-600">
            <div class="flex items-center">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span class="text-gray-700 dark:text-gray-300 font-medium text-sm">Embedded Content</span>
            </div>
            <span class="text-gray-500 dark:text-gray-400 text-xs font-mono bg-white dark:bg-gray-900 px-2 py-1 rounded border border-gray-200 dark:border-gray-600">${domain}</span>
          </div>
          <iframe
            src="${this.data.url}"
            width="100%"
            height="400"
            frameborder="0"
            class="w-full block border-0 bg-white dark:bg-gray-900"
            allowfullscreen>
          </iframe>
          ${!this.readOnly ? `<button class="link-edit-btn absolute top-4 right-4 bg-gray-800 dark:bg-gray-700 bg-opacity-80 hover:bg-opacity-100 text-white border-0 px-2.5 py-1.5 rounded-md cursor-pointer text-xs font-medium transition-all duration-200 backdrop-blur-sm">Edit</button>` : ''}
        </div>
      `;
    }
  }

  isValidUrl(string: string): boolean {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  save() {
    return this.data;
  }

  validate(savedData: any) {
    return savedData.url && savedData.url.trim() !== '';
  }

  static get sanitize() {
    return {
      url: {},
      title: {},
      embedType: {}
    };
  }

  onPaste(event: any) {
    const text = event.clipboardData?.getData('text');
    if (text && this.isValidUrl(text)) {
      this.data.url = text;
      return true;
    }
    return false;
  }

  private api: any;
  private readOnly: boolean;
  private wrapper: HTMLElement | null;
  private data: {
    url: string;
    title: string;
    embedType: string;
  };
}

export default LinkEmbedTool;
