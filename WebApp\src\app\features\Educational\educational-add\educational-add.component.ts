import { <PERSON>mponent, OnInit, ViewChild, ElementRef, On<PERSON><PERSON>roy, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, NgForm } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { EducationalServiceProxy, Educational, FileServiceProxy, FileParameter, ResponseMessage, MedicineServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { DateTime } from 'luxon';
import { AuthService } from '../../../../shared/services/auth.service';
import { EditorJsService } from '../../../shared/services/editor-js.service';

// EditorJS and its plugins
import EditorJ<PERSON> from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';
import Paragraph from '@editorjs/paragraph';
import ImageTool from '@editorjs/image';
import YouTubeEmbedTool from '../../../shared/services/youtube-embed.tool';
import LinkEmbedTool from '../../../shared/services/link-embed.tool';

// Define interface for EditorJS output
interface EditorJSData {
  time: number;
  blocks: any[];
  version: string;
}

@Component({
  selector: 'app-educational-add',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatSnackBarModule
  ],
  templateUrl: './educational-add.component.html',
  styleUrl: './educational-add.component.css'
})
export class EducationalAddComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('educationalForm') educationalForm!: NgForm;
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('editorContainer') editorContainer!: ElementRef;

  // Base URL for API calls
  baseUrl: string = getRemoteServiceBaseUrl();

  // EditorJS instance
  private editor: EditorJS | null = null;

  educational = new Educational();
  isLoading = false;
  isEditing = false;
  educationalId?: string;
  pageTitle = 'Add New Educational Content';
  editorReady = false;
  hasEditorContent = false; // Track if editor has content for validation

  // File upload properties
  selectedFile: File | null = null;
  isUploading = false;
  uploadError = '';
  imagePreview: string | ArrayBuffer | null = null;
  previousImageName: string | null = null;
  isImageLoading = false;
  imageLoadError = false;
  categoryOptions: string[] | undefined;

  // Category options (can be expanded)


  constructor(
    private _educationalService: EducationalServiceProxy,
    private _fileService: FileServiceProxy,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar,
    private authService: AuthService,
    private editorJsService: EditorJsService,
    private mediaService: MedicineServiceProxy

  ) { }

  ngOnInit(): void {
    this.mediaService.categories().subscribe((response) => {
      this.categoryOptions = response;
    });


    // Initialize default values for a new educational item
    this.educational.createDate = DateTime.now();

    // Get current user information
    const currentUser = this.authService.getUser();
    if (currentUser) {
      // Access properties using index notation since they come from token claims
      this.educational.createdBy = currentUser['unique_name'] || currentUser['email'] || 'Unknown User';
    }

    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.educationalId = params['id'];
        this.isEditing = true;
        this.pageTitle = 'Edit Educational Content';
        if (this.educationalId) {
          this.loadEducationalDetails(this.educationalId);
        }
      }
    });
  }

  ngAfterViewInit(): void {
    // Initialize EditorJS after the view is ready
    setTimeout(async () => {
      await this.initEditor();
    }, 100);
  }
  /**
   * Initialize the EditorJS instance
   */
  private async initEditor(): Promise<void> {
    if (!this.editorContainer) {
      console.error('Editor container not found');
      return;
    }

    try {
      this.editor = new EditorJS({
        holder: this.editorContainer.nativeElement,
        placeholder: 'Write educational content here...',
        tools: {
          header: Header,
          list: List,
          paragraph: {
            class: Paragraph as any,
            inlineToolbar: true
          },
          youtube: YouTubeEmbedTool,
          linkEmbed: LinkEmbedTool,
          image: {
            class: ImageTool as any,
            config: {
              uploader: {
                uploadByFile: async (file: File) => {
                  try {
                    // Create a FileParameter object for the upload
                    const fileParam: FileParameter = {
                      data: file,
                      fileName: file.name
                    };

                    // Return a Promise that resolves with the image URL
                    return new Promise((resolve, reject) => {
                      this._fileService.upload('educational', [fileParam]).subscribe({
                        next: (response: ResponseMessage) => {
                          if (response && response.message) {
                            const uploadedFilenames = response.message
                              .replace('Files uploaded: ', '')
                              .split(', ')
                              .filter(name => name.trim().length > 0);

                            if (uploadedFilenames.length > 0) {
                              const imageUrl = `${this.baseUrl}/api/File/Getfile/${uploadedFilenames[0]}`;
                              resolve({
                                success: 1,
                                file: {
                                  url: imageUrl
                                }
                              });
                            } else {
                              reject('No filename returned');
                            }
                          } else {
                            reject('Upload succeeded but no filename was returned');
                          }
                        },
                        error: (error) => {
                          reject(error);
                        }
                      });
                    });
                  } catch (error) {
                    console.error('Error uploading image:', error);
                    return {
                      success: 0,
                      message: 'Image upload failed'
                    };
                  }
                }
              }
            }
          }
        },
        onReady: () => {
          this.editorReady = true;
          console.log('EditorJS is ready');

          // If we're in edit mode and have content, parse it into EditorJS format
          if (this.isEditing && this.educational.content) {
            this.setEditorContent(this.educational.content);
          }

          // Check initial content state
          this.checkEditorContent();
        },
        onChange: (api: any, event: any) => {
          // Update content validation when editor changes
          this.checkEditorContent();
        },
        autofocus: !this.isEditing,
      });
    } catch (error) {
      console.error('Error initializing EditorJS:', error);
    }
  }

  /**
   * Save EditorJS content to the educational model before form submission
   */
  private async saveEditorContent(): Promise<void> {
    if (!this.editor) {
      return;
    }

    try {
      const editorData = await this.editor.save();

      // Store the raw EditorJS JSON for future editing
      this.educational.content = JSON.stringify(editorData);

      // For display in other parts of the application, you could also
      // store the HTML version using the editorJsService
      // this.educational.contentHtml = this.editorJsService.convertToHtml(editorData);
    } catch (error) {
      console.error('Failed to save editor content', error);
      this.snackBar.open('Error saving editor content', 'Close', { duration: 3000 });
    }
  }
  /**
   * Set content in EditorJS from stored HTML/JSON
   */
  private setEditorContent(content: string): void {
    if (!this.editor || !content) {
      return;
    }

    try {
      // Try to parse the content as EditorJS JSON
      const parsedData = JSON.parse(content);

      // Check if it's valid EditorJS data format
      if (parsedData && parsedData.blocks) {
        const editorData = {
          time: parsedData.time || Date.now(),
          blocks: parsedData.blocks,
          version: parsedData.version || '2.25.0'
        };

        // Render content using EditorJS
        this.editor.render(editorData);
      } else {
        // If not in EditorJS format, create a single paragraph block with the content
        const editorData = {
          time: Date.now(),
          blocks: [{
            type: 'paragraph',
            data: {
              text: content
            }
          }],
          version: '2.25.0'
        };

        this.editor.render(editorData);
      }
    } catch (e) {
      // If content is not valid JSON, try to parse it as HTML
      try {
        const editorData = this.editorJsService.convertFromHtml(content);
        this.editor.render(editorData);

        // Check content after rendering
        setTimeout(() => {
          this.checkEditorContent();
        }, 100);
      } catch (htmlError) {
        // If HTML parsing fails too, create a simple paragraph block
        const editorData = {
          time: Date.now(),
          blocks: [{
            type: 'paragraph',
            data: {
              text: content
            }
          }],
          version: '2.25.0'
        };

        this.editor.render(editorData);

        // Check content after rendering
        setTimeout(() => {
          this.checkEditorContent();
        }, 100);
      }
    }
  }

  ngOnDestroy(): void {
    // Clean up EditorJS instance
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
    }
  }

  loadEducationalDetails(id: string): void {
    this.isLoading = true;
    this._educationalService.getEducationalById(id).subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data) {
          this.educational = data;

          // Store original image name for comparison during update
          if (this.educational.featuredImg) {
            this.previousImageName = this.educational.featuredImg;
            this.loadImage(this.educational.featuredImg);
          }

          // If editing, we need to restore the EditorJS content
          if (this.isEditing && this.educational.content) {
            this.restoreEditorContent(this.educational.content);
          }
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Error loading educational content details', 'Close', { duration: 3000 });
        console.error('Error loading educational:', error);
      }
    });
  }

  restoreEditorContent(content: string): void {
    // Parse the content string back to object
    let parsedData: EditorJSData;
    try {
      parsedData = JSON.parse(content);
    } catch (e) {
      console.error('Error parsing content for EditorJS:', e);
      return;
    }

    // Restore content to EditorJS
    // Let the render method handle updating the blocks
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
      this.createImagePreview(this.selectedFile);
      this.uploadFile();
    }
  }

  createImagePreview(file: File): void {
    if (!file.type.startsWith('image/')) {
      this.uploadError = 'Please select an image file';
      return;
    }

    const reader = new FileReader();
    reader.onload = () => {
      this.imagePreview = reader.result;
    };
    reader.readAsDataURL(file);
  }

  loadImage(filename: string): void {
    // Reset image states
    this.isImageLoading = true;
    this.imageLoadError = false;

    // Create full URL for image with baseUrl
    const imageUrl = `${this.baseUrl}/api/File/Getfile/${filename}`;

    // Fetch the image
    fetch(imageUrl)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to load image: ${response.status} ${response.statusText}`);
        }
        return response.blob();
      })
      .then(blob => {
        // Create a URL for the blob
        const objectUrl = URL.createObjectURL(blob);
        this.imagePreview = objectUrl;
        this.isImageLoading = false;
      })
      .catch(error => {
        console.error('Error loading image:', error);
        this.imageLoadError = true;
        this.isImageLoading = false;
      });
  }

  uploadFile(): void {
    if (!this.selectedFile) {
      return;
    }

    this.isUploading = true;
    this.uploadError = '';

    // Create a FileParameter object for the upload
    const fileParam: FileParameter = {
      data: this.selectedFile,
      fileName: this.selectedFile.name
    };

    // Upload the file
    this._fileService.upload('educational', [fileParam]).subscribe({
      next: (response: ResponseMessage) => {
        this.isUploading = false;
        if (response && response.message) {
          // Assuming the response contains the saved filename
          // Expected format is something like "Files uploaded: filename1.jpg, filename2.jpg"
          const uploadedFilenames = response.message
            .replace('Files uploaded: ', '')
            .split(', ')
            .filter(name => name.trim().length > 0);

          if (uploadedFilenames.length > 0) {
            // Use the first uploaded filename (should be our image)
            this.educational.featuredImg = uploadedFilenames[0];
            this.snackBar.open('Image uploaded successfully', 'Close', { duration: 3000 });
          }
        } else {
          this.uploadError = 'Upload succeeded but no filename was returned';
        }
      },
      error: (error) => {
        this.isUploading = false;
        this.uploadError = 'Failed to upload image: ' + (error.message || 'Unknown error');
        this.snackBar.open('Failed to upload image', 'Close', { duration: 3000 });
      }
    });
  }

  /**
   * Handle image load errors in previews
   */
  onImageError(event: Event): void {
    this.imageLoadError = true;
    const img = event.target as HTMLImageElement;

    // Replace with a generic SVG icon
    img.src = `data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 24 24" fill="none" stroke="%239CA3AF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>`;

    img.onerror = null; // Prevent infinite loop
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  removeImage(): void {
    this.selectedFile = null;
    this.imagePreview = null;
    this.educational.featuredImg = undefined;
  }

  async saveEducational(): Promise<void> {
    if (this.isUploading) {
      this.snackBar.open('Please wait for the image to finish uploading', 'Close', { duration: 3000 });
      return;
    }

    if (!this.editorReady) {
      this.snackBar.open('Editor is not ready yet. Please try again in a moment.', 'Close', { duration: 3000 });
      return;
    }

    if (!this.educational.category) {
      this.snackBar.open('Please select a category', 'Close', { duration: 3000 });
      return;
    }

    // Save editor content to the educational model
    await this.saveEditorContent();

    if (!this.educational.content) {
      this.snackBar.open('Please add some content', 'Close', { duration: 3000 });
      return;
    }

    this.isLoading = true;

    // Calculate reading time if not provided
    if (!this.educational.readingTime) {
      this.educational.readingTime = this.calculateReadingTime(this.educational.content);
    }

    // Ensure createdBy is set
    if (!this.educational.createdBy) {
      const currentUser = this.authService.getUser();
      if (currentUser) {
        this.educational.createdBy = currentUser['unique_name'] || currentUser['email'] || 'Unknown User';
      } else {
        this.educational.createdBy = 'Unknown User';
      }
    }

    // Set creation or update date
    if (!this.isEditing) {
      this.educational.createDate = DateTime.now();
    } else {
      this.educational.updateDate = DateTime.now();
    }

    this._educationalService.addOrEditEducational(this.educational).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.snackBar.open(
          this.isEditing ? 'Educational content updated successfully!' : 'Educational content added successfully!',
          'Close',
          { duration: 3000 }
        );
        this.router.navigate(['/educational/list']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Error saving educational content', 'Close', { duration: 3000 });
        console.error('Error saving educational:', error);
      }
    });
  }

  /**
   * Check if editor has content for validation
   */
  private checkEditorContent(): void {
    if (!this.editor) {
      this.hasEditorContent = false;
      return;
    }

    // Use a promise-based approach to avoid blocking
    this.editor.save().then((outputData) => {
      // Check if there are any blocks with actual content
      this.hasEditorContent = outputData.blocks && outputData.blocks.length > 0 &&
        outputData.blocks.some(block => {
          if (block.type === 'paragraph') {
            return block.data.text && block.data.text.trim().length > 0;
          }
          if (block.type === 'header') {
            return block.data.text && block.data.text.trim().length > 0;
          }
          if (block.type === 'list') {
            return block.data.items && block.data.items.length > 0;
          }
          if (block.type === 'image') {
            return block.data.file && block.data.file.url;
          }
          if (block.type === 'embed') {
            return block.data.embed || block.data.source;
          }
          if (block.type === 'youtube') {
            return block.data.url;
          }
          if (block.type === 'linkEmbed') {
            return block.data.url;
          }
          return true; // For other block types, assume they have content
        });

      // Update the hidden form field to trigger validation
      if (this.hasEditorContent) {
        this.educational.content = JSON.stringify(outputData);
      } else {
        this.educational.content = '';
      }
    }).catch((error) => {
      console.error('Error checking editor content:', error);
      this.hasEditorContent = false;
    });
  }

  /**
   * Check if the form is valid for submission
   */
  isFormValid(): boolean {
    return this.editorReady &&
      this.hasEditorContent &&
      !!this.educational.category &&
      !this.isUploading &&
      !this.isLoading;
  }

  calculateReadingTime(content: string | undefined): string {
    if (!content) return '1 ';

    let plainText = '';

    try {
      // Try to parse as EditorJS JSON
      const editorContent = JSON.parse(content);
      if (editorContent && editorContent.blocks) {
        // Extract text from each block
        plainText = editorContent.blocks
          .map((block: any) => {
            switch (block.type) {
              case 'paragraph':
              case 'header':
                return block.data.text ? block.data.text.replace(/<[^>]*>/g, '') : '';
              case 'list':
                return block.data.items ? block.data.items.join(' ') : '';
              case 'embed':
                // Add some reading time for embedded content (like videos)
                return 'embedded content ';
              case 'youtube':
                // Add reading time for YouTube videos (assume 2-3 minutes average)
                return 'video content ';
              case 'linkEmbed':
                // Add reading time for embedded links
                return 'embedded link ';
              default:
                return '';
            }
          })
          .join(' ');
      } else {
        // Just remove HTML tags as fallback
        plainText = content.replace(/<[^>]*>/g, '');
      }
    } catch (e) {
      // If not valid JSON, just remove HTML tags
      plainText = content.replace(/<[^>]*>/g, '');
    }

    const words = plainText.trim().split(/\s+/).length;

    // Average reading speed: 200 words per minute
    const minutes = Math.max(1, Math.round(words / 200));

    return `${minutes}`;
  }

  goBack(): void {
    this.router.navigate(['/educational/list']);
  }

  /**
   * Manual method to test editor functionality
   */
  testEditor(): void {
    if (!this.editor) {
      console.log('Editor not initialized');
      return;
    }

    this.editor.save().then((outputData) => {
      console.log('Editor content:', outputData);
      console.log('Has content:', this.hasEditorContent);
      console.log('Form valid:', this.isFormValid());
    }).catch((error) => {
      console.error('Error testing editor:', error);
    });
  }

  /**
   * Add sample content for testing
   */
  addSampleContent(): void {
    if (!this.editor || !this.editorReady) {
      this.snackBar.open('Editor not ready', 'Close', { duration: 3000 });
      return;
    }

    const sampleData = {
      time: Date.now(),
      blocks: [
        {
          id: "sample1",
          type: "header",
          data: {
            text: "Sample Educational Content",
            level: 2
          }
        },
        {
          id: "sample2",
          type: "paragraph",
          data: {
            text: "This is a sample paragraph to test the EditorJS functionality. You can edit this content and add more blocks using the toolbar."
          }
        },
        {
          id: "sample3",
          type: "list",
          data: {
            style: "unordered",
            items: [
              "First point about health",
              "Second important tip",
              "Third piece of advice"
            ]
          }
        }
      ],
      version: "2.25.0"
    };

    this.editor.render(sampleData).then(() => {
      this.snackBar.open('Sample content added', 'Close', { duration: 3000 });
      setTimeout(() => {
        this.checkEditorContent();
      }, 100);
    }).catch((error) => {
      console.error('Error adding sample content:', error);
      this.snackBar.open('Error adding sample content', 'Close', { duration: 3000 });
    });
  }
}
