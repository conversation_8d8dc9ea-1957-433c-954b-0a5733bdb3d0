import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import {
  MedicineServiceProxy,
  Medicine,
} from '../../../../shared/service-proxies/service-proxies';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { FormsModule } from '@angular/forms';
import { DateTime } from 'luxon';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AuthService } from '../../../../shared/services/auth.service';
import { ConfirmationDialogComponent } from '../../user/user-request/confirmation-dialog/confirmation-dialog.component';
import { getRemoteServiceBaseUrl } from '../../../app.config';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

@Component({
  selector: 'app-medicine-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './medicine-list.component.html',
  styleUrl: './medicine-list.component.css',
})
export class MedicineListComponent implements OnInit, OnDestroy {
  Math = Math;
  baseUrl = getRemoteServiceBaseUrl();
  medicines: Medicine[] = [];
  filteredData: Medicine[] = [];
  displayData: Medicine[] = [];
  isLoading = true;
  errorMessage = '';
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  totalPages = 1;
  paginationRange: number[] = [];
  sortColumn = 'name';
  sortDirection = true;
  selectedCategory = '';
  searchQuery = '';
  categories: string[] = [];
  readonly pageSizeOptions = [5, 10, 20, 50];

  private destroy$ = new Subject<void>();
  private searchSubject = new Subject<string>();

  constructor(
    private _medicineService: MedicineServiceProxy,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private authService: AuthService
  ) {
    // Setup search debouncing
    this.searchSubject
      .pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe(() => this.applyFilters());
  }

  ngOnInit(): void {
    if (this.authService.hasRole('User')) {
      this.snackBar.open(
        'Access denied. Admin or Doctor privileges required.',
        'Close',
        { duration: 3000 }
      );
      this.router.navigate(['/dashboard']);
      return;
    }
    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadData(): void {
    this.isLoading = true;
    this._medicineService
      .getAll()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result: Medicine[]) => {
          result.forEach((medicine) => {
            // Only construct full URL if it's a relative path and not already a full URL
            if (medicine.imageUrl &&
                medicine.imageUrl.trim() !== '' &&
                !medicine.imageUrl.includes('://') &&
                !medicine.imageUrl.startsWith(this.baseUrl) &&
                !medicine.imageUrl.startsWith('/assets/')) {
              medicine.imageUrl = `${this.baseUrl}/api/File/Getfile/${medicine.imageUrl}`;
            } else if (medicine.imageUrl && medicine.imageUrl.trim() === '') {
              // Clear empty image URLs
              medicine.imageUrl = undefined;
            }
          });
          this.medicines = result;
          this.filteredData = result;
          this.updatePagination();
          this.isLoading = false;
          this.loadCategories();
        },
        error: (error) => {
          this.errorMessage = 'Failed to load medicines. Please try again.';
          this.isLoading = false;
          console.error('Error loading medicines:', error);
        },
      });
  }

  private loadCategories(): void {
    this._medicineService
      .categories()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (categories) => (this.categories = categories || []),
        error: () =>
          (this.categories = [
            ...new Set(this.medicines.map((m) => m.category).filter(Boolean)),
          ]),
      });
  }

  getLowStockCount = (): number =>
    this.medicines.filter((m) => m.stockQuantity <= 10).length;
  getExpiringSoonCount = (): number =>
    this.medicines.filter(
      (m) =>
        m.expiryDate &&
        DateTime.fromJSDate(new Date(m.expiryDate.toString())).diff(
          DateTime.now(),
          'days'
        ).days <= 30 &&
        DateTime.fromJSDate(new Date(m.expiryDate.toString())).diff(
          DateTime.now(),
          'days'
        ).days > 0
    ).length;
  getExpiredCount = (): number =>
    this.medicines.filter(
      (m) =>
        m.expiryDate &&
        DateTime.fromJSDate(new Date(m.expiryDate.toString())).diff(
          DateTime.now(),
          'days'
        ).days <= 0
    ).length;
  getUniqueCategories = (): number =>
    new Set(this.medicines.map((m) => m.category)).size;
  getFormattedDate = (date: DateTime | undefined): Date | null =>
    date ? new Date( date.toString()) : null;
  getExpiryDateClasses = (expiryDate: DateTime): string => {
    if (!expiryDate) return 'text-gray-500';
    const days = DateTime.fromJSDate(new Date(expiryDate.toString())).diff(
      DateTime.now(),
      'days'
    ).days;
    return days <= 0
      ? 'text-red-600 '
      : days <= 30
      ? 'text-amber-600 font-medium'
      : 'text-green-600';
  };
  getStockLevelClasses = (stockQuantity: number): string =>
    stockQuantity <= 0
      ? 'text-red-600 font-medium'
      : stockQuantity <= 10
      ? 'text-amber-600 font-medium'
      : 'text-green-600 font-medium';
  filterByCategory = (event: any): void => {
    this.selectedCategory = event.target.value;
    this.applyFilters();
  };

  clearCategory = (): void => {
    this.selectedCategory = '';
    this.applyFilters();
  };

  clearSearch = (): void => {
    this.searchQuery = '';
    this.applyFilters();
  };

  applyFilter = (event: Event): void => {
    this.searchQuery = (event.target as HTMLInputElement).value;
    this.searchSubject.next(this.searchQuery);
  };

  private applyFilters(): void {
    const query = this.searchQuery.toLowerCase();
    this.filteredData = this.medicines.filter((medicine) => {
      const matchesSearch =
        !query ||
        medicine.name?.toLowerCase().includes(query) ||
        medicine.category?.toLowerCase().includes(query) ||
        medicine.description?.toLowerCase().includes(query);
      const matchesCategory =
        !this.selectedCategory || medicine.category === this.selectedCategory;
      return matchesSearch && matchesCategory;
    });
    this.currentPage = 1;
    this.updatePagination();
  }

  sortData = (column: string): void => {
    this.sortDirection =
      this.sortColumn === column ? !this.sortDirection : true;
    this.sortColumn = column;

    this.filteredData.sort((a, b) => {
      const getValue = (item: Medicine, col: string): any => {
        const value = item[col as keyof Medicine];
        if (col === 'expiryDate' || col === 'manufacturerDate') {
          return value
            ? DateTime.fromJSDate(new Date(value.toString())).toMillis()
            : 0;
        }
        if (col === 'price' || col === 'stockQuantity')
          return (value as number) || 0;
        return String(value || '').toLowerCase();
      };

      const aVal = getValue(a, column);
      const bVal = getValue(b, column);
      const result =
        typeof aVal === 'string' ? aVal.localeCompare(bVal) : aVal - bVal;
      return this.sortDirection ? result : -result;
    });

    this.updatePagination();
  };
  private updatePagination(): void {
    this.totalItems = this.filteredData.length;
    this.totalPages = Math.ceil(this.totalItems / this.pageSize) || 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;
    this.calculatePaginationRange();
    this.updateDisplayedData();
  }

  private calculatePaginationRange(): void {
    const maxPages = 5,
      half = Math.floor(maxPages / 2);
    let start = Math.max(1, this.currentPage - half);
    let end = Math.min(this.totalPages, start + maxPages - 1);
    if (end === this.totalPages) start = Math.max(1, end - maxPages + 1);
    this.paginationRange = Array.from(
      { length: end - start + 1 },
      (_, i) => start + i
    );
  }

  private updateDisplayedData(): void {
    const start = (this.currentPage - 1) * this.pageSize;
    this.displayData = this.filteredData.slice(start, start + this.pageSize);
  }

  goToPage = (page: number): void => {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.updateDisplayedData();
      document
        .querySelector('.items-table')
        ?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  previousPage = (): void => {
    if (this.currentPage > 1) this.goToPage(this.currentPage - 1);
  };
  nextPage = (): void => {
    if (this.currentPage < this.totalPages) this.goToPage(this.currentPage + 1);
  };

  onPageSizeChange = (event: Event): void => {
    this.pageSize = parseInt((event.target as HTMLSelectElement).value, 10);
    this.currentPage = 1;
    this.updatePagination();
  };

  addNewMedicine = (): void => {
    this.router.navigate(['/medicine/add']);
  };
  editMedicine = (medicine: Medicine): void => {
    this.router.navigate(['/medicine/edit', medicine.id]);
  };

  private showSnackbar = (message: string, isError = false): void => {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      horizontalPosition: 'end',
      verticalPosition: 'top',
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
    });
  };

  deleteMedicine = (medicine: Medicine): void => {
    this.dialog
      .open(ConfirmationDialogComponent, {
        width: '400px',
        data: {
          type: 'danger',
          title: 'Delete Medicine',
          message:
            'Are you sure you want to delete this medicine? This action cannot be undone.',
          itemName: medicine.name,
          confirmText: 'Delete',
          cancelText: 'Cancel',
        },
      })
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this._medicineService
            .delete(medicine.id)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: () => {
                this.showSnackbar('Medicine deleted successfully');
                this.loadData();
              },
              error: () => this.showSnackbar('Failed to delete medicine', true),
            });
        }
      });
  };



  onImageLoad = (event: Event): void => {
    const loading = (event.target as HTMLImageElement).previousElementSibling;
    if (loading?.classList.contains('image-loading'))
      loading.classList.add('hidden');
  };

  onImageError = (event: Event): void => {
    const img = event.target as HTMLImageElement;
    const container = img.parentElement;
    if (container) {
      // Hide the loading indicator
      const loading = container.querySelector('.image-loading');
      if (loading) loading.classList.add('hidden');
      
      // Hide the failed image
      img.style.display = 'none';
      
      // Show fallback icon
      const fallback = container.querySelector('.fallback-icon') as HTMLElement;
      if (fallback) {
        fallback.style.display = 'flex';
      } else {
        // Create fallback if it doesn't exist
        const fallbackDiv = document.createElement('div');
        fallbackDiv.className = 'fallback-icon h-10 w-10 rounded-lg bg-gray-100 dark:bg-gray-900 flex items-center justify-center';
        fallbackDiv.innerHTML = `
          <svg class="h-6 w-6 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
          </svg>
        `;
        container.appendChild(fallbackDiv);
      }
    }
  };

  exportData = (): void => {
    if (!this.filteredData.length)
      return this.showSnackbar('No data to export', true);

    const csv = [
      'Name,Category,Stock Quantity,Price,Expiry Date,Description',
      ...this.filteredData.map((m) =>
        [
          m.name ? `"${m.name.replace(/"/g, '""')}"` : '',
          m.category ? `"${m.category.replace(/"/g, '""')}"` : '',
          m.stockQuantity ?? '',
          m.price ?? '',
          m.expiryDate
            ? new Date(m.expiryDate.toString()).toLocaleDateString()
            : '',
          m.description ? `"${m.description.replace(/"/g, '""')}"` : '',
        ].join(',')
      ),
    ].join('\n');

    const link = document.createElement('a');
    link.href = URL.createObjectURL(new Blob([csv], { type: 'text/csv' }));
    link.download = `medicine-inventory-${new Date()
      .toISOString()
      .slice(0, 10)}.csv`;
    link.click();
    this.showSnackbar('Export complete');
  };
}
