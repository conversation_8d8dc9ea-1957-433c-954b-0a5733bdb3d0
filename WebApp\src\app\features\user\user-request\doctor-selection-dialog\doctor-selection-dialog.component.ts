import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';

interface Doctor {
  email: string;
  name: string;
}

@Component({
  selector: 'app-doctor-selection-dialog',
  standalone: true,
  imports: [CommonModule, FormsModule, MatDialogModule],
  templateUrl: './doctor-selection-dialog.component.html',
  styleUrls: ['./doctor-selection-dialog.component.css']
})
export class DoctorSelectionDialogComponent implements OnInit {
  doctors: Doctor[] = [];
  selectedDoctor: Doctor | null = null;
  searchQuery: string = '';
  loading: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<DoctorSelectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  ngOnInit(): void {
    this.doctors = this.data.doctors || [];
    this.loading = false;
  }

  selectDoctor(doctor: Doctor): void {
    this.selectedDoctor = doctor;
  }

  confirm(): void {
    if (this.selectedDoctor) {
      this.dialogRef.close(this.selectedDoctor);
    }
  }

  cancel(): void {
    this.dialogRef.close(null);
  }

  get filteredDoctors(): Doctor[] {
    if (!this.searchQuery) return this.doctors;

    const query = this.searchQuery.toLowerCase();
    return this.doctors.filter(doctor =>
      doctor.name.toLowerCase().includes(query) ||
      doctor.email.toLowerCase().includes(query)
    );
  }
}
