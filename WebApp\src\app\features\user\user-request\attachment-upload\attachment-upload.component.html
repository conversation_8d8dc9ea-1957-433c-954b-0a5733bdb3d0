<div class="dialog-header bg-gradient-to-r from-indigo-600 to-blue-600 dark:!from-gray-800 dark:!to-gray-900 text-white dark:!text-gray-100 px-5 py-4 rounded-t-lg shadow-md">
  <h2 class="dialog-title text-xl font-semibold">{{ dialogTitle }}</h2>
</div>

<div class="dialog-content bg-white dark:!bg-gray-900 p-6 rounded-b-lg">
  <form class="attachment-form space-y-6">
    <!-- File Upload Section -->
    <div class="file-upload-section" [class.has-file]="fileSelected">
      <div class="file-upload-area border-2 border-dashed border-gray-300 dark:!border-gray-600 rounded-lg p-8 text-center hover:bg-gray-50 dark:hover:!bg-gray-800 transition-colors duration-200" *ngIf="!fileSelected">
        <svg class="upload-icon mx-auto mb-4 text-gray-500 dark:!text-gray-400" xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="17 8 12 3 7 8"></polyline>
          <line x1="12" y1="3" x2="12" y2="15"></line>
        </svg>
        <p class="upload-message text-gray-700 dark:!text-gray-200">
          <span class="upload-text block mb-2">Drag & drop your file here or</span>
          <label for="file-upload" class="browse-btn text-indigo-600 dark:!text-indigo-400 font-medium cursor-pointer underline hover:text-indigo-700 dark:!hover:text-indigo-300">Browse Files</label>
        </p>
        <p class="file-hint text-sm text-gray-500 dark:!text-gray-400">Supported formats: PDF, JPEG, PNG, DOC, DOCX</p>
        <input type="file" id="file-upload" class="file-input hidden" (change)="onFileSelected($event)" accept=".pdf,.jpeg,.jpg,.png,.doc,.docx">
      </div>

      <div class="selected-file-info space-y-4" *ngIf="fileSelected">
        <div class="file-preview flex items-center gap-4 p-4 bg-gray-50 dark:!bg-gray-800 border border-gray-200 dark:!border-gray-700 rounded-lg">
          <div class="file-icon">
            <svg *ngIf="fileType === 'pdf'" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#ef4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 dark:!text-red-400">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
            </svg>
            <svg *ngIf="fileType === 'image'" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#3b82f6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500 dark:!text-blue-400">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <circle cx="8.5" cy="8.5" r="1.5"></circle>
              <polyline points="21 15 16 10 5 21"></polyline>
            </svg>
            <svg *ngIf="fileType === 'doc'" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4f46e5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-indigo-500 dark:!text-indigo-400">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
            </svg>
          </div>
          <div class="file-details flex-1">
            <h4 class="file-name text-base font-medium text-gray-900 dark:!text-gray-100">{{ fileName }}</h4>
            <span class="file-size text-sm text-gray-500 dark:!text-gray-400">{{ fileSize }}</span>
          </div>
          <button type="button" class="remove-file-btn text-red-500 dark:!text-red-400 hover:text-red-600 dark:!hover:text-red-300 p-2" (click)="removeFile()">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="file-type-warning flex items-center gap-2 text-red-600 dark:!text-red-400 text-sm p-2 bg-white dark:!bg-gray-900 rounded" *ngIf="!isValidFileType()">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
          <span>Unsupported file format. Please use PDF, JPEG, PNG, DOC or DOCX.</span>
        </div>
      </div>
    </div>
  </form>
</div>

<div class="dialog-actions flex justify-end gap-2 px-6 py-4 bg-gray-50 dark:!bg-gray-800 border-t border-gray-200 dark:!border-gray-700">
  <button class="btn btn-cancel px-4 py-2 rounded bg-white dark:!bg-gray-800 text-gray-700 dark:!text-gray-200 border border-gray-200 dark:!border-gray-600 hover:bg-gray-100 dark:!hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:!focus:ring-indigo-400 transition-colors duration-200" [disabled]="isUploading" (click)="onCancel()">
    Cancel
  </button>
  <button class="btn btn-primary px-4 py-2 rounded bg-gradient-to-r from-indigo-600 to-blue-600 dark:!from-indigo-500 dark:!to-blue-500 text-white font-medium hover:from-indigo-700 hover:to-blue-700 dark:!hover:from-indigo-600 dark:!hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:!focus:ring-indigo-400 transition-colors duration-200 disabled:bg-gray-300 dark:!disabled:bg-gray-600 disabled:text-gray-500 dark:!disabled:text-gray-400 disabled:cursor-not-allowed" [disabled]="!fileSelected || !isValidFileType() || isUploading" (click)="onSubmit()">
    <div class="flex items-center">
      <span *ngIf="!isUploading">Upload</span>
      <span *ngIf="isUploading" class="flex items-center">
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Uploading...
      </span>
    </div>
  </button>
</div>
