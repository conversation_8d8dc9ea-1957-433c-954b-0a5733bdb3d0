import { Injectable } from '@angular/core';
import { HubConnection, HubConnectionBuilder, HubConnectionState, LogLevel } from '@microsoft/signalr';
import { Subject, BehaviorSubject, Observable } from 'rxjs';
import { ChatMessageResponseDto } from '../../shared/service-proxies/service-proxies';
import { GlobalMessageNotificationService } from './global-message-notification.service';
import { getRemoteServiceBaseUrl } from '../app.config';

export interface TypingNotification {
  userEmail: string;
  userName: string;
  isTyping: boolean;
  timestamp: Date;
}

export interface MessageNotification {
  message: ChatMessageResponseDto;
  threadId: string;
  senderName: string;
  chatPartner: string;
}

export interface MessageReadNotification {
  userId: string;
  doctorId: string;
  patientId: string;
  timestamp: Date;
}

export interface ChatThreadCreatedEvent {
  doctorId: string;
  patientId: string;
  threadId: string;
  createdAt: Date;
}

export interface ChatConnectionStatus {
  isConnected: boolean;
  isConnecting: boolean;
  connectionState: HubConnectionState;
}

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private hubConnection: HubConnection | null = null;
  private connectionState = new BehaviorSubject<HubConnectionState>(HubConnectionState.Disconnected);
  private isConnecting = false;
  private connectionPromise: Promise<void> | null = null;

  // Subjects for real-time events
  private messageSubject = new Subject<ChatMessageResponseDto>();
  private typingSubject = new Subject<TypingNotification>();
  private messageNotificationSubject = new Subject<MessageNotification>();
  private messageReadSubject = new Subject<MessageReadNotification>();
  private chatThreadCreatedSubject = new Subject<ChatThreadCreatedEvent>();

  // Public observables
  public connectionState$ = this.connectionState.asObservable();
  public message$ = this.messageSubject.asObservable();
  public typing$ = this.typingSubject.asObservable();
  public messageNotification$ = this.messageNotificationSubject.asObservable();
  public messageRead$ = this.messageReadSubject.asObservable();
  public chatThreadCreated$ = this.chatThreadCreatedSubject.asObservable();

  apiUrl: string = "";

  constructor(private globalMessageService: GlobalMessageNotificationService) {
    // Auto-reconnect on page visibility change
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.hubConnection?.state === HubConnectionState.Disconnected) {
        this.connect();
      }
    });
    this.apiUrl = getRemoteServiceBaseUrl();
  }

  /**
   * Establishes a connection to the DoctorPatientChatHub
   * @returns Promise that resolves when the connection is established
   */
  async connect(): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      return Promise.resolve();
    }

    if (this.isConnecting && this.connectionPromise) {
      return this.connectionPromise;
    }

    // Check if user is authenticated before attempting connection
    const token = this.getTokenFromCookie();
    if (!token) {
      console.warn('⚠️ ChatService: Cannot connect - no authentication token found');

      throw new Error('Authentication required. Please login first.');
    }

    this.isConnecting = true;
    this.connectionPromise = this.establishConnection();

    try {
      await this.connectionPromise;
    } finally {
      this.isConnecting = false;
      this.connectionPromise = null;
    }
  }

  private async establishConnection(): Promise<void> {
    try {
      // Get JWT token from cookies
      const token = this.getTokenFromCookie();

      // If no token, don't attempt connection
      if (!token) {
        throw new Error('No authentication token found. Please login first.');
      }

      this.hubConnection = new HubConnectionBuilder()
        .withUrl(`${this.apiUrl}/doctorpatientchathub`, {
          accessTokenFactory: () => {
            const currentToken = this.getTokenFromCookie();
            return currentToken || '';
          },
          withCredentials: false,
          transport: 1,
          skipNegotiation: true
        })
        .withAutomaticReconnect([0, 2000, 10000, 30000])
        .configureLogging(LogLevel.Information)
        .build();

      this.setupEventHandlers();

      await this.hubConnection.start();
      this.connectionState.next(this.hubConnection.state);


    } catch (error) {
      console.error('❌ DoctorPatientChatHub Connection Error:', error);
      this.connectionState.next(HubConnectionState.Disconnected);

      // Check if it's an authentication error
      const errorMessage = error instanceof Error ? error.message : String(error);
      const isAuthError = errorMessage.includes('401') ||
        errorMessage.includes('Unauthorized') ||
        errorMessage.includes('authentication') ||
        errorMessage.includes('token');

      if (isAuthError) {
        console.error('🔐 Authentication Error: Please check if you are logged in');

        // Don't retry on auth errors
        throw new Error('Authentication failed. Please login again.');
      } else {
        // Retry connection after 5 seconds for non-auth errors

        setTimeout(() => this.connect(), 5000);
      }

      throw error;
    }
  }

  private setupEventHandlers(): void {
    if (!this.hubConnection) return;

    // Handle connection state changes
    this.hubConnection.onreconnecting(() => {
      this.connectionState.next(HubConnectionState.Reconnecting);

    });

    this.hubConnection.onreconnected(() => {
      this.connectionState.next(HubConnectionState.Connected);

    });

    this.hubConnection.onclose(() => {
      this.connectionState.next(HubConnectionState.Disconnected);

    });

    // Handle real-time events
    this.hubConnection.on('ReceiveNewMessage', (message: ChatMessageResponseDto) => {
      // push to stream for components
      this.messageSubject.next(message);
      // update global badges/toasts
      try { this.globalMessageService.handleNewMessage(message); } catch { }
    });

    this.hubConnection.on('UserTyping', (notification: TypingNotification) => {

      this.typingSubject.next(notification);
    });

    this.hubConnection.on('NewMessageNotification', (notification: MessageNotification) => {
      this.messageNotificationSubject.next(notification);
      try { this.globalMessageService.handleMessageNotification(notification); } catch { }
    });

    this.hubConnection.on('MessagesMarkedAsRead', (notification: MessageReadNotification) => {
      this.messageReadSubject.next(notification);
      try { this.globalMessageService.handleMessagesMarkedAsRead(notification.doctorId, notification.patientId); } catch { }
    });

    // Subscribe to chat thread creation notifications
    this.hubConnection.on('ChatThreadCreated', (data: any) => {
      console.log('🆕 Chat thread created event received:', data);
      this.chatThreadCreatedSubject.next(data);
    });

    // Handle chat list updates for real-time list synchronization
    this.hubConnection.on('ChatListUpdate', (data: any) => {
      console.log('📋 WebApp Doctor Chat list update received:', data);
      this.chatThreadCreatedSubject.next({
        doctorId: data.doctorId,
        patientId: data.patientId,
        threadId: `${data.doctorId}-${data.patientId}`,
        createdAt: new Date()
      });
    });
  }

  /**
   * Disconnects the SignalR connection
   * @returns Promise that resolves when the connection is stopped
   */
  async disconnect(): Promise<void> {
    if (this.hubConnection) {
      try {
        await this.hubConnection.stop();
        this.connectionState.next(HubConnectionState.Disconnected);

      } catch (error) {
        console.error('Error stopping SignalR connection:', error);
        throw error;
      } finally {
        this.isConnecting = false;
        this.connectionPromise = null;
      }
    }
  }

  // Chat thread management methods
  async joinChatThread(doctorId: string, patientId: string): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('JoinChatThread', doctorId, patientId);

      } catch (error) {
        console.error('❌ Error joining chat thread:', error);
        throw error;
      }
    } else {
      console.warn('⚠️ Cannot join chat thread - SignalR not connected');
    }
  }

  async leaveChatThread(doctorId: string, patientId: string): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('LeaveChatThread', doctorId, patientId);

      } catch (error) {
        console.error('❌ Error leaving chat thread:', error);
        throw error;
      }
    }
  }

  async sendTypingNotification(doctorId: string, patientId: string, userName: string, isTyping: boolean): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('NotifyTyping', doctorId, patientId, userName, isTyping);
      } catch (error) {
        console.error('❌ Error sending typing notification:', error);
      }
    }
  }

  async markMessagesAsRead(doctorId: string, patientId: string, userId: string): Promise<void> {
    if (this.hubConnection?.state === HubConnectionState.Connected) {
      try {
        await this.hubConnection.invoke('MarkMessagesAsRead', doctorId, patientId, userId);

      } catch (error) {
        console.error('❌ Error marking messages as read:', error);
        throw error;
      }
    }
  }

  // Connection status methods
  isConnected(): boolean {
    return this.hubConnection?.state === HubConnectionState.Connected;
  }

  getConnectionState(): HubConnectionState {
    return this.hubConnection?.state || HubConnectionState.Disconnected;
  }

  getConnectionStatus(): ChatConnectionStatus {
    return {
      isConnected: this.isConnected(),
      isConnecting: this.isConnecting,
      connectionState: this.getConnectionState()
    };
  }

  // Authentication status methods
  isAuthenticated(): boolean {
    const token = this.getTokenFromCookie();
    return token !== null && token.length > 0;
  }

  getAuthenticationStatus(): { isAuthenticated: boolean; hasToken: boolean; tokenLength: number } {
    const token = this.getTokenFromCookie();
    return {
      isAuthenticated: this.isAuthenticated(),
      hasToken: token !== null,
      tokenLength: token ? token.length : 0
    };
  }

  /**
   * Get JWT token from cookies (same method as AuthService)
   */
  private getTokenFromCookie(): string | null {
    try {
      const TOKEN_KEY = 'jwt_token';
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, ...valueParts] = cookie.trim().split('=');
        if (name === TOKEN_KEY) {
          const value = valueParts.join('=');
          if (value && value.length > 0) {
            return value;
          }
        }
      }
      return null;
    } catch (error) {
      console.error('ChatService: Error reading token from cookies:', error);
      return null;
    }
  }
}
