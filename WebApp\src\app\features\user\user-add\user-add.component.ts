import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgForm, FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { RoleService } from '../../../services/role.service';
import { ButtonComponent } from '../../../shared/components/button/button.component';
import { AuthServiceProxy, RegisterDto } from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-user-add',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ButtonComponent,
    ServiceProxyModule
  ],
  templateUrl: './user-add.component.html',
  styleUrls: ['./user-add.component.css']
})
export class UserAddComponent implements OnD<PERSON>roy {
  @ViewChild('userForm') userForm!: NgForm;

  // User model for template-driven form
  user = new RegisterDto();
  availableRoles: string[] = [];
  isSubmitting = false;
  errorMessage = '';
  successMessage = '';
  passwordStrength: { score: number; feedback: string[] } | null = null;

  // Unsubscribe subject for memory leak prevention
  private destroy$ = new Subject<void>();

  // Skill/Role mapping for the predefined roles
  private skillsByRole: { [key: string]: string[] } = {
    'Admin': ['System Administration', 'User Management', 'Data Management', 'Security Management'],
    'Doctor': ['Medical Consultation', 'Patient Care', 'Diagnosis', 'Treatment Planning'],
    'User': ['Patient Registration', 'Basic Data Entry', 'Profile Management']
  };

  constructor(
    private authService: AuthServiceProxy,
    private router: Router
  ) { }



  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }



  onPasswordChange = (password: string): void => {
    this.passwordStrength = password ? this.checkPasswordStrength(password) : null;
  }

  private checkPasswordStrength = (password: string): { score: number; feedback: string[] } => {
    const checks = [
      [password.length >= 8, 'Password should be at least 8 characters long'],
      [/[a-z]/.test(password), 'Password should contain lowercase letters'],
      [/[A-Z]/.test(password), 'Password should contain uppercase letters'],
      [/\d/.test(password), 'Password should contain numbers'],
      [/[!@#$%^&*(),.?":{}|<>]/.test(password), 'Password should contain special characters']
    ] as [boolean, string][];

    return {
      score: checks.filter(([valid]) => valid).length,
      feedback: checks.filter(([valid]) => !valid).map(([, msg]) => msg)
    };
  }


  onRoleSelect = (event: any): void => {
    this.user.role = event.target.value;
    this.user.skills = this.user.role ? (this.skillsByRole[this.user.role]?.join(', ') || '') : '';
  }

  private colorMaps = {
    role: { 'Admin': 'bg-red-100 text-red-800', 'Doctor': 'bg-blue-100 text-blue-800', 'User': 'bg-green-100 text-green-800' },
    skill: { 'System': 'bg-red-100 text-red-800', 'User': 'bg-purple-100 text-purple-800', 'Data': 'bg-indigo-100 text-indigo-800',
             'Security': 'bg-gray-100 text-gray-800', 'Medical': 'bg-blue-100 text-blue-800', 'Patient': 'bg-sky-100 text-sky-800',
             'Diagnosis': 'bg-cyan-100 text-cyan-800', 'Treatment': 'bg-teal-100 text-teal-800', 'Registration': 'bg-emerald-100 text-emerald-800',
             'Basic': 'bg-green-100 text-green-800', 'Profile': 'bg-lime-100 text-lime-800' }
  };

  getRoleColorClass = (roleName: string): string => this.colorMaps.role[roleName as keyof typeof this.colorMaps.role] || 'bg-gray-100 text-gray-800';
  getSkillColorClass = (skill: string): string => this.colorMaps.skill[skill.split(' ')[0] as keyof typeof this.colorMaps.skill] || 'bg-blue-100 text-blue-800';

  onSubmit = (): void => {
    if (!this.userForm.valid || !this.user.role || this.isSubmitting) return;

    this.isSubmitting = true;
    this.errorMessage = this.successMessage = '';

    this.authService.register(new RegisterDto({
      name: this.user.name, email: this.user.email, password: this.user.password,
      role: this.user.role, skills: this.user.skills
    })).pipe(takeUntil(this.destroy$)).subscribe({
      next: () => {
        this.isSubmitting = false;
        this.successMessage = `User "${this.user.name}" has been created successfully!`;
        setTimeout(() => this.router.navigate(['/users']), 2000);
      },
      error: (error) => {
        this.isSubmitting = false;
        const errorMap = { 400: 'Invalid user data. Please check all fields.', 409: 'A user with this email already exists.',
                          422: 'Password does not meet security requirements.' };
        this.errorMessage = errorMap[error.status as keyof typeof errorMap] || error.message || 'Failed to create user. Please try again.';
      }
    });
  }

  onCancel = (): void => { this.router.navigate(['/users']); };

  getFieldError = (fieldName: string): string => {
    const control = this.userForm?.form.get(fieldName);
    if (!control?.touched || !control.invalid) return '';

    const labels = { name: 'Name', email: 'Email', password: 'Password', role: 'Role', skills: 'Skills' };
    const label = labels[fieldName as keyof typeof labels] || fieldName;

    if (control.hasError('required')) return `${label} is required`;
    if (control.hasError('email')) return 'Please enter a valid email address';
    if (control.hasError('minlength')) return `${label} must be at least ${control.getError('minlength').requiredLength} characters`;
    return '';
  }

  getPasswordStrengthText = (): string =>
    this.passwordStrength ? (['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'][Math.min(this.passwordStrength.score, 4)] || 'Very Weak') : '';
}
