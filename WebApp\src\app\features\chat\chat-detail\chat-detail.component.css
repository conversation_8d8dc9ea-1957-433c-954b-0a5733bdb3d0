/* Modern Responsive Chat Detail Component */
.chat-detail-container {
  height: 100%;
  height: 100dvh; /* Dynamic viewport height for mobile */
  display: flex;
  flex-direction: column;
  background: #ffffff;
  position: relative;
  padding-bottom: env(safe-area-inset-bottom);
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

/* Dark mode for chat detail container */
.dark .chat-detail-container {
  background: #1f2937;
}

/* Modern Chat Header */
.modern-chat-header {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  min-height: 60px;
  flex-shrink: 0;
  position: relative;
  z-index: 10;
  width: 100%;
  background-color: inherit;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.back-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.user-info {
  display: flex !important;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.avatar-container {
  position: relative;
  flex-shrink: 0;
}

.avatar-container .avatar-circle {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 50%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.avatar-container .avatar-text {
  color: white;
  font-weight: 600;
  font-size: 16px;
  line-height: 1;
  text-transform: uppercase;
}

.online-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s ease;
  z-index: 2;
}

.online-dot.online {
  background: #22c55e;
}

.online-dot.offline {
  background: #6b7280;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-seen {
  font-size: 13px;
  margin-top: 2px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.header-actions {
  display: flex !important;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  justify-content: flex-end;
}

.modern-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  border-radius: 50%;
  transition: all 0.2s ease;
  position: relative;
}

.modern-action-btn:hover {
  background-color: rgba(156, 163, 175, 0.1);
}

.modern-action-btn mat-icon {
  display: flex !important;
  align-items: center;
  justify-content: center;
  color: inherit;
}

/* Messages Container */
.messages-container {
  position: relative;
  background: #f3f4f6;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* Dark mode for messages container */
.dark .messages-container {
  background: #374151;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.modern-messages-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  max-width: 100%;
}

.modern-message-wrapper {
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
}

.message-row {
  display: flex;
  align-items: flex-end;
  width: 100%;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.message-row.sent {
  justify-content: flex-end;
}

.message-row.received {
  justify-content: flex-start;
}

.message-bubble {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  position: relative;
}

.message-text {
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 4px;
}

.message-time {
  font-size: 11px;
  opacity: 0.8;
}

.message-status {
  display: flex;
  align-items: center;
}

/* Modern Message Input */
.modern-input-container {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
  z-index: 10;
  border: none;
  background: #ffffff;
}

/* Dark mode for message input container */
.dark .modern-input-container {
  background: #1f2937;
}

.input-row {
  width: 100%;
  max-width: 100%;
  display: flex;
  align-items: flex-end;
  box-sizing: border-box;
}

.input-field-wrapper {
  min-width: 0;
  display: flex;
  align-items: center;
  position: relative;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: #f3f4f6;
}

/* Dark mode for input field wrapper */
.dark .input-field-wrapper {
  background: #374151;
}

.modern-message-input {
  width: 100%;
  max-width: 100%;
  font-family: inherit;
  word-wrap: break-word;
  overflow-wrap: break-word;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  color: #1f2937;
  background: transparent;
}

/* Dark mode for message input */
.dark .modern-message-input {
  color: #f9fafb;
}

.modern-message-input::placeholder {
  color: #9ca3af;
}

.dark .modern-message-input::placeholder {
  color: #6b7280;
}

.modern-message-input:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.modern-send-btn {
  border: none;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
  background: #3b82f6;
}

.modern-send-btn:hover:not(:disabled) {
  background: #2563eb;
}

.modern-send-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Typing Indicator */
.modern-typing-indicator {
  display: flex;
  align-items: flex-end;
  width: 100%;
  max-width: 100%;
}

.typing-bubble {
  display: flex;
  align-items: center;
  padding: 8px 12px;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

/* Responsive Design */
/* Mobile Optimizations */
@media (max-width: 768px) {
  .chat-detail-container {
    width: 100vw;
    max-width: 100vw;
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .modern-chat-header {
    padding: 8px 12px;
    min-height: 56px;
    margin-top: 61px;
    flex-shrink: 0;
  }

  .user-name {
    font-size: 15px;
  }

  .last-seen {
    font-size: 12px;
  }

  .avatar-container .avatar-circle {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .online-dot {
    width: 10px;
    height: 10px;
    border-width: 1.5px;
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
  }

  .modern-messages-list {
    padding: 8px 12px;
    width: 100%;
    max-width: 100%;
  }

  .modern-input-container {
    padding: 8px 12px;
    flex-shrink: 0;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }

  .input-row {
    width: 100%;
    max-width: 100%;
    gap: 8px;
  }

  .input-field-wrapper {
    flex: 1;
    min-width: 0;
    padding: 8px 12px;
  }

  .modern-message-input {
    width: 100%;
    max-width: 100%;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .message-bubble {
    max-width: 85%;
    word-wrap: break-word;
    overflow-wrap: break-word;
    padding: 8px 12px;
  }

  .message-text {
    font-size: 14px;
  }

  .message-time {
    font-size: 10px;
  }

  .modern-send-btn {
    width: 40px;
    height: 40px;
    padding: 8px;
  }

  /* Hide scrollbars but keep functionality */
  .messages-container::-webkit-scrollbar {
    display: none;
  }

  .messages-container {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Ensure proper touch scrolling */
  .messages-container {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

/* Tablet Optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .modern-chat-header {
    padding: 12px 20px;
    min-height: 64px;
  }

  .avatar-container .avatar-circle {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }

  .user-name {
    font-size: 17px;
  }

  .last-seen {
    font-size: 14px;
  }

  .modern-messages-list {
    padding: 16px 20px;
  }

  .modern-input-container {
    padding: 12px 20px;
  }

  .input-field-wrapper {
    padding: 10px 16px;
  }

  .modern-message-input {
    font-size: 15px;
  }

  .message-bubble {
    max-width: 70%;
    padding: 10px 16px;
  }

  .message-text {
    font-size: 15px;
  }

  .modern-send-btn {
    width: 44px;
    height: 44px;
  }
}

/* Desktop Optimizations */
@media (min-width: 1025px) {
  .modern-chat-header {
    padding: 16px 24px;
    min-height: 72px;
  }

  .avatar-container .avatar-circle {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .user-name {
    font-size: 18px;
  }

  .last-seen {
    font-size: 14px;
  }

  .modern-messages-list {
    padding: 20px 24px;
  }

  .modern-input-container {
    padding: 16px 24px;
  }

  .input-field-wrapper {
    padding: 12px 20px;
  }

  .modern-message-input {
    font-size: 16px;
  }

  .message-bubble {
    max-width: 60%;
    padding: 12px 20px;
  }

  .message-text {
    font-size: 16px;
  }

  .modern-send-btn {
    width: 48px;
    height: 48px;
  }
}

/* Animation keyframes */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Touch targets for mobile accessibility */
@media (max-width: 768px) {
  button, .touch-target {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .message-bubble {
    border: 1px solid currentColor;
  }

  .avatar-circle {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode specific adjustments */
.dark .chat-detail-container {
  background: #1f2937;
}

.dark .messages-container {
  background: #374151;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.dark .modern-input-container {
  background: #1f2937;
  border-color: #374151;
}

.dark .input-field-wrapper {
  background: #374151;
}

.dark .modern-message-input {
  color: #f9fafb;
}

.dark .modern-message-input::placeholder {
  color: #6b7280;
}

/* Enhanced dark mode message bubbles */
.dark .message-bubble {
  border-color: #4b5563;
}

.dark .received-bubble {
  background: #374151;
  border-color: #4b5563;
}

.dark .sent-bubble {
  background: #3b82f6;
}

/* Dark mode loading states */
.dark .loading-container,
.dark .error-container,
.dark .empty-container {
  color: #d1d5db;
}

/* Dark mode typing indicator */
.dark .typing-bubble {
  background: #374151;
  border-color: #4b5563;
}

/* Incoming Call Invitation Styles */
.incoming-call-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.incoming-call-container {
  background: #2d2d2d;
  border-radius: 20px;
  padding: 1.5rem;
  text-align: center;
  color: white;
  min-width: 280px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.incoming-call-header h3 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  color: #4CAF50;
}

.caller-info {
  margin-bottom: 1.5rem;
}

.caller-avatar {
  font-size: 3rem;
  color: #4CAF50;
  margin-bottom: 0.8rem;
}

.caller-name {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
}

.call-type {
  font-size: 0.9rem;
  color: #ccc;
}

.call-actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
}

.decline-btn,
.accept-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  padding: 0.8rem;
  border: none;
  border-radius: 50%;
  width: 70px;
  height: 70px;
  cursor: pointer;
  font-size: 1.3rem;
  transition: all 0.3s ease;
}

.decline-btn {
  background: #f44336;
  color: white;
}

.decline-btn:hover {
  background: #d32f2f;
  transform: scale(1.1);
}

.accept-btn {
  background: #4CAF50;
  color: white;
}

.accept-btn:hover {
  background: #45a049;
  transform: scale(1.1);
}

.decline-btn span,
.accept-btn span {
  font-size: 0.7rem;
  font-weight: 500;
}

/* Video Call Window Styles */
.video-call-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-call-container {
  width: 95vw;
  height: 95vh;
  max-width: 800px;
  max-height: 600px;
  background: linear-gradient(145deg, #1e1e1e 0%, #2a2a2a 100%);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-call-header {
  padding: 1rem;
  background: #2d2d2d;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.video-call-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.call-title {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.call-status-badge {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  color: #ccc;
}

.call-status-badge.connected {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.video-area {
  flex: 1;
  position: relative;
  background: #000;
}

.remote-video-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.remote-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.participant-name-badge {
  position: absolute;
  bottom: 0.8rem;
  left: 0.8rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-weight: 500;
  font-size: 0.8rem;
}

.connection-status {
  position: absolute;
  top: 0.8rem;
  right: 0.8rem;
  background: rgba(0, 0, 0, 0.8);
  color: #ccc;
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.connection-status.connected {
  color: #4CAF50;
  border-color: rgba(76, 175, 80, 0.3);
}

.connected-dot {
  color: #4CAF50;
  font-size: 0.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.waiting-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
}

.waiting-message i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.waiting-message p {
  font-size: 1rem;
  margin: 0;
}

.connected-state {
  width: 100%;
  height: 100%;
  position: relative;
}

.simulated-remote-video {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.video-call-avatar {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  transition: opacity 0.3s ease;
}

.video-simulation {
  text-align: center;
  color: white;
  padding: 1.5rem;
}

.avatar-large {
  font-size: 4rem;
  color: #4CAF50;
  margin-bottom: 1rem;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  border: 2px solid #4CAF50;
}

.video-info h4 {
  font-size: 1.2rem;
  margin: 0 0 0.3rem 0;
  color: #4CAF50;
}

.video-info p {
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
  color: white;
}

.video-info small {
  color: #ccc;
  font-style: italic;
  font-size: 0.8rem;
}

.connection-error-alert {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
  background: rgba(244, 67, 54, 0.95);
  border: 1px solid rgba(244, 67, 54, 0.4);
  border-radius: 16px;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  z-index: 15;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.3s ease-out;
  backdrop-filter: blur(10px);
}

.connection-error-alert i {
  font-size: 1.1rem;
  color: #ffcdd2;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.local-video-container {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 120px;
  height: 90px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #4CAF50;
  z-index: 999;
}

.local-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.local-video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  z-index: 1;
}

.local-label {
  position: absolute;
  bottom: 0.25rem;
  left: 0.25rem;
  color: white;
  background: rgba(0, 0, 0, 0.7);
  padding: 0.2rem 0.4rem;
  border-radius: 8px;
  font-size: 0.7rem;
}

.video-controls {
  padding: 1rem;
  background: #2d2d2d;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.controls-left,
.controls-right {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 0.8rem;
}

.controls-center {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.call-timer {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
}

.call-id-short {
  font-size: 0.7rem;
  color: #ccc;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.3rem 0.6rem;
  border-radius: 10px;
  font-family: monospace;
}

.control-btn {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  border: none;
  background: #4a4a4a;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: #5a5a5a;
  transform: scale(1.1);
}

.mic-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.mic-btn.muted {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  box-shadow: 0 3px 10px rgba(244, 67, 54, 0.3);
}

.camera-btn {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  box-shadow: 0 3px 10px rgba(33, 150, 243, 0.3);
}

.camera-btn.disabled {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  box-shadow: 0 3px 10px rgba(244, 67, 54, 0.3);
}

.end-call-btn {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  box-shadow: 0 3px 10px rgba(244, 67, 54, 0.4);
}

.end-call-btn:hover {
  background: #d32f2f;
}

/* Online Status Indicator */
.online-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  transition: background-color 0.3s ease;
}

.online-indicator.online {
  background-color: #4ade80;
  box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.3);
}

.online-indicator.offline {
  background-color: #9ca3af;
}
