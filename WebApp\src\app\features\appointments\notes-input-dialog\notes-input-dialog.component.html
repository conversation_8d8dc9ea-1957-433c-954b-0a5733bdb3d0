<div class="notes-dialog dark:bg-gray-800 dark:border dark:border-gray-700">
  <!-- Dialog Header -->
  <div class="dialog-header mb-4 ">
    <h2 class="text-lg font-semibold text-gray-900 dark:!text-gray-100">{{ data.title }}</h2>
  </div>

  <!-- Notes Input -->
  <div class="mb-6">
    <label for="notes" class="block text-sm font-medium text-gray-700 dark:!text-gray-300 mb-2">
      Notes <span class="text-red-500 dark:!text-red-400">*</span>
    </label>
    <textarea
      id="notes"
      [(ngModel)]="notes"
      [placeholder]="data.placeholder"
      rows="4"
      class="w-full px-3 py-2 border border-gray-300 dark:!border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none bg-white dark:!bg-gray-800 text-gray-900 dark:!text-gray-100"
      [class.border-red-500]="!isValid() && notes.length > 0"
      [class.dark:!border-red-400]="!isValid() && notes.length > 0"
    ></textarea>
    <div *ngIf="!isValid() && notes.length > 0" class="mt-1 text-sm text-red-600 dark:!text-red-400">
      Notes are required
    </div>
    <div class="mt-1 text-sm text-gray-500 dark:!text-gray-400">
      Please provide detailed notes for this action.
    </div>
  </div>

  <!-- Dialog Actions -->
  <div class="dialog-actions flex justify-end space-x-3">
    <button
      (click)="onCancel()"
      class="px-4 py-2 text-gray-600 dark:!text-gray-300 bg-gray-100 dark:!bg-gray-700 hover:bg-gray-200 dark:!hover:bg-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-300 dark:!focus:ring-gray-500 transition-colors"
    >
      Cancel
    </button>
    <button
      (click)="onConfirm()"
      [disabled]="!isValid()"
      [ngClass]="{
        'bg-blue-500 hover:bg-blue-600 dark:!bg-blue-600 dark:!hover:bg-blue-700 text-white': isValid(),
        'bg-gray-300 dark:!bg-gray-600 text-gray-500 dark:!text-gray-400 cursor-not-allowed': !isValid()
      }"
      class="px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 dark:!focus:ring-offset-gray-800 transition-colors"
    >
      {{ data.confirmButtonText }}
    </button>
  </div>
</div>
