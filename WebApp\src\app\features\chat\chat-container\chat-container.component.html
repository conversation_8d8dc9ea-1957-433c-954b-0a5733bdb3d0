<div class="flex w-full h-screen bg-gray-50 dark:bg-gray-900 overflow-auto font-sans">
  <!-- Mobile Chat List -->
  <div
    class="w-full md:w-96 md:min-w-80 md:max-w-lg bg-white dark:bg-gray-800 shadow-lg border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-300 lg:mt-0"
    [class.hidden]="!shouldShowList"
    [class.md:flex]="true"
    [class.mt-16]="true"
    [class.lg:mt-0]="true">

    <!-- Mobile-First Header -->
    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-700 text-white shadow-lg">
      <div class="flex justify-between items-center p-3">
        <div class="flex items-center gap-3">
          <div class="relative">
            <div class="w-10 h-10 md:w-11 md:h-11 rounded-full flex items-center justify-center bg-white/20 backdrop-blur-sm">
              <mat-icon class="text-white ">local_hospital</mat-icon>
            </div>
          </div>
          <div class="min-w-0 flex-1">
            <h1 class="text-lg md:text-xl font-bold text-white truncate">Patient Care</h1>
            <div class="flex items-center gap-2">
              <span class="text-xs md:text-sm text-white/80 font-normal">Messages</span>
              <!-- Connection Status -->
              <div class="flex items-center gap-1" [title]="getConnectionStatusTooltip()">
                <div class="w-1.5 h-1.5 md:w-2 md:h-2 rounded-full"
                  [class.bg-green-400]="connectionState === HubConnectionState.Connected"
                  [class.bg-yellow-400]="connectionState === HubConnectionState.Reconnecting"
                  [class.bg-red-400]="connectionState === HubConnectionState.Disconnected"
                  [class.animate-pulse]="connectionState === HubConnectionState.Reconnecting">
                </div>
                <span class="text-xs text-white/60 hidden sm:inline">
                  {{ getConnectionStatusText() }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Header Actions - Mobile Optimized -->
        <!-- <div class="flex gap-1 items-center">
          <button
            class="p-2 md:p-2.5 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 touch-manipulation"
            title="Search conversations"
            (click)="toggleSearch()">
            <mat-icon class="text-base md:text-lg text-white">search</mat-icon>
          </button>
        </div>  -->
      </div>

      <!-- Mobile Search Bar -->
      <div class="transition-all duration-300 overflow-hidden"
        [class]="showSearch ? 'max-h-20 opacity-100' : 'max-h-0 opacity-0'">
        <div class="p-3 md:p-4 pt-0">
          <div class="relative bg-white/10 rounded-2xl flex items-center px-3 md:px-4 py-2.5 md:py-3 gap-3 backdrop-blur-sm border border-white/20 focus-within:bg-white/20 transition-all duration-300">
            <mat-icon class="text-white/70 text-lg">search</mat-icon>
            <input
              type="text"
              placeholder="Search patients or messages..."
              class="flex-1 text-white bg-transparent outline-none text-sm font-medium placeholder-white/50 focus:placeholder-white/30 transition-colors duration-300"
              [(ngModel)]="searchQuery"
              (input)="onSearchInput($event)">
            <button
              *ngIf="searchQuery"
              class="text-white/70 hover:text-white p-1 rounded-full hover:bg-white/10 transition-all duration-200 touch-manipulation"
              (click)="clearSearch()">
              <mat-icon class="text-sm">close</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat List Content -->
    <div class="flex-1 overflow-y-auto bg-white dark:bg-gray-800">
      <app-chat-list
        #chatList
        [selectedDoctorId]="selectedDoctorId"
        [selectedPatientId]="selectedPatientId"
        (chatSelected)="onChatSelected($event)">
      </app-chat-list>
    </div>
  </div>

  <!-- Chat Detail Panel - Mobile Responsive -->
  <div class="flex-1 bg-gray-50 dark:bg-gray-900 flex flex-col relative overflow-hidden transition-all duration-300 lg:mt-0"
    [class.hidden]="!shouldShowDetail"
    [class.absolute]="isMobileView && shouldShowDetail"
    [class.inset-0]="isMobileView && shouldShowDetail"
    [class.z-20]="isMobileView && shouldShowDetail"
    [class.mt-16]="true"
    [class.lg:mt-0]="true">

    <!-- Welcome Screen - Mobile Optimized -->
    <div *ngIf="!showChatDetail"
      class="flex-1 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 relative overflow-hidden p-4 md:p-10">

      <!-- Floating Background Elements - Hidden on Mobile -->
      <div class="absolute inset-0 overflow-hidden hidden md:block">
        <div class="absolute top-20 left-20 w-16 h-16 bg-blue-200/30 dark:bg-blue-700/30 rounded-full animate-bounce" style="animation-delay: 0s;"></div>
        <div class="absolute top-40 right-32 w-12 h-12 bg-blue-300/30 dark:bg-blue-700/30 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
        <div class="absolute bottom-32 left-40 w-20 h-20 bg-blue-100/30 dark:bg-blue-700/30 rounded-full animate-bounce" style="animation-delay: 2s;"></div>
        <div class="absolute bottom-20 right-20 w-14 h-14 bg-blue-400/30 dark:bg-blue-700/30 rounded-full animate-bounce" style="animation-delay: 3s;"></div>
      </div>

      <!-- Welcome Content - Mobile First -->
      <div class="relative z-10 text-center max-w-sm md:max-w-2xl mx-auto w-full">
        <!-- Main Icon -->
        <div class="relative mb-6 md:mb-8">
          <div class="w-20 h-20 md:w-32 md:h-32 mx-auto relative">
            <div class="w-full h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-2xl relative overflow-hidden">
              <mat-icon class="text-3xl md:text-5xl text-white relative z-10">chat</mat-icon>
              <!-- Pulse rings - Hidden on small screens -->
              <div class="absolute inset-0 rounded-full border-4 border-blue-400/30 animate-ping hidden md:block"></div>
              <div class="absolute inset-0 rounded-full border-4 border-blue-400/20 animate-ping hidden md:block" style="animation-delay: 0.5s;"></div>
            </div>
          </div>
        </div>

        <!-- Welcome Text -->
        <div class="mb-8 md:mb-12">
          <h2 class="text-2xl md:text-4xl font-bold text-gray-900 dark:text-white mb-3 md:mb-4 tracking-tight">Welcome to Patient Care</h2>
          <p class="text-base md:text-xl text-gray-600 dark:text-gray-300 font-medium">Professional patient care communication</p>
        </div>

        <!-- Feature Cards - Responsive Grid -->
        <div class="grid gap-4 md:gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
          <div class="bg-white dark:bg-gray-800 backdrop-blur-sm rounded-xl md:rounded-2xl p-4 md:p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg md:rounded-xl flex items-center justify-center mb-3 md:mb-4 mx-auto">
              <mat-icon class="text-white text-base md:text-lg">security</mat-icon>
            </div>
            <h4 class="text-sm md:text-lg font-semibold text-gray-900 dark:text-white mb-2">Secure & HIPAA</h4>
            <p class="text-gray-600 dark:text-gray-300 text-xs md:text-sm leading-relaxed">Encrypted conversations</p>
          </div>

          <div class="bg-white dark:bg-gray-800 backdrop-blur-sm rounded-xl md:rounded-2xl p-4 md:p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg md:rounded-xl flex items-center justify-center mb-3 md:mb-4 mx-auto">
              <mat-icon class="text-white text-base md:text-lg">schedule</mat-icon>
            </div>
            <h4 class="text-sm md:text-lg font-semibold text-gray-900 dark:text-white mb-2">Real-time</h4>
            <p class="text-gray-600 dark:text-gray-300 text-xs md:text-sm leading-relaxed">Instant messaging</p>
          </div>

          <div class="bg-white dark:bg-gray-800 backdrop-blur-sm rounded-xl md:rounded-2xl p-4 md:p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 sm:col-span-2 md:col-span-1">
            <div class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg md:rounded-xl flex items-center justify-center mb-3 md:mb-4 mx-auto">
              <mat-icon class="text-white text-base md:text-lg">local_hospital</mat-icon>
            </div>
            <h4 class="text-sm md:text-lg font-semibold text-gray-900 dark:text-white mb-2">Professional Care</h4>
            <p class="text-gray-600 dark:text-gray-300 text-xs md:text-sm leading-relaxed">Streamlined communication</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Detail Content -->
    <div *ngIf="showChatDetail" class="flex-1 flex flex-col h-full">
      <app-chat-detail
        [doctorId]="selectedDoctorId!"
        [patientId]="selectedPatientId!"
        (backToList)="onBackToList()"
        (messagesMarkedAsRead)="onMessagesMarkedAsRead()"
        (messageSent)="onMessageSent($event)">
      </app-chat-detail>
    </div>
  </div>
</div>
